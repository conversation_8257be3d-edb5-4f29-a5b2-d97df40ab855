import os
import pandas as pd
import plotly.express as px
import dash
from dash import dcc, html, Input, Output, State
import dash_bootstrap_components as dbc
from datetime import datetime

# Try to import dash_extensions, but make it optional
try:
    from dash_extensions import Download
    from dash_extensions.snippets import send_data_frame
    DASH_EXTENSIONS_AVAILABLE = True
except ImportError:
    DASH_EXTENSIONS_AVAILABLE = False
    print("WARNING: dash_extensions not available. Download functionality will be disabled.")

# Load data with error handling
def load_data():
    """Load sales data with proper error handling"""
    data_path = "data/Sales_Data.csv"
    try:
        if not os.path.exists(data_path):
            raise FileNotFoundError(f"Data file not found: {data_path}")

        # Try different encodings
        for encoding in ['utf-8', 'latin1', 'cp1252']:
            try:
                df = pd.read_csv(data_path, encoding=encoding)
                print(f"Data loaded successfully with {encoding} encoding")
                return df
            except UnicodeDecodeError:
                continue

        raise ValueError("Could not decode the CSV file with any common encoding")

    except Exception as e:
        print(f"Error loading data: {e}")
        # Return empty DataFrame with expected columns for graceful degradation
        return pd.DataFrame(columns=['Order Date', 'Ship Date', 'Sales', 'Profit', 'Region', 'Sub-Category', 'Category', 'Segment', 'State', 'Order ID'])

df = load_data()

# Convert dates
df['Order Date'] = pd.to_datetime(df['Order Date'], dayfirst=True, errors='coerce')
df['Ship Date'] = pd.to_datetime(df['Ship Date'], dayfirst=True, errors='coerce')

# Drop rows with missing critical info
df.dropna(subset=['Sales', 'Profit', 'Region', 'Sub-Category', 'Order Date'], inplace=True)

# Convert state names to abbreviations for USA choropleth
us_state_abbrev = {
    'Alabama': 'AL', 'Alaska': 'AK', 'Arizona': 'AZ', 'Arkansas': 'AR',
    'California': 'CA', 'Colorado': 'CO', 'Connecticut': 'CT', 'Delaware': 'DE',
    'Florida': 'FL', 'Georgia': 'GA', 'Hawaii': 'HI', 'Idaho': 'ID',
    'Illinois': 'IL', 'Indiana': 'IN', 'Iowa': 'IA', 'Kansas': 'KS',
    'Kentucky': 'KY', 'Louisiana': 'LA', 'Maine': 'ME', 'Maryland': 'MD',
    'Massachusetts': 'MA', 'Michigan': 'MI', 'Minnesota': 'MN', 'Mississippi': 'MS',
    'Missouri': 'MO', 'Montana': 'MT', 'Nebraska': 'NE', 'Nevada': 'NV',
    'New Hampshire': 'NH', 'New Jersey': 'NJ', 'New Mexico': 'NM',
    'New York': 'NY', 'North Carolina': 'NC', 'North Dakota': 'ND',
    'Ohio': 'OH', 'Oklahoma': 'OK', 'Oregon': 'OR', 'Pennsylvania': 'PA',
    'Rhode Island': 'RI', 'South Carolina': 'SC', 'South Dakota': 'SD',
    'Tennessee': 'TN', 'Texas': 'TX', 'Utah': 'UT', 'Vermont': 'VT',
    'Virginia': 'VA', 'Washington': 'WA', 'West Virginia': 'WV',
    'Wisconsin': 'WI', 'Wyoming': 'WY'
}
df['State Abbrev'] = df['State'].map(us_state_abbrev)

# App setup
app = dash.Dash(__name__, external_stylesheets=[dbc.themes.BOOTSTRAP])
app.title = "Superstore Dashboard"

# Layout
app.layout = dbc.Container([
    html.H1("📊 Superstore Interactive Dashboard", className="text-center my-4"),

    dbc.Row([
        dbc.Col(dcc.DatePickerRange(
            id='date-range',
            start_date=df['Order Date'].min(),
            end_date=df['Order Date'].max(),
            display_format='DD/MM/YYYY',
        ), width=4),

        dbc.Col(dcc.Dropdown(
            id='category-filter',
            options=[{'label': cat, 'value': cat} for cat in df['Category'].unique()],
            placeholder="Filter by Category",
            clearable=True
        ), width=4),

        dbc.Col(dcc.Dropdown(
            id='segment-filter',
            options=[{'label': seg, 'value': seg} for seg in df['Segment'].unique()],
            placeholder="Filter by Segment",
            multi=True
        ), width=4),
    ], className='mb-4'),

    dbc.Row([
        dbc.Col(html.Div(id='total-sales'), width=4),
        dbc.Col(html.Div(id='total-profit'), width=4),
        dbc.Col(html.Div(id='total-orders'), width=4)
    ], className='mb-4 text-center'),

    dbc.Row([
        dbc.Col(dcc.Loading(dcc.Graph(id='sales-trend')), width=6),
        dbc.Col(dcc.Loading(dcc.Graph(id='profit-by-category')), width=6),
    ]),

    dbc.Row([
        dbc.Col(dcc.Loading(dcc.Graph(id='sales-map')), width=6),
        dbc.Col(dcc.Loading(dcc.Graph(id='region-pie')), width=6),
    ]),

    # Download section (only if dash_extensions is available)
    dbc.Row([
        dbc.Col([
            html.Button("Download CSV", id="btn-download", className="btn btn-primary") if DASH_EXTENSIONS_AVAILABLE
            else html.P("WARNING: Download functionality requires dash_extensions package", className="text-muted"),
            Download(id="download") if DASH_EXTENSIONS_AVAILABLE else html.Div()
        ], width="auto"),
    ], className='mt-4'),

], fluid=True)

# Callbacks
@app.callback(
    [Output('total-sales', 'children'),
     Output('total-profit', 'children'),
     Output('total-orders', 'children'),
     Output('sales-trend', 'figure'),
     Output('profit-by-category', 'figure'),
     Output('sales-map', 'figure'),
     Output('region-pie', 'figure')],
    [Input('date-range', 'start_date'),
     Input('date-range', 'end_date'),
     Input('category-filter', 'value'),
     Input('segment-filter', 'value')]
)
def update_dashboard(start_date, end_date, selected_category, selected_segments):
    filtered_df = df[(df['Order Date'] >= start_date) & (df['Order Date'] <= end_date)]

    if selected_category:
        filtered_df = filtered_df[filtered_df['Category'] == selected_category]
    if selected_segments:
        filtered_df = filtered_df[filtered_df['Segment'].isin(selected_segments)]

    total_sales = f"💰 Total Sales: ${filtered_df['Sales'].sum():,.2f}"
    total_profit = f"📈 Total Profit: ${filtered_df['Profit'].sum():,.2f}"
    total_orders = f"🛒 Total Orders: {filtered_df['Order ID'].nunique()}"

    # Sales trend
    trend = filtered_df.groupby('Order Date').agg({'Sales': 'sum'}).reset_index().sort_values("Order Date")
    sales_trend_fig = px.line(trend, x='Order Date', y='Sales', title='Sales Over Time')

    # Profit by Sub-Category
    profit_cat = filtered_df.groupby('Sub-Category')['Profit'].sum().sort_values().reset_index()
    profit_fig = px.bar(profit_cat, x='Profit', y='Sub-Category', orientation='h', title='Profit by Sub-Category')

    # Sales by State map
    state_sales = filtered_df.groupby('State Abbrev')['Sales'].sum().reset_index()
    map_fig = px.choropleth(
        state_sales,
        locations='State Abbrev',
        locationmode='USA-states',
        color='Sales',
        scope="usa",
        color_continuous_scale='Blues',
        title='Sales by State',
        height=600  # Increased height for clarity
    )
    map_fig.update_layout(margin={"r":0,"t":50,"l":0,"b":0})


    # Pie chart: sales by region
    region_data = filtered_df.groupby('Region')['Sales'].sum().reset_index()
    pie_fig = px.pie(region_data, names='Region', values='Sales', title='Sales Distribution by Region')

    return total_sales, total_profit, total_orders, sales_trend_fig, profit_fig, map_fig, pie_fig


# Download callback (only if dash_extensions is available)
if DASH_EXTENSIONS_AVAILABLE:
    @app.callback(
        Output("download", "data"),
        Input("btn-download", "n_clicks"),
        [State('date-range', 'start_date'),
         State('date-range', 'end_date'),
         State('category-filter', 'value'),
         State('segment-filter', 'value')],
        prevent_initial_call=True,
    )
    def download_filtered_data(_n_clicks, start_date, end_date, selected_category, selected_segments):
        filtered_df = df[(df['Order Date'] >= start_date) & (df['Order Date'] <= end_date)]
        if selected_category:
            filtered_df = filtered_df[filtered_df['Category'] == selected_category]
        if selected_segments:
            filtered_df = filtered_df[filtered_df['Segment'].isin(selected_segments)]

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return send_data_frame(filtered_df.to_csv, filename=f"filtered_sales_data_{timestamp}.csv")

# Run server
if __name__ == '__main__':
    app.run(debug=True)
