{"cells": [{"cell_type": "code", "execution_count": 1, "id": "c1d1f01f", "metadata": {}, "outputs": [], "source": ["from pyspark.sql import SparkSession\n", "\n", "spark = SparkSession.builder \\\n", "    .appName(\"YellowTaxiAnalysis\") \\\n", "    .getOrCreate()"]}, {"cell_type": "code", "execution_count": 10, "id": "75ed5231", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["root\n", " |-- VendorID: long (nullable = true)\n", " |-- tpep_pickup_datetime: timestamp_ntz (nullable = true)\n", " |-- tpep_dropoff_datetime: timestamp_ntz (nullable = true)\n", " |-- passenger_count: double (nullable = true)\n", " |-- trip_distance: double (nullable = true)\n", " |-- RatecodeID: double (nullable = true)\n", " |-- store_and_fwd_flag: string (nullable = true)\n", " |-- PULocationID: long (nullable = true)\n", " |-- DOLocationID: long (nullable = true)\n", " |-- payment_type: long (nullable = true)\n", " |-- fare_amount: double (nullable = true)\n", " |-- extra: double (nullable = true)\n", " |-- mta_tax: double (nullable = true)\n", " |-- tip_amount: double (nullable = true)\n", " |-- tolls_amount: double (nullable = true)\n", " |-- improvement_surcharge: double (nullable = true)\n", " |-- total_amount: double (nullable = true)\n", " |-- congestion_surcharge: double (nullable = true)\n", " |-- airport_fee: double (nullable = true)\n", "\n", "+--------+--------------------+---------------------+---------------+-------------+----------+------------------+------------+------------+------------+-----------+-----+-------+----------+------------+---------------------+------------+--------------------+-----------+\n", "|VendorID|tpep_pickup_datetime|tpep_dropoff_datetime|passenger_count|trip_distance|RatecodeID|store_and_fwd_flag|PULocationID|DOLocationID|payment_type|fare_amount|extra|mta_tax|tip_amount|tolls_amount|improvement_surcharge|total_amount|congestion_surcharge|airport_fee|\n", "+--------+--------------------+---------------------+---------------+-------------+----------+------------------+------------+------------+------------+-----------+-----+-------+----------+------------+---------------------+------------+--------------------+-----------+\n", "|       2| 2023-01-01 00:32:10|  2023-01-01 00:40:36|            1.0|         0.97|       1.0|                 N|         161|         141|           2|        9.3|  1.0|    0.5|       0.0|         0.0|                  1.0|        14.3|                 2.5|        0.0|\n", "|       2| 2023-01-01 00:55:08|  2023-01-01 01:01:27|            1.0|          1.1|       1.0|                 N|          43|         237|           1|        7.9|  1.0|    0.5|       4.0|         0.0|                  1.0|        16.9|                 2.5|        0.0|\n", "|       2| 2023-01-01 00:25:04|  2023-01-01 00:37:49|            1.0|         2.51|       1.0|                 N|          48|         238|           1|       14.9|  1.0|    0.5|      15.0|         0.0|                  1.0|        34.9|                 2.5|        0.0|\n", "|       1| 2023-01-01 00:03:48|  2023-01-01 00:13:25|            0.0|          1.9|       1.0|                 N|         138|           7|           1|       12.1| 7.25|    0.5|       0.0|         0.0|                  1.0|       20.85|                 0.0|       1.25|\n", "|       2| 2023-01-01 00:10:29|  2023-01-01 00:21:19|            1.0|         1.43|       1.0|                 N|         107|          79|           1|       11.4|  1.0|    0.5|      3.28|         0.0|                  1.0|       19.68|                 2.5|        0.0|\n", "+--------+--------------------+---------------------+---------------+-------------+----------+------------------+------------+------------+------------+-----------+-----+-------+----------+------------+---------------------+------------+--------------------+-----------+\n", "only showing top 5 rows\n"]}], "source": ["df = spark.read.parquet(r\"../data/yellow_taxi_trip_2023.parquet\")\n", "df.printSchema()\n", "df.show(5)"]}, {"cell_type": "code", "execution_count": 11, "id": "38d68389", "metadata": {}, "outputs": [], "source": ["df = df.dropna(subset=[\"trip_distance\", \"fare_amount\"])"]}, {"cell_type": "code", "execution_count": 12, "id": "7e9d2326", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["+-------+------------------+------------------+\n", "|summary|     trip_distance|       fare_amount|\n", "+-------+------------------+------------------+\n", "|  count|           3066766|           3066766|\n", "|   mean|3.8473420306601414| 18.36706861234247|\n", "| stddev|249.58375606858166|17.807821939337924|\n", "|    min|               0.0|            -900.0|\n", "|    max|         258928.15|            1160.1|\n", "+-------+------------------+------------------+\n", "\n", "+-------------+-----+\n", "|trip_distance|count|\n", "+-------------+-----+\n", "|          0.0|45862|\n", "|          1.0|43827|\n", "|          0.9|43473|\n", "|          1.1|42578|\n", "|          0.8|41801|\n", "+-------------+-----+\n", "only showing top 5 rows\n", "+---------------+------------------+\n", "|passenger_count|  avg(fare_amount)|\n", "+---------------+------------------+\n", "|           NULL|20.821606289114612|\n", "|            0.0|16.258301149245607|\n", "|            1.0|17.864870699568197|\n", "|            2.0|20.187779579036082|\n", "|            3.0| 19.66543200473928|\n", "|            4.0|20.900317797004483|\n", "|            5.0|17.897048569621038|\n", "|            6.0|18.010731403783193|\n", "|            7.0| 68.16666666666667|\n", "|            8.0| 82.11307692307693|\n", "|            9.0|              90.0|\n", "+---------------+------------------+\n", "\n"]}], "source": ["# Trip stats\n", "df.describe([\"trip_distance\", \"fare_amount\"]).show()\n", "\n", "# Top 5 most common trip distances\n", "df.groupBy(\"trip_distance\").count().orderBy(\"count\", ascending=False).show(5)\n", "\n", "# Average fare by passenger count\n", "df.groupBy(\"passenger_count\").avg(\"fare_amount\").orderBy(\"passenger_count\").show()\n"]}, {"cell_type": "code", "execution_count": 13, "id": "629d7301", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["+-----------+------+\n", "|pickup_hour| count|\n", "+-----------+------+\n", "|          0| 84969|\n", "|          1| 59799|\n", "|          2| 42040|\n", "|          3| 27438|\n", "|          4| 17835|\n", "|          5| 18011|\n", "|          6| 43860|\n", "|          7| 86877|\n", "|          8|116865|\n", "|          9|131111|\n", "|         10|143666|\n", "|         11|154157|\n", "|         12|169858|\n", "|         13|178739|\n", "|         14|191604|\n", "|         15|196424|\n", "|         16|195977|\n", "|         17|209493|\n", "|         18|215889|\n", "|         19|192801|\n", "+-----------+------+\n", "only showing top 20 rows\n"]}], "source": ["from pyspark.sql.functions import to_timestamp, hour\n", "\n", "df = df.withColumn(\"pickup_hour\", hour(to_timestamp(\"tpep_pickup_datetime\")))\n", "df.groupBy(\"pickup_hour\").count().orderBy(\"pickup_hour\").show()"]}, {"cell_type": "code", "execution_count": 14, "id": "39c7c23d", "metadata": {}, "outputs": [], "source": ["insights = df.groupBy(\"passenger_count\").avg(\"fare_amount\")\n", "insights.toPandas().to_csv(\"../output/insights.csv\", index=False)"]}, {"cell_type": "code", "execution_count": 18, "id": "f421470a-14b2-4ac6-a7be-5d5ba104ddbd", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_21400\\1373471520.py:10: FutureWarning: \n", "\n", "Passing `palette` without assigning `hue` is deprecated and will be removed in v0.14.0. Assign the `x` variable to `hue` and set `legend=False` for the same effect.\n", "\n", "  sns.barplot(x=\"pickup_hour\", y=\"count\", data=hourly_trips_pd, palette=\"viridis\")\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# Convert spark DataFrame to pandas\n", "hourly_trips = df.groupBy(\"pickup_hour\").count().orderBy(\"pickup_hour\")\n", "hourly_trips_pd = hourly_trips.toPandas()\n", "\n", "# Plot trip counts by hour\n", "plt.figure(figsize=(10, 6))\n", "sns.barplot(x=\"pickup_hour\", y=\"count\", data=hourly_trips_pd, palette=\"viridis\")\n", "plt.title(\"Number of Trips by Hour of Day\")\n", "plt.xlabel(\"Hour\")\n", "plt.ylabel(\"Trip Count\")\n", "plt.grid(True)\n", "plt.tight_layout()\n", "plt.savefig(\"../images/trips_by_hour.png\", dpi=300, bbox_inches='tight')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 17, "id": "22acd8ab", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["+---------------+------------------+\n", "|passenger_count|avg(trip_distance)|\n", "+---------------+------------------+\n", "|           NULL| 21.01115439833843|\n", "|            0.0|2.7619044640763186|\n", "|            1.0|3.3381694835055487|\n", "|            2.0|3.9310511454237163|\n", "|            3.0|  3.66439272987126|\n", "|            4.0|3.8125808912456707|\n", "|            5.0| 3.282478386167122|\n", "|            6.0| 3.250963234248319|\n", "|            7.0| 4.238333333333333|\n", "|            8.0| 4.270769230769231|\n", "|            9.0|               0.0|\n", "+---------------+------------------+\n", "\n", "+-------+------------------+\n", "|summary|       fare_amount|\n", "+-------+------------------+\n", "|  count|           3066766|\n", "|   mean| 18.36706861234247|\n", "| stddev|17.807821939337924|\n", "|    min|            -900.0|\n", "|    25%|               8.6|\n", "|    50%|              12.8|\n", "|    75%|              20.5|\n", "|    max|            1160.1|\n", "+-------+------------------+\n", "\n"]}], "source": ["# Average trip distance by passenger count\n", "df.groupBy(\"passenger_count\").avg(\"trip_distance\").orderBy(\"passenger_count\").show()\n", "\n", "# Fare distribution\n", "df.select(\"fare_amount\").summary().show()"]}, {"cell_type": "code", "execution_count": null, "id": "5e7c261c-49be-4b90-9f52-7cb16b4a0c82", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}