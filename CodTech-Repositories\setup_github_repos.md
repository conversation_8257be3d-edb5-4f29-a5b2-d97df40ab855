# 🚀 GitHub Repository Setup Guide

## 📋 Overview
This guide will help you push your 4 CodTech internship projects to separate GitHub repositories.

## 🗂️ Repository Structure Created
```
CodTech-Repositories/
├── codtech-bigdata-taxi-analysis/     # NYC Taxi Big Data Analysis
├── codtech-interactive-dashboard/     # Sales Dashboard with Dash
├── codtech-sentiment-analysis/        # NLP Sentiment Analysis
└── codtech-churn-prediction/          # Telco Customer Churn ML
```

## 🔧 Prerequisites
1. **GitHub Account**: Make sure you have a GitHub account
2. **Git Configured**: Ensure Git is configured with your credentials
3. **GitHub CLI (Optional)**: For easier repository creation

## ⚙️ Configure Git (If not done already)
```bash
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"
```

## 🚀 Step-by-Step GitHub Setup

### Method 1: Using GitHub Web Interface (Recommended)

#### For each repository, follow these steps:

1. **Go to GitHub.com** and sign in
2. **Click "New Repository"** (green button)
3. **Repository Settings:**
   - Repository name: `codtech-bigdata-taxi-analysis` (use exact names below)
   - Description: Add appropriate description
   - Set to **Public** (for portfolio visibility)
   - **DO NOT** initialize with README, .gitignore, or license (we already have these)
4. **Click "Create Repository"**
5. **Copy the repository URL** (HTTPS or SSH)

#### Repository Names and Descriptions:

**Repository 1:**
- **Name**: `codtech-bigdata-taxi-analysis`
- **Description**: "NYC Yellow Taxi Big Data Analysis using PySpark - CodTech Internship Project"

**Repository 2:**
- **Name**: `codtech-interactive-dashboard`
- **Description**: "Interactive Sales Dashboard with Dash and Plotly - CodTech Internship Project"

**Repository 3:**
- **Name**: `codtech-sentiment-analysis`
- **Description**: "NLP Sentiment Analysis with Machine Learning - CodTech Internship Project"

**Repository 4:**
- **Name**: `codtech-churn-prediction`
- **Description**: "Telco Customer Churn Prediction ML Pipeline - CodTech Internship Project"

### Method 2: Using GitHub CLI (Alternative)
```bash
# Install GitHub CLI first: https://cli.github.com/
gh repo create codtech-bigdata-taxi-analysis --public --description "NYC Yellow Taxi Big Data Analysis using PySpark"
gh repo create codtech-interactive-dashboard --public --description "Interactive Sales Dashboard with Dash and Plotly"
gh repo create codtech-sentiment-analysis --public --description "NLP Sentiment Analysis with Machine Learning"
gh repo create codtech-churn-prediction --public --description "Telco Customer Churn Prediction ML Pipeline"
```

## 📤 Push to GitHub

### Repository 1: Big Data Taxi Analysis
```bash
cd codtech-bigdata-taxi-analysis
git remote add origin https://github.com/YOUR_USERNAME/codtech-bigdata-taxi-analysis.git
git branch -M main
git push -u origin main
```

### Repository 2: Interactive Dashboard
```bash
cd ../codtech-interactive-dashboard
git remote add origin https://github.com/YOUR_USERNAME/codtech-interactive-dashboard.git
git branch -M main
git push -u origin main
```

### Repository 3: Sentiment Analysis
```bash
cd ../codtech-sentiment-analysis
git remote add origin https://github.com/YOUR_USERNAME/codtech-sentiment-analysis.git
git branch -M main
git push -u origin main
```

### Repository 4: Churn Prediction
```bash
cd ../codtech-churn-prediction
git remote add origin https://github.com/YOUR_USERNAME/codtech-churn-prediction.git
git branch -M main
git push -u origin main
```

## 🏷️ Add Topics/Tags (Optional but Recommended)
After pushing, go to each repository on GitHub and add relevant topics:

**Common Topics for All:**
- `codtech-internship`
- `data-science`
- `python`
- `machine-learning`

**Specific Topics:**
- **Big Data**: `pyspark`, `big-data`, `data-analysis`, `nyc-taxi`
- **Dashboard**: `dash`, `plotly`, `data-visualization`, `interactive-dashboard`
- **Sentiment**: `nlp`, `sentiment-analysis`, `text-processing`, `scikit-learn`
- **Churn**: `customer-churn`, `predictive-analytics`, `classification`, `random-forest`

## 📝 Next Steps After Pushing

1. **Update Repository Descriptions** on GitHub
2. **Add Repository Topics/Tags** for better discoverability
3. **Create Release Tags** for major versions
4. **Add Collaborators** if working in a team
5. **Enable GitHub Pages** for documentation (optional)
6. **Add Repository to your Portfolio/Resume**

## 🔗 Portfolio Links
After pushing, your repositories will be available at:
- https://github.com/YOUR_USERNAME/codtech-bigdata-taxi-analysis
- https://github.com/YOUR_USERNAME/codtech-interactive-dashboard
- https://github.com/YOUR_USERNAME/codtech-sentiment-analysis
- https://github.com/YOUR_USERNAME/codtech-churn-prediction

## 🛠️ Troubleshooting

### Common Issues:
1. **Authentication Error**: Use GitHub Personal Access Token instead of password
2. **Repository Already Exists**: Delete and recreate, or use different name
3. **Large Files**: Use Git LFS for files > 100MB
4. **Permission Denied**: Check SSH keys or use HTTPS with token

### Git Commands Reference:
```bash
# Check repository status
git status

# Add all changes
git add .

# Commit changes
git commit -m "Your commit message"

# Push changes
git push origin main

# Check remote repositories
git remote -v
```

## 🎉 Success!
Once all repositories are pushed, you'll have 4 professional GitHub repositories showcasing your CodTech internship projects!
