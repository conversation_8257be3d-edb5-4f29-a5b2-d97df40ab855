{"cells": [{"cell_type": "code", "execution_count": null, "id": "22170781", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["import pandas as pd\n", "import joblib\n", "\n", "from sklearn.feature_extraction.text import CountVectorizer\n", "from nltk.corpus import stopwords\n", "from nltk.stem import PorterStemmer\n", "import nltk\n", "import string\n", "\n", "nltk.download('punkt')\n", "nltk.download('stopwords')"]}, {"cell_type": "code", "execution_count": null, "id": "3c9a7227", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": []}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}