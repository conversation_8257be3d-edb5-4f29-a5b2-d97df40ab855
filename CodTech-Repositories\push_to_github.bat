@echo off
echo.
echo ========================================
echo 🚀 CodTech GitHub Repository Setup
echo ========================================
echo.

set /p username="Enter your GitHub username: "

if "%username%"=="" (
    echo ❌ GitHub username is required!
    pause
    exit /b 1
)

echo.
echo 📋 Setting up repositories for user: %username%
echo.
echo IMPORTANT: Make sure you have created these repositories on GitHub:
echo - codtech-bigdata-taxi-analysis
echo - codtech-interactive-dashboard  
echo - codtech-sentiment-analysis
echo - codtech-churn-prediction
echo.

set /p continue="Have you created all repositories on GitHub? (y/n): "
if /i not "%continue%"=="y" (
    echo ❌ Please create the repositories on GitHub first.
    pause
    exit /b 1
)

echo.
echo 🚀 Starting GitHub push process...
echo.

REM Repository 1: Big Data Analysis
echo 🔄 Processing: codtech-bigdata-taxi-analysis
cd codtech-bigdata-taxi-analysis
git remote add origin https://github.com/%username%/codtech-bigdata-taxi-analysis.git 2>nul
git remote set-url origin https://github.com/%username%/codtech-bigdata-taxi-analysis.git
git branch -M main
git push -u origin main
if %errorlevel% equ 0 (
    echo ✅ Successfully pushed codtech-bigdata-taxi-analysis
) else (
    echo ❌ Failed to push codtech-bigdata-taxi-analysis
)
cd ..

REM Repository 2: Interactive Dashboard
echo.
echo 🔄 Processing: codtech-interactive-dashboard
cd codtech-interactive-dashboard
git remote add origin https://github.com/%username%/codtech-interactive-dashboard.git 2>nul
git remote set-url origin https://github.com/%username%/codtech-interactive-dashboard.git
git branch -M main
git push -u origin main
if %errorlevel% equ 0 (
    echo ✅ Successfully pushed codtech-interactive-dashboard
) else (
    echo ❌ Failed to push codtech-interactive-dashboard
)
cd ..

REM Repository 3: Sentiment Analysis
echo.
echo 🔄 Processing: codtech-sentiment-analysis
cd codtech-sentiment-analysis
git remote add origin https://github.com/%username%/codtech-sentiment-analysis.git 2>nul
git remote set-url origin https://github.com/%username%/codtech-sentiment-analysis.git
git branch -M main
git push -u origin main
if %errorlevel% equ 0 (
    echo ✅ Successfully pushed codtech-sentiment-analysis
) else (
    echo ❌ Failed to push codtech-sentiment-analysis
)
cd ..

REM Repository 4: Churn Prediction
echo.
echo 🔄 Processing: codtech-churn-prediction
cd codtech-churn-prediction
git remote add origin https://github.com/%username%/codtech-churn-prediction.git 2>nul
git remote set-url origin https://github.com/%username%/codtech-churn-prediction.git
git branch -M main
git push -u origin main
if %errorlevel% equ 0 (
    echo ✅ Successfully pushed codtech-churn-prediction
) else (
    echo ❌ Failed to push codtech-churn-prediction
)
cd ..

echo.
echo 🎉 GitHub push process completed!
echo.
echo 📋 Your repositories are now available at:
echo 🔗 https://github.com/%username%/codtech-bigdata-taxi-analysis
echo 🔗 https://github.com/%username%/codtech-interactive-dashboard
echo 🔗 https://github.com/%username%/codtech-sentiment-analysis
echo 🔗 https://github.com/%username%/codtech-churn-prediction
echo.
echo ✨ Your CodTech internship portfolio is now live on GitHub! ✨
echo.
pause
