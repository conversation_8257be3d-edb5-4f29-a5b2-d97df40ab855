{"cells": [{"cell_type": "code", "execution_count": 33, "id": "4aa69241", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Accuracy: 0.7853589196872779\n", "\n", "Classification Report:\n", "               precision    recall  f1-score   support\n", "\n", "           0       0.83      0.90      0.86      1033\n", "           1       0.63      0.48      0.54       374\n", "\n", "    accuracy                           0.79      1407\n", "   macro avg       0.73      0.69      0.70      1407\n", "weighted avg       0.77      0.79      0.78      1407\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20392\\2121513742.py:58: FutureWarning: \n", "\n", "Passing `palette` without assigning `hue` is deprecated and will be removed in v0.14.0. Assign the `y` variable to `hue` and set `legend=False` for the same effect.\n", "\n", "  sns.barplot(x=top_features, y=top_features.index, palette=\"viridis\")\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA98AAAJOCAYAAACuvrTiAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAPYQAAD2EBqD+naQAAm+5JREFUeJzt3Qm8jPX///+Xfd93smYXshYVsmRpQYuK7EuhJIl8yp4lkfZSypakJK0SQqgshRQJEYWI7GWd/+35/v2v+c4cZz/nOofjcb/dJmdmrrmua64ZOs/r9Xq/r1SBQCBgAAAAAADAN6n9WzUAAAAAABDCNwAAAAAAPiN8AwAAAADgM8I3AAAAAAA+I3wDAAAAAOAzwjcAAAAAAD4jfAMAAAAA4DPCNwAAAAAAPiN8AwAAAADgM8I3AAAAAAA+I3wDAHCZSpUqVaxuS5cu9X1fXn31VbvrrrusWLFibpudOnWKdLmpU6dGuZ/79u2LcTsNGjSwq666yi5Ve/bssWHDhtn69et939bJkyfdtmL7+Wu5qD6be+65x5d93LRpk9vHnTt3+rJ+AEhMaRN1bQAA4JIxY8aMsPvTp0+3hQsXXvB4hQoVfN+Xp59+2o4dO2a1a9e2vXv3xrj8iBEjrGTJkmGP5cyZ01I6he/hw4dbiRIl7Oqrr/Y9fGtb3kmL2OrTp4/VqlUr7DHtr1/hW/uo/fNrGwCQWAjfAABcpu67776w+999950L3xEfTwrLli0LVr2zZs0a4/LNmze3mjVr2uXi7Nmzdv78ebsU3HDDDXbnnXfapezEiROWJUuW5N4NACkMbecAACDaEPLoo49a0aJFLUOGDFauXDkbP368BQKBsOUUmh988EGbOXOmWyZjxoxWo0YN+/rrr2O1neLFi7t1xIUq5efOnbOE8vb9/ffft4oVK1qmTJmsTp06tnHjRvf8pEmTrHTp0u49qcIascXZa2X//vvvrW7duu71qsq/9tprF2xr//791rVrVytQoIBbX9WqVW3atGlhy2j92icd5+eee86uvPJKd+xfeeWVYEW5c+fOwZZuteLL8uXLg637Wl6f2SOPPGL//vtv2PrV0q8THH/++ae1atXK/ZwvXz7r379/8HhqH/SYqLLsbUst3gm1atUqa9asmeXIkcMyZ85s9evXt5UrV4Yt8/vvv1uvXr3cd0nHM0+ePO69hR57vW89JjfeeOMFwySi2l9VyEOHNXhDGXQCSNvMnz+/XXHFFcHn58+f704oKIxny5bNbr75Zvv555/D1qkhD/pM9Dod+0KFClnLli1phwcQhso3AACIlAL2bbfdZkuWLHGBUW3OCxYssMcee8wFt4kTJ4Ytr/Aye/Zs13bshUWFrNWrVyf6OGuFrePHj1v69OmtadOmNmHCBCtTpky816fg+vHHH1vv3r3d/TFjxtgtt9xiAwYMcO9Doeyff/6xcePGWZcuXeyrr74Ke72ea9GihbVp08buvfdee++996xnz55u/7S8KAQrqG/bts2FfQV0BX4FwcOHD9vDDz8cts4pU6bYf//9Zz169HDHs3Xr1u6Ew5AhQ9xjCoSiwC9al1rFtV2FVR33F1980f744w/3XCiFbB23a665xoX8RYsWuWOooK/XK3hrHL5+1nZvv/1297oqVarEeCy1j3///XfYY7lz57bUqVO746auBZ2YGTp0qHtM77Nhw4buM9CwA1mzZo198803bqy4Aq1CrPZHx0+t5grt9erVc9+1F154wf73v/8Fh0fEd5iEPmO9bx1fnXQSDcHo2LGjO1YaGqHjq/24/vrrbd26dcFW9zvuuMMF8oceesg9ppMs6iLZtWsX7fAA/k8AAAAgEAj07t1b5ezg/Xnz5rn7Tz31VNhyd955ZyBVqlSBbdu2BR/TcrqtXbs2+Njvv/8eyJgxY6B169Zx2o8sWbIEOnbsGOlzs2fPDnTq1Ckwbdq0wIcffhh48sknA5kzZw7kzZs3sGvXrhjXXb9+/UClSpXCHtN+Z8iQIbBjx47gY5MmTXKPFyxYMHD06NHg44MGDXKPhy6rdeqxCRMmBB87depU4Oqrrw7kz58/cPr0affYc88955Z7++23g8vpuTp16gSyZs0a3I7WreWyZ88e2L9/f9i+rlmzxj03ZcqUC97byZMnL3hszJgx7rPSZ+HRsdU6RowYEbZstWrVAjVq1AjeP3DggFtu6NChgdhYsmRJ8HsQ8ab3dP78+UCZMmUCTZs2dT+H7nfJkiUDTZo0ifa9fPvtt25d06dPDz72/vvvu8e07Yii2vfixYuHfb90LLXs9ddfHzh79mzw8WPHjgVy5swZ6N69e9jr9+3bF8iRI0fw8X/++ce9/plnnonVcQJw+aLtHAAAROrzzz+3NGnSuOpiKLWhK9uoHTeUWrVV0fSo/Vmtt6qWJ0Z7uKiyrEpphw4dXMv0yJEj3foPHjxoo0aNivd6GzVqFFahVEXYq2iq1Tji47/99lvY69OmTWv3339/8L4q3rqvCqja0b3jWbBgQVcZ96RLl84dX1Xx1TkQStv2Wr9jQ+3ZHlVuVX1WVVyflaq0ET3wwANh91VJj/i+4kOVY1V9Q29635qhfevWrda2bVv3eWn/dNO+6vhriII3rj30vZw5c8Ytr9Z/Tar3ww8/mB+6d+/uvu8e7bc6EvR5efuqm5bR90AdId6+6vNWu7s6IAAgKrSdAwCASGncbeHChcPCZ2hbr54PFVnbd9myZV2r7oEDB1wA84NagBWG1DodXzpREErjkUXjpiN7PGLI0nGKOEGX3ruoZfraa691x0vHSK3WsTmeEWdzj4lanBV81T4fcf+OHDkSdl/jzSMG+1y5ciVKeKxcubI1btz4gscVvEVt3FHRfmo/1KKv1n+daNEQh9A5BiK+l8QS8Xh7+6uW+Mhkz57d/akhAWpJ10kpjeXXZ60hCzpB5Nd3HsClifANAAAueQrJW7ZsiffrQyuesXk84oRzfgit/sZEnQVNmjSxQ4cO2cCBA618+fLuZICCq8aUR5wpPar35SdvH5555pkoL5PmzXSvsdMK3n379nUdFTrp4V0vPKGzvkfVhRHxeHvb0bjvyEK0uh082s9bb73V5s2b5zoxBg8e7E4eaIx7tWrVErS/AFIOwjcAAIhyBnJVkzWBVmj1+5dffgk+H1mlMNSvv/7qJseKS/t0fKhd2u9txHT97YiXp9J7F6+dXcfrxx9/dKEutPod1fGMTFQzwmtmdm1PM6er4hraOh1fcZ19PiaazM2rGEdWGQ81Z84cVyHXJHAeTT6nNvDY7qMq6BGXP336dKyuIx+6v5r9PKb99ZZX9Vs3/V3QCQbt/9tvvx2r7QFI+RjzDQAAIqXZu1UlfOmll8Ie1yznCj2atTrUt99+GzYed/fu3fbRRx/ZTTfdlGiVVrWvR6Sx1BpXrZnVk/M63LokWWjI032dEPDGwet46pJUmhE+9HWakVwVX11yKyZeuI8YKr3jG1qR18/PP/98vN+TTppEtq340nFQQNXs6hrjHt1nq/cTsbtAxyli1Tqq4yHaVsRL3b3++uuxnn9AM5zrRMHo0aPduPOo9lfDKnRiIOK2dcLq1KlTsdoWgMsDlW8AABAptdHqkl5PPPGEG7esa1J/+eWXLlCrzdarDHp0OTEFltBLjXnXiY7JJ598Yhs2bHA/K+ioQvzUU0+5+7rcmXeJK00gpjbemjVrulZkhf233nrLtZ3rclPJRWO+Ne5Xx0ljvRWwNcGYwp4mVRNdHkyBXG3gOlmgirgqvLrGta7nHXFsfWR0zDXpmK4hruUVPjXeXW3mek7X6laruULjBx98kKAx3GrD1nXP9V70nnS5MH3G8b1snKr9kydPdidtKlWq5K6LXaRIEbe/mrxM+6zvgWjMtNq99RlrH3RiR10YuoRaKFWXFdR17DUWXN87jdFWtbpbt25uUjlNXKeWfH2/1BKeN2/eWO2v9keXFWvfvr1Vr17dtbzrZIrG1n/22Wd23XXXuRNT6jjQhHGaDFD7qnb0Dz/80P766y/3GgAISu7p1gEAwMV5qTHvckuPPPJIoHDhwoF06dK5S0Xpkkqhl4oSvU6v12W0tIwu3aVLV0V2CajIeJe/iuwWelmtJ554wl3CS5d60v4UK1Ys0LNnT3f5p9iI6lJj2vdQ3uW+Il4+yrucli5xFXGdusyaLhumy6vpclYvvfTSBdv/66+/Ap07d3aXRkufPn2gcuXKF1w2LKptez766KNAxYoVA2nTpg07Pps2bQo0btzYXbZM69elsDZs2HDBMdSx1uXcItJluSJ+/t988427/Jj2NabLjkV2bCKzbt26wO233x7IkyeP+57oWLVp0yawePHi4DK6fJd3nPR+dHmyX3755YLLhMkbb7wRKFWqVCBNmjRhlx07d+5cYODAgW4duhyd1qHL40V1qTFdxi2q96XX6junz/bKK690l7vzLqv3999/u+9P+fLl3XHVctdcc03gvffei/Y4ALj8pNJ//i+KAwAAxJ3a0Hv37n1Bi/rloEGDBu4SVD/99FNy7woA4CLGmG8AAAAAAHxG+AYAAAAAwGeEbwAAAAAAfMaYbwAAAAAAfEblGwAAAAAAnxG+AQAAAADwWVq/NwAg5Tp//rzt2bPHsmXL5i4zBAAAAKQUgUDAjh07ZoULF7bUqRNetyZ8A4g3Be+iRYsm924AAAAAvtm9e7ddccUVCV4P4RtAvKni7f2DlD179uTeHQAAACDRHD161BWavN95E4rwDSDevFZzBW/CNwAAAFKiVIk0vJIJ1wAAAAAA8BmVbwAJ1rpqT0ubOn1y7wYAAABSkAXbp1hKQuUbAAAAAACfEb4BAAAAAPAZ4RsAAAAAAJ8RvgEAAAAA8BnhGwAAAAAAnxG+AQAAAADwGeEbAAAAAACfEb6BWEqVKpXNmzcvTq+ZOnWq5cyZ07d9AgAAAHBpIHzjkgzB0d2GDRsW5Wt37tzpllm/fn2i7MuSJUusRYsWlidPHsucObNVrFjRHn30Ufvzzz8TZf0AAAAAUgbCNy45e/fuDd6ee+45y549e9hj/fv3T5L9mDRpkjVu3NgKFixoH3zwgW3atMlee+01O3LkiE2YMMHXbZ85c8bX9QMAAABIXIRvXHIUdr1bjhw5XCXbu58/f3579tln7YorrrAMGTLY1VdfbV988UXwtSVLlnR/VqtWzb2uQYMG7v6aNWusSZMmljdvXrfO+vXr2w8//BDlPvzxxx/Wp08fd3vrrbfcekqUKGH16tWzyZMn25AhQ8KWX7BggVWoUMGyZs1qzZo1cycJPLHZtvb11Vdftdtuu82yZMlio0aNco8/9dRT7j1ny5bNunXrZo8//rh7z6G0P9p2xowZrXz58vbKK68Enzt9+rQ9+OCDVqhQIfd88eLFbcyYMfH8ZAAAAABEhfCNFOX55593Vefx48fbjz/+aE2bNnWBdevWre751atXuz8XLVrkAvDcuXPd/WPHjlnHjh1txYoV9t1331mZMmVcO7kej8z777/vguuAAQMifT50nPfJkyfd/syYMcO+/vpr27VrV1h1PrbbVjt969atbePGjdalSxebOXOmC+FPP/20ff/991asWDEX0ENpGZ0I0HKbN2+20aNH2+DBg23atGnu+RdeeME+/vhje++992zLli1ueZ1EiMqpU6fs6NGjYTcAAAAAMUsbi2WAS4ZC7sCBA+2ee+5x9xVMNS5b7ekvv/yy5cuXzz2uMdqqlHsaNmwYtp7XX3/dBehly5bZLbfccsF2FObV7q6KcWxaxNWOfuWVV7r7qjSPGDEizttu27atde7cOXj/xRdftK5duwYfU8j+8ssv7fjx48Flhg4d6k5G3H777cHKv9rj1TKvwK8TAQr7119/vauuq/IdHVXFhw8fHuN7BgAAABCOyjdSDFVh9+zZY9ddd13Y47qvqm90/vrrL+vevbsLomr9VrBWiFU4jUwgEHBhNTY0EZsXvEWBff/+/XHeds2aNcPuq1Jdu3btsMdC7584ccK2b9/uArra3b2bWtX1uHTq1MlNPleuXDnXQq/wHp1Bgwa5Me3ebffu3bE6BgAAAMDljso3YOaqwAcPHnRt66r+arx4nTp1XGt5ZMqWLevCp1rXY6p+p0uXLuy+QrvCe1y3rbHeceFVwN944w275pprwp5LkyaN+7N69eq2Y8cOmz9/vmvFb9OmjZtEbs6cOZGuU/umGwAAAIC4ofKNFEMV48KFC9vKlSvDHtd9XQJM0qdP7/48d+7cBcuo8qux1pUqVXIB8++//45yW3feeadb17hx4yJ9/vDhw7He77hu26NqtSZrCxV6v0CBAu54/Pbbb1a6dOmwmzfxnHfc7r77bhfSZ8+e7WZuP3ToUKz3HwAAAEDMqHwjRXnsscfcOGe1eWvW7ylTpri2ak0kJpoZPFOmTG4GdM2Irhm+1eqtlm9NiKbWbrWvaz1aLipFixa1iRMnuvHbWr5Dhw5uojLNgj59+nTX3h3by43Fdduehx56yLWr63V169Z1wVmTzJUqVSq4jMZnK9jrPWqWdU2YtnbtWvvnn3+sX79+bmZ4Ve41+3vq1KndRHIaCx86YRwAAACAhKPyjRRFQVOh8tFHH7XKlSu7kK3ZvBVwJW3atG6Gb004pqpwy5Yt3eNvvvmmC6Rqw27fvr1bj4J6dHr16uXGSP/5559uFnJdxkuX+1IlOS7XGo/PtqVdu3ZuDLa25bWPawy3Tih4tD+61JhOQuh46DJmU6dODVa+dYkyVe8V4GvVqmU7d+60zz//3AVxAAAAAIknVSB08CmAS5quF67KtSrpSUGVelXVG5Zoa2lT/7+WfgAAACAxLNg+JVm37/2uq7meVGBLKNrOgUuUrh+uS5jpWuaaQG3WrFlu0rSFCxcm964BAAAAiIDwDVyiNGu6WsRHjRpl//33n5uATZOlabZyAAAAABcXwjdwidKkbKp0AwAAALj4MasSAAAAAAA+I3wDAAAAAOAzwjcAAAAAAD4jfAMAAAAA4DMmXAOQYB9ueDVRrn0IAAAApFRUvgEAAAAA8BnhGwAAAAAAnxG+AQAAAADwGeEbAAAAAACfEb4BAAAAAPAZs50DSLA7Gw6ydGkzJPduAAAAXBQ+++7Z5N4FXISofAMAAAAA4DPCNwAAAAAAPiN8AwAAAADgM8I3AAAAAAA+I3wDAAAAAOAzwjcAAAAAAD4jfAMAAAAA4DPCNy4LqVKlsnnz5kX5/NKlS90yhw8fTtTtTp061XLmzJmo6wQAAABw6SF8w1edOnVyofaBBx644LnevXu757RMYhk2bJhdffXVllSWLFliLVq0sDx58ljmzJmtYsWK9uijj9qff/6ZZPsAAAAA4OJH+IbvihYtau+++679+++/wcf+++8/e+edd6xYsWJ2qZo0aZI1btzYChYsaB988IFt2rTJXnvtNTty5IhNmDDB122fOXPG1/UDAAAASFyEb/iuevXqLoDPnTs3+Jh+VvCuVq1a8LFTp05Znz59LH/+/JYxY0a7/vrrbc2aNRe0hi9evNhq1qzpKs1169a1LVu2BFu8hw8fbhs2bHDL6abHPH///be1bt3ava5MmTL28ccfR7q/J06csOzZs9ucOXPCHlfbepYsWezYsWP2xx9/uH3V7a233rIGDRpYiRIlrF69ejZ58mQbMmRI2GsXLFhgFSpUsKxZs1qzZs1s7969wef0Hps0aWJ58+a1HDlyWP369e2HH34Ie73ey6uvvmq33Xab24dRo0a5x5966il3vLJly2bdunWzxx9//ILKv/ZH29YxLV++vL3yyivB506fPm0PPvigFSpUyD1fvHhxGzNmTIyfKQAAAIC4IXwjSXTp0sWmTJkSvK/A2rlz57BlBgwY4CrI06ZNc+GzdOnS1rRpUzt06FDYck888YSrLK9du9bSpk3r1i133323a/muVKmSC7e66TGPgnmbNm3sxx9/dK3i7dq1u2DdonB7zz33hO2v6P6dd97pgu7777/vgqv2OTKh47xPnjxp48ePtxkzZtjXX39tu3btsv79+wefV5jv2LGjrVixwr777jt3YkD7p8cjttTr5MHGjRvde545c6YL4U8//bR9//337mSGAnooLaMTAVpu8+bNNnr0aBs8eLA7xvLCCy+4kxDvvfeeO4mh5XUSAQAAAEDiSpvI6wMidd9999mgQYPs999/d/dXrlzpWtFVzfaqzQqOqlQ3b97cPfbGG2/YwoUL7c0337THHnssuC4FSVWHRZXem2++2bWxZ8qUyVWWFcjVCh6Rxpbfe++97meFUAXP1atXu0p0RKoiq6quAK+q8P79++3zzz+3RYsWuee3bt3qquN6LjYt4mpHv/LKK919VZpHjBgRfL5hw4Zhy7/++usuvC9btsxuueWW4ONt27YNO2Hx4osvWteuXYOPKWR/+eWXdvz48eAyQ4cOdScqbr/9dne/ZMmSrj1eLfMK/DoRoLCvLgNV11X5jo66E3TzHD16NMb3DwAAAIDKN5JIvnz5XEhWuFYFWT+rzdqzfft2F1Kvu+664GPp0qWz2rVru4ptqCpVqgR/9sKvwnFMQl+n6rbCc1Sv03ZVQfcqxG+//bYLpmorl0Ag4MJqbKjN3Qve3j6Hbvevv/6y7t27uxCstnPtlwK0gnEotdqHUqVa+xlxvz06oaHjqoCukxLeTa3qetw7IbF+/XorV66ca6FXeI+OWtK1j95NwwkAAAAAxIzKN5KMWqVV9ZWXX3453utRKPd4Afj8+fNxep332uhep+q39lPVdZ0wUIXZ217ZsmXdxGpeZTyu21V496gCffDgQXv++eddwM+QIYPVqVPHtbWH0gmDuPAq4OoguOaaa8KeS5MmTXA8/o4dO2z+/Pmuqq+2fE0iF3G8u0fdC/369QurfBPAAQAAgJhR+UaSUXu3AqUq3BrLHUqV4fTp07t2dI+W02RkunxXbGkd586dS7RWebXJqz1drdoKyR6N/da2xo0bF+lr43K9cL1nVZ01zlvVdoVvTQ4XE1WrQyekk9D7BQoUsMKFC9tvv/3mxs+H3tR+7lGlXWPjFdJnz57txt1HNhZetG9aPvQGAAAAIGZUvpFkVG31Wsi9ymtoVbdnz55ubHfu3Lnd5GEKtpqsTG3TsaXJwlTJVSv1FVdc4SZHU2CMj1y5crmx0tqnm266ya3Po2rvxIkTXSVf1d8OHTq4bWsW9OnTp7v27thebkzt5pqMTW3lWpe2p/HrMXnooYdcu7pep/HpCs6aTK5UqVJhk8wp2KtFXCc/NF5bE9X9888/roL97LPPusq9Zp1PnTq1m0hO4+VDJ4wDAAAAkHBUvpGkoquWjh071u644w5r3769a4fetm2bu0SXQnBs6fUKmTfeeKMbZz5r1qwE7a+Cv6r13ozqoXr16uXGSP/5559uFnJdxkut6np/obOZx0QTyikM6z3rvXuXW4uJZmtXG7i25bWPawy3Lhnm0f7oUmNqm69cubKbqE7j7r3Kt05O6CSHAnytWrVs586dbmI5BXEAAAAAiSdVIHTwKYAwqkg/8sgjtmfPHtdmfrHT9cJVudZ+JwVV6lVVb1Kjl6VLG78OAwAAgJTms++eTe5dQCL+rqu5nhJjuCVt50Ak1O6uydRUjb///vsvyuCtfdQlzDR+Xm38qvJr0jRdng0AAADAxYXeUiASasVWG7mqyGrtvhhp1nS1iOvyZzVq1LBPPvnETZam2coBAAAAXFxoOwcQb7SdAwAAXIi285ThaCK3nVP5BgAAAADAZ4RvAAAAAAB8RvgGAAAAAMBnhG8AAAAAAHzGpcYAJNicr8YkyiQUAAAAQEpF5RsAAAAAAJ8RvgEAAAAA8BnhGwAAAAAAnxG+AQAAAADwGeEbAAAAAACfEb4BAAAAAPAZlxoDkGB33jHK0qXLkNy7AQAAkGQ++3xEcu8CLjFUvgEAAAAA8BnhGwAAAAAAnxG+AQAAAADwGeEbAAAAAACfEb4BAAAAAPAZ4RsAAAAAAJ8RvgEAAAAA8BnhGwAAAAAAnxG+gSTSoEED69u3b3LvBgAAAIBkQPgGLjOBQMDOnj2b3LsBAAAAXFYI30AS6NSpky1btsyef/55S5Uqlbvt3LnTfvrpJ2vevLllzZrVChQoYO3bt7e///47rFrep08fGzBggOXOndsKFixow4YNCz6vdWhd69evDz52+PBh99jSpUvdff2p+/Pnz7caNWpYhgwZbMWKFXb+/HkbM2aMlSxZ0jJlymRVq1a1OXPmJPGRAQAAAC4PhG8gCSh016lTx7p372579+51t2zZslnDhg2tWrVqtnbtWvviiy/sr7/+sjZt2oS9dtq0aZYlSxZbtWqVjRs3zkaMGGELFy6M8z48/vjjNnbsWNu8ebNVqVLFBe/p06fba6+9Zj///LM98sgjdt9997mTBFE5deqUHT16NOwGAAAAIGZpY7EMgATKkSOHpU+f3jJnzuyq1/LUU0+54D169Ojgcm+99ZYVLVrUfv31Vytbtqx7TEF56NCh7ucyZcrYSy+9ZIsXL7YmTZrEaR8U2r3XKERru4sWLXInBaRUqVKuIj5p0iSrX79+pOtQYB8+fHg8jwIAAABw+SJ8A8lkw4YNtmTJEtdyHtH27dvDwneoQoUK2f79++O8vZo1awZ/3rZtm508efKCAH/69Gl3QiAqgwYNsn79+gXvq/KtkwUAAAAAokf4BpLJ8ePH7dZbb7Wnn376gucUsD3p0qULe07jtzVeW1KnTh2cRM1z5syZSLen1vXQbctnn31mRYoUCVtOY8Kjoueiex4AAABA5AjfQBJR2/m5c+eC96tXr24ffPCBlShRwtKmjd9fxXz58rk/NYbcq1iHTr4WlYoVK7oQvWvXrihbzAEAAAAkHsI3kEQUsjVpmmYoV6t579697Y033rB77703OJu52sHfffddmzx5sqVJkybGdWqW8muvvdZNpKZZy9WO/uSTT8b4Ok321r9/fzfJmqro119/vR05csRWrlxp2bNnt44dOybSuwYAAAAgzHYOJBGFXQVqVZ1Vsdb4aoVdVcNvuukmq1y5svXt29dy5swZbCePDU3Sput26zJier0mcouNkSNH2uDBg90kahUqVLBmzZq5NnSFeAAAAACJK1UgdLAoAMSBJlzTTO5NGg+wdOkYCw4AAC4fn30+Irl3AUn0u646RNUdmlBUvgEAAAAA8BnhGwAAAAAAnxG+AQAAAADwGeEbAAAAAACfEb4BAAAAAPAZ4RsAAAAAAJ8RvgEAAAAA8FlavzcAIOWb88ETiXLtQwAAACClovINAAAAAIDPCN8AAAAAAPiM8A0AAAAAgM8I3wAAAAAA+IzwDQAAAACAzwjfAAAAAAD4jEuNAUiwVp3GWtp0GZN7N3AR+3L2kOTeBQAAgGRF5RsAAAAAAJ8RvgEAAAAA8BnhGwAAAAAAnxG+AQAAAADwGeEbAAAAAACfEb4BAAAAAPAZ4RsAAAAAAJ8RvnFRGTZsmF199dUX3X506tTJWrVqZZe6pUuXWqpUqezw4cPJvSsAAADAZYXwHU/xCWMKPfPmzbOLPYgdOHDAevbsacWKFbMMGTJYwYIFrWnTprZy5Urf96l///62ePFiSwpTp0517z/ibfLkyUm6H35p0KCB9e3bN+yxunXr2t69ey1HjhzJtl8AAADA5Shtcu8A4u7MmTOWLl0639Z/xx132OnTp23atGlWqlQp++uvv1wQPXjwYLzXqfWlT58+xuWyZs3qbkkle/bstmXLlrDHFEwzZcrk+34EAgE7d+6cpU2bdH8N9RnoZAoAAACApEXlOxGrjH369LEBAwZY7ty5XcBR67KnRIkS7s/WrVu76qp3Xz766COrXr26ZcyY0YXd4cOH29mzZ4PPa/lXX33VbrvtNsuSJYuNGjUq2BY9Y8YMty4FxnvuuceOHTsWfN358+dtzJgxVrJkSRcmq1atanPmzHHP7dy502688Ub3c65cudw2VM1XFXz58uX29NNPu+eLFy9utWvXtkGDBrnte7Rct27dLF++fC7ANmzY0DZs2BB83ts/VZG1fb23119/3QoXLuz2K1TLli2tS5cuYa8L9dZbb1mlSpVcFb5QoUL24IMPxno/YqL3rc8q9KZjFVX7uz4bb1sPPPCAO6kQm+Md2mkwf/58q1Gjhns/K1asiHS/Nm7c6N6L1pMnTx7r0aOHHT9+/ILOi6j2R88vW7bMnn/++WBFX595ZN0O6mjQ9zdz5szuu6Auh3/++SfWxxAAAABAzAjfiUiVYoXjVatW2bhx42zEiBG2cOFC99yaNWvcn1OmTHFtv959Bd0OHTrYww8/bJs2bbJJkya5dmgF7FAKgwruCmVeUN2+fbtrY//000/dTWFr7NixwdcoCE6fPt1ee+01+/nnn+2RRx6x++67zy1XtGhR++CDD9xyqvxqnxTUvMqz1nvq1Kko3+tdd91l+/fvd0Hy+++/dycPGjVqZIcOHQous23bNreNuXPn2vr1691rVD1fsmRJcBkt/8UXX1i7du0i3Y5OOvTu3duFT733jz/+2EqXLh2n/Ugsqv5v3rzZBdhZs2a596XwG5vjHerxxx93n5PWVaVKlQu2c+LECReAFYT1PXn//fdt0aJFYScdYtoffZZ16tSx7t27u89WN33mEelz0fGqWLGiffvtt+5kwK233uoq8pHRd+Lo0aNhNwAAAAAxo+08ESlIDR061P1cpkwZe+mll1xAatKkiatOSs6cOcPafhWWFMY6duzo7qvyPXLkSFdB99Ylbdu2tc6dO4dtT5VWBfVs2bK5++3bt3fbU3BXSBo9erQLbQph3roVrhTw69ev7yr0kj9/frdfHq1ToU0hUmFWy6qq7gVFrWP16tUu9Kp6K+PHj3eBXZVeBWVRFVZh1Hvv0rx5c3vnnXdc4BMtnzdv3mAVPqKnnnrKHn30UXdywlOrVq047Ud0jhw5EtZerp/37dsXZcu2qvCqEKsSr5Mrjz32mPu8NBQgpuPt0ev0nYiKjs9///3njp1O5oi+SwrF6kgoUKBAjPujTgg9r+eiazPXSaKaNWvaK6+8EnxM64qKTjCEnnAAAAAAEDuE70QUsYqpFmkFw+ioRVptv6GVblUdFb5OnjzpwpMoIEWkdnMveEfcnqrOen3EkKdAXK1atRjHfN98882uKv/dd9+5qrJCmlrI1c6sfVYLtNqhQ/3777+uGu9Ry3po8BZVuBXsFfYUmGfOnOmCferUFzZh6L3s2bMnGNQjO3ax2Y/o6Pj98MMPwfuR7YdHbeTe5yEK2dr+7t273Z+xPd6RfZahVM3WtrzgLdddd5072aIuBS98R7c/Ovax4XUkxJaGH/Tr1y94X5XvyCrqAAAAAMIRvhNRxEnQNLY24vjmiBSWVEm8/fbbL3hO46Q9oUEsNtvzxgd/9tlnVqRIkbDlvCpxdLRtBUndBg8e7MZVqxKv8K11K+ir3Tmi0Ap6ZPus6q0mGtN+qYKtgD9x4sRI90HjnaMT2/2IjsJ2aBt7fMXleEd2XJJLTMc4Ir2X2Hx/AAAAAIQjfCchheWIY2nV1q1qZmIEwFAaw6uQtGvXrrCW51De7ONRje+NuD7vMmnaZ7Vma5bu0InjYkOhXicaVPFWdb5cuXJufVFVpbV+tdJH1paekP2ID1XaVVX3Aqu6AtSmrsqvWvhjOt6xVaFCBdf6r7HfXlBXd4ROFOh4xWZ/vM83ps9W3Ro6vrSSAwAAAP4ifCchL0iqhVhBTRNqDRkyxG655RZ3Te0777zTBSyFqp9++smNd44vBVddq1qTfqkafv3117vxzQpxmhlbY8zVmqxquSZra9GihQtxGiuuNmRN6qZgpvWsXbvWtZ1rVnJp3Lixa3HWbNt6vGzZsq49XFVfTQoXU1u1Ws/1njUpmSYki44mmtMs3hqXrvHims1d7+Ghhx5K8H7ElVrIu3btak8++aSbOVydAJoETZ9ZbI53bOn4aN16jd6/rruu96sx/V7LeUz7433fNPmfnlMo98b4R2wjr1y5svXq1csdZwV2TYin74DG4gMAAABIHMx2noQmTJjgZj9XZdIbB6xZrRV+v/zyS9eGfe2117o27NiO2Y2OJt5Sy7gmyVI1tVmzZi6Y6lJYovZob8I3hToFN4W0a665xu1DvXr17KqrrnLr0DhtTfolCuyff/65e16TwCn0atz277//HhYOo6JLaCkIquKvieSiowD63HPPuTHimghMoX3r1q2Jsh9xpbHnmkhP27v77rvdpddCLycX0/GOLY3jXrBggZuxXd8JnZTRtr3jH9v90cmANGnSuK4Fjb1XVT4iHTN993TCR5eU08kMXfouKa89DgAAAFwOUgU0ABfAJcW7Jrs3FCC5aMI1zax+Y+tBljbd/81RAET05ewhyb0LAAAA8fpdVx2t6mZNKCrfAAAAAAD4jPCNFEtt6mqjj+ymCd8AAAAAIKkwsBMplsaDnzlzJtLn/BgTnpQ0GzoAAACASwfhGylWYkxaBwAAAACJgbZzAAAAAAB8RvgGAAAAAMBntJ0DSLB5Ux9PlMsvAAAAACkVlW8AAAAAAHxG+AYAAAAAwGeEbwAAAAAAfEb4BgAAAADAZ4RvAAAAAAB8RvgGAAAAAMBnXGoMQII17/20pU2fMbl3Az5Y9ubg5N4FAACAFIHKNwAAAAAAPiN8AwAAAADgM8I3AAAAAAA+I3wDAAAAAOAzwjcAAAAAAD4jfAMAAAAA4DPCNwAAAAAAPiN8p1AlSpSw5557LtHX26lTJ2vVqlWirS9VqlQ2b948u1Q1aNDA+vbt68u6ly5d6o7P4cOHzS9Tp061nDlz+rZ+AAAAABdB+FaQU7jQLX369Fa6dGkbMWKEnT171i51UYUahTW937Fjx17w3M033+yeGzZsWIK3czEI/XxDb82aNfNtm0kd5ufOnWsjR45Msu0BAAAAuDQle+VbQWzv3r22detWe/TRR13wfOaZZywlK1q0qAvNof78809bvHixFSpUyFIS7/MNvc2aNStZ9+n06dOJtq7cuXNbtmzZEm19AAAAAFKmZA/fGTJksIIFC1rx4sWtZ8+e1rhxY/v444/dc88++6xVrlzZsmTJ4gJrr1697Pjx4+65EydOWPbs2W3OnDlh61PVU8sfO3bMdu7c6Sqh7733nt1www2WKVMmq1Wrlv3666+2Zs0aq1mzpmXNmtWaN29uBw4cCFvP5MmTrUKFCpYxY0YrX768vfLKK8HnvPWq6nnjjTda5syZrWrVqvbtt98G24U7d+5sR44cCVZ7Q6vZt9xyi/3999+2cuXK4GPTpk2zm266yfLnzx+2H6dOnbL+/ftbkSJF3Pu65ppr3Ppjs52TJ09aly5dXDgsVqyYvf7662Hr3rhxozVs2NAdlzx58liPHj2Cx1fOnTtn/fr1c5V1PT9gwAALBALx+nxDb7ly5Ypy+d27d1ubNm3cNhVsW7Zs6Y53qLfeessqVark1q2TFQ8++GCw1V5at27tjoV3X8fk6quvdp9pyZIl3Wcqu3btcuvXd0DfJW33r7/+Cm7He92MGTPcunLkyGH33HOP+25F1Xauz2vgwIHu+6r9UzfHm2++GeX7jc3y33//vfuu6ntWt25d27JlS9jzH330kVWvXt29r1KlStnw4cPDukfUtn7//fdbgQIF3DJXXXWVffrpp5Huj/4eaFs6hto3AAAAACkkfEekIOhVJlOnTm0vvPCC/fzzzy6cfvXVVy4AioKogtCUKVPCXq/7d955Z1g1cujQofbkk0/aDz/8YGnTprW2bdu69Tz//PO2fPly27Ztmw0ZMiS4/MyZM939UaNG2ebNm2306NE2ePBgtw+hnnjiCReM169fb2XLlrV7773XhR4FJI23VqDzqr1azqMW+3bt2oXtuyrhCsoRKVgq1L/77rv2448/2l133eWqyeoUiGk7EyZMcEFq3bp17sSFTm54wU0nL5o2beqCsE5EvP/++7Zo0aJgkPVer/1S2F2xYoUdOnTIPvzwQ/PLmTNn3D7ps9PnopMTCsZ6v9534tVXX7XevXu7EwU6eaATNQqsovchOq46Ft590Wf8wQcfuBMm+rzOnz/vgrfe07Jly2zhwoX222+/2d133x22T9u3b3cndBRWddOykQ0Z8HTo0MFV9vW91Xdn0qRJ7j0kZHl9z/RZrF271n1/Q78nOk5ax8MPP2ybNm1yr9dnpu+u6H3q5JKO5dtvv+2W0f6nSZMm0hMfOkmlcK6TWjoZAAAAACBxpLWLhCqqartesGCBPfTQQ+6x0IqiKo9PPfWUPfDAA8EqdLdu3VwAVdBSBXT//v32+eefuxAZSoFUoU4UUhSSta3rrrvOPda1a9ewNnCFdYWd22+/3d1XtdQLNh07dgxbr8Zpi6qNqsYq5KlSriqpqq+q9EZGAUpBRycAVNlU9VoV8dDKtSqzCpL6s3DhwsFtfvHFF+5xnRSIbjstWrRwoVtUXZ04caItWbLEypUrZ++88479999/Nn36dHciQ1566SW79dZb7emnn3ZVUgX7QYMGBY/Da6+95j6fuFBgjRgm//e//7lbRLNnz3ZhURVqvSfR+1QVXFV+dQboO6DhCfocPepmkHz58rk/tXzE46HwrvfqLaOwrfC+Y8cOV3UWPa/PUKHdW6f2R98N72RO+/bt3XfHC7eh1FGhLgutWx0cokp0VGK7vLZVv3599/Pjjz/uvnP67FTF1vdOj3nfS71eY9B1cknfY/1dWL16tQv2OkEU1TZ0UqZJkyau4q3P3Tv+EakaHloRP3r0aJTvDwAAAMBFFL69cKaqp4KOqtJeAFVwGDNmjP3yyy/ul3xVlRU61E6tFtzatWu7sKSKtAKIKntqX69Xr17YNqpUqRL8WaFS1M4e+piCu1cRVrVTgbx79+7BZbRtBd2o1uuN1dZ6FL5jojb1MmXKuAqjArFCnaqaoRQO1frthSaPwo/awGMSun9eQPfep8KY9sEL3qKTEfoMFMQU7HRSQ23uHu2fKulxaT1XW76q1aHUTh6ZDRs2uJMXEcdQ6zPXZ6J937NnjzVq1MjiSt8LL3h771+h2wveUrFiRRfc9ZwXvnXSJ3R/vJM8kVFFXRVlLyjHJLbLR/U901ACHTNVtUNPBug74/090TauuOKKC75Dof799193Ikh/92KaIV9/HxX4AQAAAFxi4dsLZ2rFVnXXC6Aa56tKsFqlFSwU2NT6rFCsKqbCt1f9fvnll134VpVUY6AjVu3SpUsX/Nl7LuJjCp3ijXl+4403woKnRGzVjWy93npiQ9Vv7buq6qpORqR90TZVGY+47ehamSPbP28f47J/iUHh3msLj4neb40aNVzbf0QKzhqGkJD9iI+4HEMNmYiL2C4f3fdMx0xh2OtOCKUTKLHZhtrLVXnXibDHHnvMzS8QFXVCaB4Aj06KhZ7AAAAAAHCRjvn2wpmqeKGVXwVOBQy1f1977bWucqeqZ0T33Xef/f77727MrEJsaFt4fKgKrpMAGv+r/Qq9qf08tnQyQRXI6KjSqOq2xtiq6hpRtWrV3DpU5Yy4L15bdWy2ExlNJqeqqSr9HlVQFXDVlq4qv6qsq1atCqv+63PxiyYN01h2TToX8f1qf1SBViVabd/RBdXYHA+9f41x1s2j748mJ4vss4gNdVPoO6tx4X4sH9UxU6dCxOOlmz5LVc3/+OMP1+IeFS2nSeV04kMnwyL7exYa1DXHQOgNAAAAwCUQvqOi8KBW9BdffNEFYYUDjTmOSBOGqeqnip3GBKvFNqFUSVR7rQK9QosCsqrqmn09thQSVZVUUNTM5moBjmzf1dodVZjUCQdNzKYJtTRRmMYnq0Kuffvss89ivZ3IaL2qjOpkxU8//eRa3zXWXu3vXmu+xlVrci5NOKbWf40fVziNC7XI79u3L+ym/Yxqn/LmzesmQtNEYnq/Guvdp08fFyBFQxJ0QkafjYK6JtHTd8TjhXNt559//olyv1TpVfjVNrUOHVcdZ7WAq7U+PrRtHU91NOiYefuvcd2JsXxkNDGgxqrrO6uJCdUyr8n5NMGg6P1oGMYdd9zhxpZrG/Pnz3fzBoRSZ4U6DjQUQTPg6/gBAAAAuAzCt0KAwq4m/1JlWMFAoTMyXit6ZLOFx4da2TXplwK3ApoCjCbdikvlWxPBaXI4zZ6tlulx48ZFupzGGEfXEq19UCjUJGOqSLdq1cpNCKZOgbhsJyK17WvyNM32rfHNmiFeY6k16ZpH21QYV0CsU6eOqzxrQq64UMhTBT30dv3110e5T19//bV7bzqhouq0PluNX/YqrNoXjUvWpHsa76+hCQrhHgVzhUy1QqtzICpq39YlunQCROFUYVwTkWnSt4TQEAodS52o0Nh/zRsQ2l2Q0OUj0kSCahf/8ssv3eeoLhFNrKcx7h7N8q7nNNGgqvqajC2y7gB1nmjmdR1XBfCoxrYDAAAAiLtUgbheuPkipKr4I4884tpl1YYNIGlozLeGBNS973+WNv3/u346UpZlbw5O7l0AAABI1t91dWWqxBhumewTriWEWqzVtq3W6Pvvv5/gDQAAAAC4KF20beexoRZrtepq8jHNwoyko2uPa8b1qG56HgAAAACQgtrOkfQ087kuBxcVTSYW8brlSHloO0/5aDsHAACXq6O0neNioGAd2+t3AwAAAMDl7pJuOwcAAAAA4FJA+AYAAAAAwGeEbwAAAAAAfMaYbwAJNv/lgYkyCQUAAACQUlH5BgAAAADAZ4RvAAAAAAB8RvgGAAAAAMBnhG8AAAAAAHxG+AYAAAAAwGeEbwAAAAAAfMalxgAkWKMBT1va9BmTezcue9++MDi5dwEAAABRoPINAAAAAIDPCN8AAAAAAPiM8A0AAAAAgM8I3wAAAAAA+IzwDQAAAACAzwjfAAAAAAD4jPANAAAAAIDPCN/w1c6dOy1VqlS2fv16d3/p0qXu/uHDh+1y0aBBA+vbt29y7wYAAACAZET4RpR2795tXbp0scKFC1v69OmtePHi9vDDD9vBgwfjvc66deva3r17LUeOHIm6r8uWLbOGDRta7ty5LXPmzFamTBnr2LGjnT592pLb3LlzbeTIkcH7JUqUsOeeey5R1v3rr7+69/vOO++EPX7+/Hl3rO+8885E2Q4AAACAhCF8I1K//fab1axZ07Zu3WqzZs2ybdu22WuvvWaLFy+2OnXq2KFDh+K1XoX4ggULuup3Ytm0aZM1a9bM7e/XX39tGzdutBdffNFt69y5c5ZcvOCvEwLZsmXzZRtly5a1sWPH2kMPPeROangmTJjgPkN9ZgAAAACSH+Ebkerdu7cLr19++aXVr1/fihUrZs2bN7dFixbZn3/+aU888USwijt69GhXIVfA1HKvv/56lOuN2HY+depUy5kzpy1YsMAqVKhgWbNmdUE6NEjK5MmT3fMZM2a08uXL2yuvvBJ8TvuoQD9u3Di76qqr7Morr3TreOONNyxTpkzB5VasWGE33HCDe6xo0aLWp08fO3HiRPD5U6dO2cCBA91zGTJksNKlS9ubb74Ztp+h5s2bF3YSYdiwYXb11Ve7fS1ZsqTb14ht5/r5999/t0ceecS9VjftQ/bs2W3OnDkXrD9Llix27NixaD8rBe+qVata9+7d3f1ffvnFhgwZ4j6HvHnzRnvsdILgwQcftEKFCrnn1d0wZsyYaLcHAAAAIO4I37iAqtoKw7169QoLr6KQ265dO5s9e7YFAoFglVVV53Xr1rnX9OzZ07Zs2RLr7Z08edLGjx9vM2bMcJXrXbt2Wf/+/YPPz5w504XJUaNG2ebNm13YHzx4sE2bNi24Twrrem1Utm/f7gL5HXfcYT/++KPbf4VxBU9Phw4dXJX/hRdecNuZNGmSOxkQF+oQ+OCDD1yruTfOPZQev+KKK2zEiBFun3VTwL7nnntsypQpYcvqvtrGY6qaK8Br2eXLl7sTDp06dXLru+2222I8dnqvH3/8sb333nvuM9PyOqESFZ2gOHr0aNgNAAAAQMzSxmIZXGbUaq5grWppZPT4P//8YwcOHHD3W7Ro4UK3qHI8ceJEW7JkiZUrVy5W2ztz5oxrj1bFWhSIFU49Q4cOdQH/9ttvd/dVVVarucKxxnXfdddd7mSBKvQK4tdee601atTIhWlVlEXVXJ008CrQGhOu4KnXvPrqqy7wK4AuXLjQGjdu7JYpVapUnI+dKsnTp0+3fPnyRfq8WtDTpEnjArX21dOtW7fgeHhVoffv32+ff/656zSIDVWsNY5c61G4VzdAbI6d3reOxfXXX+9CvNYTHR3H4cOHx+GIAAAAABAq34iSV9mOSZUqVYI/K8ApVCo8xpYmDPOCt3jhU9SSrap1165dXRXauz311FPucVGYVeX3jz/+cK3nRYoUcRXeSpUqBdvXN2zY4FrHQ9fRtGlTNzHZjh07XJVa61EYTwiF16iCd3Rq167t9terSL/99ttuXfXq1Yv1Ojp37uyOndrQddIhNsdOVXK9d50oURu+F9qjMmjQIDty5Ejwpkn5AAAAAMSMyjcuoLHOCtFqU27duvUFz+vxXLlyBUNmunTpwp7XaxVqYyuy13vB//jx4+5PtVNfc801YcspLIdS6G7fvr27aXZxTUamiroqtVrP/fff7wJmRBqnrnbx6KROnfqCkxGq2EekFvL4UtX65Zdftscff9ydTFCYjuvEdGnTpnW32B676tWru5MP8+fPd1X2Nm3auMp/xPHnHo2F1w0AAABA3BC+cYE8efJYkyZN3MRcmhgsdNz3vn373LhgtXQn5ozlUSlQoIC71Jlm7lbbeGzp5ICqwN6EagqZarfWiYXIVK5c2Z0w0CXLvLbzUDrRoInPtD4vYEc2pjs2opqF/b777rMBAwa4dnjtq9rCk+LYqUp+9913u5vGmGtsvMb9q0UeAAAAQOIgfCNSL730khuDrNZstSlrrPDPP/9sjz32mKswawKvpKLKtSrWuja4gqEm/Vq7dq0bd96vXz83fllBWFV6ta//999/bty19leXHPPGomssuMaTq8KsAK2AqzHeeq+aZExhV7O2K/xq9nDNSq72d1WDVTlWe/z//vc/ty+rVq1ybezxoW1pcjhNiqYqsmYk904YaGy2jvFNN93kxm77feyeffZZd5KiWrVqrrr//vvvu2EDEWd2BwAAAJAwjPlGpDQJl0KaJh1T+FSo7dGjh91444327bffJmlVVGFZl8tSK7Yq1BqXreCrEwLeeGm1WD/wwANu3LSe/+6779ylurwx3BqXrqr2r7/+6i43prCpWcBVGfZo4jVVfjV5nC7JpUt3eZVzvV+Nw9YkaNoHzYquS4vFhyaT27lzpzumEceHa3y2Jm3TSYCkOHaa+E3j5DVbfa1atdx+6T0qiAMAAABIPKkCsZ1VC4DvdLk1tfrv2bPHtadf7HSpMVXVa97/P0ub/v9d1xzJ59sXBif3LgAAAKQYR///33U10bB3FaWEoO0cuAjoWueamX3s2LFuYrhLIXgDAAAAiD16S4GLgFq/1equ8da6nFcoXTYt9FJhobfmzZsn2z4DAAAAiD3azoGLnGYe1y0ymoleE+AlF9rOLy60nQMAACQe2s6By4wme+OyXwAAAMCljbZzAAAAAAB8RvgGAAAAAMBnhG8AAAAAAHzGmG8ACbZ43MBEmYQCAAAASKmofAMAAAAA4DPCNwAAAAAAPiN8AwAAAADgM8I3AAAAAAA+I3wDAAAAAOAzZjsHkGD1h421NBkyJvduXNbWjhmS3LsAAACAaFD5BgAAAADAZ4RvAAAAAAB8RvgGAAAAAMBnhG8AAAAAAHxG+AYAAAAAwGeEbwAAAAAAfEb4BgAAAADAZ4RvAAAAAAB8RvhGtPbt22cPPfSQlSpVyjJkyGBFixa1W2+91RYvXpyo22nQoIH17dvXksLSpUstVapUdvjw4Vgtr2Wjuw0bNsz3fQYAAABwaUub3DuAi9fOnTvtuuuus5w5c9ozzzxjlStXtjNnztiCBQusd+/e9ssvvyTp/gQCATt37pylTZu0X9u9e/cGf549e7YNGTLEtmzZEnwsa9asltKcPn3a0qdPn9y7AQAAAKQYVL4RpV69ernK7urVq+2OO+6wsmXLWqVKlaxfv3723XffBZfbtWuXtWzZ0oXQ7NmzW5s2beyvv/4KPq/K8NVXX20zZsywEiVKWI4cOeyee+6xY8eOuec7depky5Yts+effz5YTVbw9yrU8+fPtxo1arjK+4oVK2z79u1uewUKFHDbrFWrli1atChs30+dOmUDBw50lXq9rnTp0vbmm2+69d54441umVy5crn1a/vRKViwYPCmfddr9HOmTJmsSJEiwZMQ58+ft9y5c9u1114bfO3bb7/t9sGzceNGa9iwoXttnjx5rEePHnb8+PEoTzZov8ePHx/2+Pr1690+bNu2zd1XBb9bt26WL18+d/y1/g0bNgSXj83x0ucycuRI69Chg1uH9gsAAABA4iF8I1KHDh2yL774wlW4s2TJcsHzqoZ7gVPBTssrQC9cuNB+++03u/vuu8OWVwCcN2+effrpp+6mZceOHeueU+iuU6eOde/e3VWZdQsNrI8//rhbdvPmzValShUXVlu0aOFa39etW2fNmjVzrfA6CeBRiJw1a5a98MIL7nWTJk1ywVPr/eCDD9wyql5rW9p+fCiI66SCThJ4wVqhWPvkBWq9z/r167ufT5w4YU2bNnWhf82aNfb++++7EPzggw9Gun6tq0uXLjZlypSwx3W/Xr16LpjLXXfdZfv373cnKb7//nurXr26NWrUyH0mEpvjJQr5VatWdcsMHjw4XscEAAAAQORoO0ekVFVV5bV8+fLRLqdAp9C5Y8eOYGCePn26q5ArYKrK6oX0qVOnWrZs2dz99u3bu9eOGjXKhVi1OGfOnNlVlCMaMWKENWnSJHhf1WWFRI8qth9++KF9/PHHLsj++uuv9t5777kTAY0bN3bLaMx66Oslf/78wZMICRmrrvDdv39/96f2U5VwVegVcvXYgAED3LLvvPOO/ffff+74eCc0XnrpJReEn376aVeZjkhVebW5q/ugdu3aru1f6/Gq4dqOnlP4VoVf9JxOdMyZM8dVsHWsojteHlXMH3300WjfrzoKdPMcPXo0QccPAAAAuFxQ+UakFLxjQ1Vlhe7QSnXFihVdqNVzoW3NXvCWQoUKucAYGzVr1gy7r0quwm6FChXcdlTR1ra8Sq7astOkSROsOPtJ21AA1lh0VbkVxr1AvmfPHncSQ/dF+6gQHNpJoDH1OjEROoY8VOHChe3mm2+2t956y93/5JNPXPhVtVvUXq7joRZ2HQfvppMh6jaIzfGK6jhHZsyYMe5kiXcL/dwBAAAARI3KNyJVpkwZ1/acWJOqpUuXLuy+1q3QGRsR294VJFXVVoVXrdcaP33nnXe6ScJE95OK2r81dv2HH36wr7/+2kaPHu2q92qTV9BWeNaxTAiN51anwMSJE13LuVr61SXgBWudyPBa30N5Vf2YjpcnsuEFEQ0aNMiN+Q+tfBPAAQAAgJgRvhEptWZrfPLLL79sffr0uSCYaZIvhTtVU3fv3u1uXgjbtGmTe14V8NhS27mqx7GxcuVK147dunXrYADVRGoezcquYK9KtNd2HnFbEtvtRUfHQOPQ1T6uEwxq01c7uwKyxraHVt91rNR6r7Hf3vHUe0mdOrWVK1cuym1ovLaWf/XVV904fIV8j8Z363JwmgFe3QXxOV5xodZ2r70dAAAAQOzRdo4oKXgroGqssSYp27p1q2tX1iRmmiBNFG4Vdtu1a+eqvxp/rMnOFDpj08bsUXBctWqVC4V///13tFVxVZLnzp3r2svVdt22bduw5bWujh07usnKNPZZLdiqDGscuBQvXtxV3hWODxw4EOVs47GltvKZM2cGg7ZOXCho67JkoeFbxyhjxoxu33766SdbsmSJu4a6qtqRjff2qIVe4VlVZ71379h7x1/3W7VqZV9++aU7ft9884098cQTtnbt2lgdLwAAAAD+I3wjSpqkTIFal+bSRFxXXXWVm1BME6WpCisKsR999JGbwVst2AqDep2CZ1yoNVohU9VyXTIr4njkUM8++6zbXt26dd1kZarQqwIcSvun1mpdLk3VaM2kroqz6PJgw4cPd7OoK/RGNdt4bClg6ySFN7Zb9HPEx9QqrmukaxZyTUSn/dOs5Kqax6Rr166uTbxz585hj+v4f/755+7Y6zldDk6Xcfv999+DgT42xwsAAACAv1IFYjuzFoBks3z5chfU1d4fXZU8qWnMt7vk2iODLE2GjMm9O5e1tWOGJPcuAAAApChH///fdY8cOWLZs2dP8PoY8w1cxDSzuVrjhw0b5mY4v5iCNwAAAIDYo+0clz21uIdepiviLboWeL/NmjXLjVHXBHbjxo1Ltv0AAAAAkDBUvnHZ0+XANBlZdM8nF020phsAAACASxvhG5c9XaZL178GAAAAAL/Qdg4AAAAAwMUavmfMmGHXXXeda8nVZY3kueeec5edAgAAAAAACQzfuoZyv379rEWLFm4iKF3PWHLmzOkCOAAAAAAASOB1vitWrGijR4+2Vq1aWbZs2WzDhg1WqlQp++mnn6xBgwb2999/x3WVAC5BiX3tQwAAACCl/q4br8r3jh07rFq1ahc8niFDBjtx4kSCdwoAAAAAgJQkXuG7ZMmSkV6a6YsvvrAKFSokxn4BAAAAAHB5X2pM47179+5t//33n6lrffXq1TZr1iwbM2aMTZ48OfH3EgAAAACAyy18d+vWzTJlymRPPvmknTx50tq2betmPX/++eftnnvuSfy9BAAAAADgcgrfZ8+etXfeeceaNm1q7dq1c+H7+PHjlj9/fn/2EAAAAACAy23Md9q0ae2BBx5wLeeSOXNmgjcAAAAAAInddl67dm1bt26dFS9ePD4vB5DCXDdujKXJmCG5d+Oysf7JYcm9CwAAAEiK8N2rVy979NFH7Y8//rAaNWpYlixZwp6vUqVKfFYLAAAAAECKFK/w7U2q1qdPn+BjqVKlcjOf689z584l3h4CAAAAAHA5hu8dO3Yk/p4AAAAAAJBCxSt8M9YbAAAAAACfw/f06dOjfb5Dhw7xWS0AAAAAAClSvML3ww8/HHb/zJkz7nrf6dOnd5ceI3wDAAAAAJCA63zLP//8E3Y7fvy4bdmyxa6//nqbNWtWfFYJAAAAAECKFa/wHZkyZcrY2LFjL6iKAylRp06drFWrVnapKVGihD333HPJvRsAAADAZSfRwrekTZvW9uzZk5irBC5bU6dOdZfuq1ChwgXPvf/+++45hWkAAAAAKXTM98cffxx2X9f33rt3r7300kt23XXXJda+ASmW/s6cO3fOnbCKTpYsWWz//v327bffWp06dYKPv/nmm1asWLEk2FMAAAAAyVb5Vrtt6O3222+3YcOGWZUqVeytt95KlB0DYuPYsWPWrl07F1ILFSpkEydOtAYNGljfvn3d86dOnbL+/ftbkSJF3DLXXHONLV26NKy6nDNnTluwYIGrMGfNmtWaNWvmTiZ5FJL79evnlsuTJ48NGDDAhedQ58+ftzFjxljJkiUtU6ZMVrVqVZszZ07weW1Tler58+dbjRo1LEOGDLZixYoY35/Cedu2bcP+Xv3xxx9ufXo81Pbt261ly5ZWoEAB9z5q1aplixYtinb9hw8ftm7dulm+fPkse/bs1rBhQ9uwYUOM+wUAAAAgCcK3gkboTeFk37599s4777gABCQVheKVK1e6boyFCxfa8uXL7Ycffgg+/+CDD7qq8bvvvms//vij3XXXXS5cb926NbiMZuofP368zZgxw77++mvbtWuXC+yeCRMmuJCuAKzAfOjQIfvwww/D9kPBW5fge+211+znn3+2Rx55xO677z5btmxZ2HKPP/64mxth8+bN7mRVbHTp0sXee+89t5+ifdF7UMgOpYkPW7RoYYsXL7Z169a5ZW699Vb3fqKi46HKuk4KfP/991a9enVr1KiRe48AAAAAkjl8jxgxIhgEQv3777/uOSCpqt7Tpk1zwVmB8aqrrrIpU6a4k0Gi0Kn7Gh99ww032JVXXulCtWbl1+Ohl8pTaK5Zs6YLnwrsCrAeTVA2aNAg1+Gh6riWzZEjR/B5VddHjx7twnnTpk2tVKlSbkI2he9JkyaF7bP+fjRp0sTtS+7cuWP1PqtVq+bWqUq6Ku4K3wrkEanafv/997vjoAkQR44c6bYTcZiIRycSVq9e7Y6P3rteo2OpCn9o1T6U3uvRo0fDbgAAAAB8Ct/Dhw93VbaIFMj1HJAUfvvtNxeca9euHXxMobhcuXLu540bN7ogXrZsWdeG7d1UjVaLtkfXpldI9ah7Q9VgOXLkiGtBV7t6aCu4wqpn27Zt7ruvUB26HVXCQ7cjoa+LC4VtnTDQvp84ccJVuCPS30mdXNAJAgVo7YMq7FFVvtVerteolT50v3fs2HHBfodW+HWMvVvRokXj9X4AAACAy028JlxT9U3jVyP7ZT621TzAbwqWadKkce3U+jOUQqYnXbp0Yc/pux1xTHdM25HPPvvMjS0PpbHdoTTuPD40rl1jzTW3Qvv27SOdqE3BW633ql6XLl3ajT2/88477fTp01Hut040hI6B9yi8R0YdAGr196jyTQAHAAAAEjl858qVywUT3VRNDA3gqjDql/kHHnggLqsE4k2t2ArOa9asCc78rUr1r7/+avXq1XPt2vpeqoqttvP4UHVXAXXVqlVunXL27Nng+GipWLGiC9mqMNevX9/8oJNat912mxv7rbb3yGjsu9rdW7du7e7r7+POnTujXKf2X3M1KMjH9pJlep8RTygAAAAASOTwrbGvqgiqBVbt5aHjXtOnT+9+gQ+9HBLgp2zZslnHjh3tsccec+E0f/78NnToUEudOnXwBJEqxh06dHCTpimMHzhwwI3n1mRnN998c6y28/DDD7tJ0jQmunz58vbss8+6WcJD90NVZ02ypgkINaZcJwEUhjWDuPYxMWis9yuvvOLaxCOj/Zs7d66bZE3vf/DgwW5/otK4cWP391VXLBg3bpw7Xnv27HEVfAX4+LbIAwAAAEhg+PZChC6nVLdu3QvadYGkpiCsbotbbrnFBV21Zu/evdsyZszontc46aeeesoeffRR+/PPPy1v3rx27bXXuuVjS6/VuG99/xXsdfJJ4VQB26PJzXS5Lo2J1lh0tW2rsvy///0v0d6r2sh1i+5YaN/0d1Pvc+DAgdFOiKaA/vnnn9sTTzxhnTt3dicmChYs6Cr8EWdSBwAAAJAwqQJxGdwaif/++++CMaUKQUBy0GRkGnetSnfXrl2Te3dSPIV7dcBc9cTjliYj7ehJZf2Tw5J7FwAAAC6b33WPHDmSKBk3XhOuaWZnVRg1/vTgwYMXPO9d6gnwm65n/csvv7gZz/WXwrvUXcuWLZN71wAAAAAgYZca0xjbr776yl599VU3+dLkyZPdGPDChQu7yysBSUmze+sa1xrDrMr38uXLXdv1xa5SpUphl/gKvc2cOTO5dw8AAABAIopX5fuTTz5xIbtBgwZurKhmktaljYoXL+5Cgya5ApKCJlHTzOOXIo231nXKI8OYawAAACBliVf4PnTokLvMk6j3XfdFszz37NkzcfcQSKF0sgoAAADA5SFebecK3jt27HA/69JLGvvtVcQ1yzMAAAAAAEhg+Far+YYNG9zPjz/+uL388svu0k66zrHGgwMAAAAAgAS2nStkezTJlWab1rhbjfuuUqVKfFYJAAAAAECKlSjX+VbVG8DlJ7GvfQgAAACk1N9149V2rut4jxw50ooUKeIui/Tbb7+5xwcPHmxvvvlmgncKAAAAAICUJF7he9SoUTZ16lQbN26cpU+fPvj4VVdd5a75DQAAAAAAEhi+dY3v119/3V3PO02aNMHHq1at6sZ/AwAAAACABIbvP//8002uFtH58+ftzJkz8VklAAAAAAApVrzCd8WKFW358uUXPD5nzhyrVq1aYuwXAAAAAACX96XGhgwZYh07dnQVcFW7586da1u2bHHt6J9++mni7yUAAAAAAJfLpcY0q3nJkiUtVapUrvI9YsQI27Bhgx0/ftyqV6/uQvlNN93k7x4DuOguv3DVmIGWJmOG5N6dFGt93+HJvQsAAACXnaOJfKmxOFW+y5QpY3v37rX8+fPbDTfcYLlz57aNGzdagQIFErwjAAAAAACkVHEa8x2xSD5//nw7ceJEYu8TAAAAAAApSrwmXPPEoWMdAAAAAIDLVpzCt8Z66xbxMQAAAAAAkEhjvlXp7tSpk2XI8P8mVvrvv//sgQcesCxZsoQtp9nPAQAAAABAPMK3Li8W6r777ovLywEAAAAAuCzFKXxPmTLFvz0BAAAAACCFStCEa0BSGjZsmF199dXJvRsAAAAAEGeEbyQqb1K+qG4K0H46cOCA9ezZ04oVK+bmJihYsKA1bdrUVq5caZeKpUuXumN1+PDhWM3D0LhxY/ceI3rllVcsZ86c9scff/i0pwAAAAB8aTsHYrJ3797gz7Nnz7YhQ4bYli1bgo9lzZrV1+3fcccddvr0aZs2bZqVKlXK/vrrL1u8eLEdPHjQLgVnzpyJ0/IK6RoOUrlyZZs0aZLdf//97vEdO3bYgAED7NVXX7UrrrjCp70FAAAAEFtUvpGoVGn2bjly5HDhMPSxd9991ypUqGAZM2a08uXLu+psKFVp7733XsudO7ebRb9mzZq2atWqsGVmzJhhJUqUcOu/55577NixY+5xVYqXL19uTz/9tN14441WvHhxq127tg0aNMhuu+02t8zOnTvdPq1fvz64Pr1Oj6niHFp5/uyzz6xKlSpuX6+99lr76aefgq+ZOnWqqyrPmzfPypQp45ZR9Xn37t1h+6rwe+WVV1r69OmtXLlybt9DaTtaRvun99u9e3e375IrVy73vK4wEJ2iRYva888/b/3793ehW9Xwrl272k033WTt27d3+928eXN34qNAgQLusb///jv4+jlz5rjwnilTJsuTJ4+rpJ84cSKWnzgAAACA2CB8I8nMnDnTVcJHjRplmzdvttGjR9vgwYNdlVqOHz9u9evXtz///NM+/vhj27Bhg6venj9/PriO7du3u8D76aefutuyZcts7Nix7jmFS930/KlTpxK8v4899phNmDDB1qxZY/ny5bNbb701rDJ98uRJ916mT5/u2toV4nUywPPhhx/aww8/bI8++qgLwKpKd+7c2ZYsWRK2HbXit27d2jZu3GjDhw+3Dz74wD2ujgF1EihYx+ZKBI0aNbIuXbrYSy+95LanSrj2qWHDhlatWjVbu3atffHFF64boE2bNu51Wr9Oduh1+kx04uH22293AT4yOq5Hjx4NuwEAAACIGW3nSDJDhw51YVbhTkqWLGmbNm1yIVHh8Z133nFjthV2VfmW0qVLh61DQVxV52zZsrn7quKqrVwhOG3atO45VY9fe+01q169ugvzCsSqYMdnf5s0aeJ+1gkCtW8rUHvBVUFcQfeaa64JLqOq/urVq13Fffz48a5q3atXL/d8v3797LvvvnOPe9Vtadu2rQvlHlWvJX/+/K66Hluvv/66VapUyb7++msX4HXC4KmnnnLBWyc6PG+99Zarlv/666/uhMfZs2fdZ6JOAVEVPCpjxoxxJwgAAAAAxA2VbyQJtTGraq12aK9CrZvCoR4XtYIrKHrBOzJqN/eCtxQqVMj2798fNuZ7z549rnLerFkzV8lVCFcoj6s6deoEf9Y+qW1c1WGPwn6tWrWC99VGr7DsLaM/r7vuurB16n7oOkSt9YlBYV3VdZ0AaNWqlXtM3QOqtIcec+2n6LhXrVrVVcwVuO+66y5744037J9//olyG2rhP3LkSPAWsc0eAAAAQOSofCNJqMIqCndepdiTJk0a96fGHMckXbp0Yfc1Jjq0LV00/loVa93U1t6tWzdXxVYVOnXq/3e+KbStOq6TnCU2jfVOLDohoFvocVe7vMbBR6QTFzr2CxcutG+++ca+/PJLe/HFF+2JJ55w4+zVmRCRZpDXDQAAAEDcUPlGktBEX4ULF7bffvvNtZKH3ryQp9ZwVb8PHTqUqNuuWLFicAIxtWJHnJU9dPK1UGoR96garDZtVZU9atfWOGqPxmhrjLW3jP6MeIkz3df+REeTs8m5c+csoVT1//nnn13HQMTj7oV+ncBQRV7t5OvWrXPbV3s9AAAAgMRD5RtJRuGuT58+bpZytYRr8i6FVwVbjYfWxF8am6yWaY0tVmVWYVChPbQFPCq6nJhapzV5mIK82tO1/nHjxlnLli2D1XXNXK5J2hT61bL+5JNPRrq+ESNGuNm/deJA1eC8efMG27m9KvxDDz1kL7zwgqs2P/jgg27dGu/tTdim8eFqpdcM4p988onNnTvXFi1aFO370NhrBWJNKNeiRQu3z/G9RFvv3r1dt4GOrSavU/v8tm3b3KzzkydPdsdHY+Y1M7ra1lXx1rj70JMMAAAAABKOyjeSjNq/Ffi861JrMjSNxfYq36q4qvVZIVChU8soJHtt6TFRQFVL+8SJE61evXp21VVXubZzTcCmidFCJxxT1bpGjRrWt29fN+48Mtq2ZivXcvv27XPh2atKS+bMmW3gwIFuwjRVjrV9Xdvco6Cumco1wZomQtPEcnrvDRo0iPZ9FClSxJ2oePzxx13wV6iPL524ULVdVXQFbB1TvWeNTVcLfvbs2d0EbTreZcuWdSciNCmeLk0GAAAAIPGkCkR1TSHgMqVJ2jQbuSryUc02rpMGCrFqM7+c6VJj6mS4asxAS5ORseB+Wd+XGeYBAACS63ddTTSsolVCUfkGAAAAAMBnhG/gIrdr166wS4VFvOl5AAAAABc32s6Bi5zGp+/cuTPK5zWTeejlxZISbedJg7ZzAACAS7/tnNnOgYucgrUuDQYAAADg0kXbOQAAAAAAPiN8AwAAAADgM8I3AAAAAAA+Y8w3gARb2et/iTIJBQAAAJBSUfkGAAAAAMBnhG8AAAAAAHxG+AYAAAAAwGeEbwAAAAAAfEb4BgAAAADAZ8x2DiDBGr890tJmypDcu5GifNP5qeTeBQAAACQiKt8AAAAAAPiM8A0AAAAAgM8I3wAAAAAA+IzwDQAAAACAzwjfAAAAAAD4jPANAAAAAIDPCN8AAAAAAPiM8I2LytKlSy1VqlR2+PBhu1hof+bNm+d+3rlzp7u/fv36SPd36tSpljNnzmTdXwAAAAAXH8L3ZapTp04uNOqWPn16K126tI0YMcLOnj1rl4thw4YFj4FuOXLksBtuuMGWLVsWttzevXutefPmsVrn3Xffbb/++qslhVOnTlmlSpWsR48eFzw3YMAAK1mypB07dixJ9gUAAABA9Ajfl7FmzZq5YLl161Z79NFHXRh95plnkm1/zpw5k+TbVHjVMdDt22+/tTJlytgtt9xiR44cCS5TsGBBy5AhQ6zWlylTJsufP78lBe3T9OnTXbV9wYIFwce/++47mzhxons8W7ZsSbIvAAAAAKJH+L6MKbwpWBYvXtx69uxpjRs3to8//tieffZZq1y5smXJksWKFi1qvXr1suPHjwdf57VWqxVbYTVjxozWtGlT2717d9j6P/roI6tevbp7vlSpUjZ8+PCwyrqqza+++qrddtttblujRo2KdD9XrFjhKtIKttqfPn362IkTJ4LPv/LKK8H9KFCggN15553B5+bMmePei16bJ08e9x5DX5s2bVp3DHSrWLGiq/7rvYZWr0PbzmMSse1cJzSuvvpqmzFjhpUoUcJV1++5556wirR+bteunTsGhQoVcsG5QYMG1rdv3xi3V6NGDXviiSesa9eurvX9v//+s86dO9tDDz1k9evXT9CxAwAAAJB4CN8IUkA7ffq0pU6d2l544QX7+eefbdq0afbVV1+5NuZQJ0+edGFZldeVK1e64KdQ6Vm+fLl16NDBHn74Ydu0aZNNmjTJBdOIAVvhtHXr1rZx40br0qXLBfu0fft2V6G/44477Mcff7TZs2e7QPnggw+659euXesCpULzli1b7IsvvrB69eq551TNvvfee916N2/e7MZn33777RYIBKJs454yZYoLz+XKlUuUY+q9B4X3Tz/91N3U1j527Njg8/369XPHUCc+Fi5c6I7dDz/8EOv1K3zr5IGOw5NPPulOFowePTpBxw4AAABA4kqbyOvDJUhhdPHixa51WRXT0IqrqrVPPfWUPfDAA65KGtoi/tJLL9k111zj7iukV6hQwVavXm21a9d2Ve7HH3/cOnbs6J5X5XvkyJEuxA8dOjS4nrZt27pKree3334L27cxY8a4qrC3T6rS6sSAqrqqmu/atctVjNUqrhZrVfGrVasWDN+qtCtw63FRFTyUQn/WrFmDJxS0DoXU7NmzJ9rxPX/+fFgLePv27d3x1okIVb117N555x1r1KiRe14nAAoXLhzr9at6r5MgqoJrWwryqmQn5NhFRScodPMcPXo0nkcFAAAAuLwQvi9jqsIqeCpIK7QpCKsSvWjRIhfcfvnlFxeuFGDVzqxwmjlz5mDgq1WrVnBd5cuXdxVjVZgVvjds2OBCYGil+9y5cxesp2bNmtHuo9ajqu3MmTPDThZof3fs2GFNmjRxoVHhXlVe3VRJ1/qrVq3qAq0Ct9rib7rpJtdWnStXruC6VOFWxVkUhBW877rrLluyZEmM+xZbOoEROvZareX79+8PnmzQ8dcx86g1Pa6Vd7XMq8KtDgRvvxNy7KKi74VOrAAAAACIG9rOL2M33niju2SWJlz7999/XQX2wIEDrhJapUoV++CDD+z777+3l19+2S2vlvTY0rhphTSt37upyqxtqSrrUeU1pvXcf//9YetRqNR6rrzyShdq1aI9a9YsF2qHDBniQrdCaJo0aVwb9/z58104ffHFF12oVfD0eDO966aqr9rBixQpYs8995wllnTp0oXdV1u4AnBi0wkR3RLj2EVl0KBBbjI67xZxnD8AAACAyFH5vowp+Cp0hlLYVjCcMGGCG/st77333gWvVTVcY4a9iq3GDCu0qfVcNNGaHou4/rjSejRmPLr1KHBqIjXd1NKuCrzGqavdXEH3uuuuczeFS1V6P/zwQzfOOioK7ToZkRRUdVY4X7NmjRUrVsw9plCrCd8SOv46occuqkn6YjvzOwAAAID/Q/hGGAU1tUGrSnzrrbe61vHXXnvtguUUGDU+XGOIFeA0ide1114bDOMKuqqgK1Cq1VtBXlXXn376yY0hj62BAwe69Wr93bp1cycMFChV0daYc7XOq3VbQVXt5J9//rk7eaAK96pVq9zYarWb6/Jfuq/KvneCwDuJsG/fvrC2c61f200Kqj5rXPxjjz1muXPndvupEKzjpRMHCZGQYwcAAAAgcdF2jjBqO9alxp5++mm76qqr3HhhjfONSOOCFe40TlxVZY0dV3D1aIy1wt2XX37pxoYrBOoSWt7EZ7Gl9nfNDq5KsC6ZpdZwBXtvQjJVaufOnWsNGzZ0oVonCtRGret3a9K0r7/+2lq0aGFly5Z1M4Grot+8efPg+jWju1quddMlwVTl12Rkmqk9qeh416lTx52sUAVax1PvJbQ9Pz4ScuwAAAAAJK5UgaiuuwREQTN3awbt6MYGI/50HW6NO9eJAl2/+2KmCfk0QVytl/tb2ky0oyembzrHvkMEAAAA/v2uq2GhiXE1JNrOgWS2bt06N7O8Wvb1F1vX3ZaWLVsm964BAAAASCSEb+AiMH78eDdBnWZf1/W6ly9fbnnz5nV/hrbJR6QZzQEAAABc/Gg7By5imnX9zz//jPL5hM4mn1C0nfuHtnMAAIDkRds5cBnJlClTsgdsAAAAAAnHbOcAAAAAAPiM8A0AAAAAgM8I3wAAAAAA+Iwx3wASbNF9gxNlEgoAAAAgpaLyDQAAAACAzwjfAAAAAAD4jPANAAAAAIDPCN8AAAAAAPiM8A0AAAAAgM8I3wAAAAAA+IxLjQFIsHs+HmLpMmdI7t1IET66/enk3gUAAAD4gMo3AAAAAAA+I3wDAAAAAOAzwjcAAAAAAD4jfAMAAAAA4DPCNwAAAAAAPiN8AwAAAADgM8I3AAAAAAA+I3wDAAAAAOAzwjcitW/fPnvooYesVKlSliFDBitatKjdeuuttnjx4kTdToMGDaxv376WFJYuXWqpUqWyw4cPx+l1u3fvti5duljhwoUtffr0Vrx4cXv44Yft4MGDvu0rAAAAgJSF8I0L7Ny502rUqGFfffWVPfPMM7Zx40b74osv7MYbb7TevXsn+f4EAgE7e/asJYfffvvNatasaVu3brVZs2bZtm3b7LXXXnMnIerUqWOHDh2ylOj06dPJvQsAAABAikL4xgV69erlKsSrV6+2O+64w8qWLWuVKlWyfv362XfffRdcbteuXdayZUvLmjWrZc+e3dq0aWN//fVX8Plhw4bZ1VdfbTNmzLASJUpYjhw57J577rFjx4655zt16mTLli2z559/3m1PNwV/r0I9f/58dxJAlfcVK1bY9u3b3fYKFCjgtlmrVi1btGhR2L6fOnXKBg4c6Cr1el3p0qXtzTffdOvVyQPJlSuXW7+2HxOdbFC1+8svv7T69etbsWLFrHnz5m67f/75pz3xxBPBZfUeR48e7ark2bJlc8u+/vrrF1TRdZxy5sxpuXPndu9H+xbVSQft//jx48MeX79+vdt/nQgQVfK7detm+fLlc59Dw4YNbcOGDcHlY3PctO8jR460Dh06uHX06NEjxmMDAAAAIPYI3wijSq6q3AqdWbJkueB5hUY5f/68C3RaXgF64cKFrkp89913hy2v4Ddv3jz79NNP3U3Ljh071j2n0K3qcffu3W3v3r3uptDsefzxx92ymzdvtipVqtjx48etRYsWruq8bt06a9asmWuF10kAj8KjKtQvvPCCe92kSZNc4NR6P/jgA7fMli1b3La0/ZiOxYIFC9zJiEyZMoU9V7BgQWvXrp3Nnj3bhWTPhAkTXKVc+6fX9ezZ021Pzpw5Y02bNnXBfPny5bZy5Uq3b3ofkVWaFbAV5KdMmRL2uO7Xq1fPBXO56667bP/+/e5kxffff2/Vq1e3Ro0aBavysTluopBftWpVt8zgwYMjPSY6uXH06NGwGwAAAICYpY3FMriMqJqqMFm+fPlol1OQUzv6jh07goF5+vTprkK+Zs0aV131QvrUqVNd4JT27du7144aNcpVwlVVzpw5swuzEY0YMcKaNGkSvK9KscKhR5XaDz/80D7++GN78MEH7ddff7X33nvPnQho3LixW0Zj1kNfL/nz5w+eRIiOWs11LCpUqBDp83r8n3/+sQMHDrh1ikKuQreoAj9x4kRbsmSJlStXzgV1HY/Jkye7YO0Fae2Lqv033XTTBdtQdX7IkCGuC6F27douwL/zzjvBarg6AvScwrcq/aLndMJjzpw5roKtYxbdcfOoYv7oo49Ge0zGjBljw4cPj/HYAQAAAAhH5RthQqu40VFVWaE7tFJdsWJFFyT1XGg7sxe8pVChQi4oxoYqyKFUwe3fv78LvdqOqsballfBVTt2mjRpXHt4chwTUYXeo4Ctkwre+1UruE5u6Hho33XTCYH//vvPdQhERpO83XzzzfbWW2+5+5988omrPqva7a1TxyVPnjzBdeqmkyLeOmM6blEd78gMGjTIjhw5ErypjR4AAABAzKh8I0yZMmVcaPzll18SZX3p0qULu691q/obGxHb3hUgVdVWZVct12oFv/POO4Mt2xFbwxNK29D+Kqi2bt36guf1uMaPa6x1bN6vQrDGsM+cOfOCdYWuIyKN51bHgKroqpSrtV/dAt46dUJDlfOIvOp+TMfNE9kwg4hUXfcq7AAAAABij/CNMKrEalzyyy+/bH369LkgkGlyL4U6VVFV9dTNq35v2rTJPa8KeGyp7fzcuXOxWlZjpNWG7QVhBc/QycoqV67sgq7GlXtt5xG3JbHdnqrJant/5ZVX7JFHHgkL97oUm0K0xph7LeQx0VhstZ6rRV2TmsWWWtn1Obz66qtuPP7XX38dtk7tS9q0aV2XQXyOGwAAAAD/0XaOCyh4K6BqjLEmKdPYZ1V5NYmZJkgThVuFXU069sMPP7hxxwqiavmOTfuyR4Fx1apVLgz+/fff0VbFVZWfO3euay9Xu3Xbtm3Dlte6Onbs6CYp05hntV6rIqxx4KLrcysoa+I3jdNWCI3JSy+95Nq8dUJCoVcnGxSAFcqLFCnixq7Hlo5V3rx53UR1mnDN2z+d5Pjjjz+ifJ1a6RWe1fKtY+B9Bt7noPutWrVyM7LrOH7zzTduFva1a9fG6rgBAAAA8B/hGxfQJGUK1Lo0lybguuqqq1zY1ERpqr6KQuxHH33k2q4187ZCoF6nym5cqCVa4VLVcrVeRxyHHOrZZ59126tbt66brVuBWJXfUNo/tVRr0jNNGqeZ1E+cOOGeU1jWZGGaRV2X3QqdbCwqCq4KsXpvukTYlVde6SYx07H59ttvg5O4xYZaxRXgdQmy22+/3XUPdO3a1Y35jqkSruXUJt65c+ewx/U5fP755+4z0HO6LJwu5/b777+79xjb4wYAAADAX6kCcZlNCkCyUKVclw9T5d0L1RcDXWpMs9Y3n/GwpcvMWPDE8NHtTyf3LgAAAMD+73ddTTQcl2GjUWHMN3ARU8u7WuSHDRvmZji/mII3AAAAgNij7RyXLbW4h16eK+Ituhb4pDJr1iw3Vl0T2Y0bNy65dwcAAABAPFH5xmVL19DWJGTRPZ/cNNGabgAAAAAubYRvXLZ0eS5d9xoAAAAA/EbbOQAAAAAAPiN8AwAAAADgM8I3AAAAAAA+Y8w3gAR797YRiXLtQwAAACClovINAAAAAIDPCN8AAAAAAPiM8A0AAAAAgM8I3wAAAAAA+IzwDQAAAACAzwjfAAAAAAD4jEuNAUiwx5YOsPRZMiT3blzSXmz0fHLvAgAAAHxE5RsAAAAAAJ8RvgEAAAAA8BnhGwAAAAAAnxG+AQAAAADwGeEbAAAAAACfEb4BAAAAAPAZ4RsAAAAAAJ8RvgEAAAAA8BnhGylep06dLFWqVO6WPn16K126tI0YMcLOnj2boHW2atXKktuvv/5qmTNntnfeeSfs8fPnz1vdunXtzjvvTLZ9AwAAAPB/CN+4LDRr1sz27t1rW7dutUcffdSGDRtmzzzzTJzXc+7cORdsk9Lp06ejfK5s2bI2duxYe+ihh9z780yYMMF+++03e+2115JoLwEAAABEh/CNy0KGDBmsYMGCVrx4cevZs6c1btzYPv74Y3v22WetcuXKliVLFitatKj16tXLjh8/Hnzd1KlTLWfOnG7ZihUruvV06dLFpk2bZh999FGwor506VLbuXOn+3nu3Ll24403uop01apV7dtvvw3blxUrVtgNN9xgmTJlctvs06ePnThxIvh8iRIlbOTIkdahQwfLnj279ejRI9r3puCt7XTv3t3d/+WXX2zIkCH2+uuvW968eW3y5MlWoUIFy5gxo5UvX95eeeWVsGD/4IMPWqFChdzzOj5jxoxJxCMPAAAAQNJyGHA5UvA9ePCgpU6d2l544QUrWbKkqxQrfA8YMCAsoJ48edKefvppF2Lz5Mnjguq///5rR48etSlTprhlcufObXv27HE/P/HEEzZ+/HgrU6aM+/nee++1bdu2Wdq0aW379u2uCv/UU0/ZW2+9ZQcOHHDhVzdvXaLXK0APHTo0xveiwK/XVqlSxd544w1788037Z577rHbbrvNZs6c6dbz0ksvWbVq1WzdunUupOtkQ8eOHd1714mF9957z4oVK2a7d+92t6icOnXK3Tw6BgAAAABiRvjGZSUQCNjixYttwYIFrmLct2/fsIqzQvEDDzwQFr7PnDnj7qu6HBreFUJVTY+of//+dvPNN7ufhw8fbpUqVXLhW1VnVZXbtWsX3K4CugJw/fr17dVXX3XVZ2nYsKFrj48tVayfe+4569atm11xxRX25ZdfuscV3tWCfvvtt7v7OsmwadMmmzRpkgvfu3btcvtw/fXXuxCv9URH+6/3BAAAACBuaDvHZeHTTz+1rFmzunDbvHlzu/vuu92470WLFlmjRo2sSJEili1bNmvfvr2riKva7dEkbaoqx1bosqqSy/79+92fGzZscK3s2hfv1rRpUzeOfMeOHcHX1axZM87vsXPnzm57OqmgdnW1sqvS3rVr17Dt6QSDHvcmjlu/fr2VK1fOtb97oT0qgwYNsiNHjgRv0VXJAQAAAPwfKt+4LGgMtirLCtKFCxd2LeAao33LLbe4MeCjRo1yreMaj62wqrHQGrPtVblVFY6tdOnSBX/2XudN0qbx5Pfff78LuhGp7dujtvD40PvSzduWqBX9mmuuCVsuTZo07s/q1au70D9//nx3IqJNmzZuPPycOXMiXb/GvOsGAAAAIG4I37gsKMzqEmOhvv/+exeK1Zatsd+isc+xoRCvmc/jSmFXbd8R98UPBQoUcCcaNJZdre5RUZVcnQC66dJkGpN+6NAhdzICAAAAQOIgfOOypQCs8dwvvvii3XrrrbZy5cpYX5pL48M1bnzLli1uErYcOXLE6nUDBw60a6+91k2wpvHZOimgML5w4UI3KVpi0/hsVdm1fwrVGqe+du1a++eff6xfv35utne1qmsyNp2AeP/99904ds3wDgAAACDxMOYbly1NoKbwqZnMr7rqKjczeGwvs6UZwzVOWmOz8+XL54J7bMeDL1u2zH799Vd3uTGFXs1Grgq1HxTwNUu7ZkPXJdU0sZvGnGviNdE493Hjxrn3UatWLdeK//nnnwc7AQAAAAAkjlQBTf8MAPGgS42pqt7jo/stfRbGgifEi42eT+5dAAAAQCS/62qiYQ3VTCjKWwAAAAAA+IzwDVzkRo8eHXapsNCbLpsGAAAA4OLHhGvARe6BBx5wlwCLjC6DBgAAAODiR/gGLnK65BeX/QIAAAAubbSdAwAAAADgM8I3AAAAAAA+o+0cQII902Bcolx+AQAAAEipqHwDAAAAAOAzwjcAAAAAAD4jfAMAAAAA4DPCNwAAAAAAPiN8AwAAAADgM8I3AAAAAAA+41JjABLs2W+7W8Ys6ZN7Ny5qj18/I7l3AQAAAMmIyjcAAAAAAD4jfAMAAAAA4DPCNwAAAAAAPiN8AwAAAADgM8I3AAAAAAA+I3wDAAAAAOAzwjcAAAAAAD4jfAORaNCggfXt2zfJtleiRAl77rnnol1m2LBhdvXVVyfZPgEAAABIPIRvXHIOHDhgPXv2tGLFilmGDBmsYMGC1rRpU1u5cmWibWPu3Lk2cuTIRFnX0aNH7YknnrDy5ctbxowZ3f42btzYbSMQCLhl1qxZYz169Ai+JlWqVDZv3ryw9fTv398WL16cKPsEAAAAIGmlTeLtAQl2xx132OnTp23atGlWqlQp++uvv1woPXjwYKJtI3fu3Al6/blz51yAVvC+/vrr7ciRI/bUU09ZrVq1LG3atLZs2TIbMGCANWzY0HLmzGn58uWLcZ1Zs2Z1NwAAAACXHirfuKQcPnzYli9fbk8//bTdeOONVrx4catdu7YNGjTIbrvttuAy3bp1c4E2e/bsLuBu2LDhgvbtGTNmuHbvHDly2D333GPHjh2Lsu38n3/+sQ4dOliuXLksc+bM1rx5c9u6dWvw+alTp7oQ/fHHH1vFihVdRX7Xrl32v//9z3bu3GmrVq2yjh07uufKli1r3bt3t/Xr1wfDdGjbuX6W1q1buwDv3Y/Ydq7nIt68ZeWnn35y+6ltFChQwNq3b29///132Hvs06ePOwmgkw2qyGsbAAAAABIf4RuXFK/6q5bsU6dORbrMXXfdZfv377f58+fb999/b9WrV7dGjRrZoUOHgsts377drePTTz91N1Wix44dG+V2O3XqZGvXrnXh+ttvv3Xt4i1atLAzZ84Elzl58qQ7KTB58mT7+eefLX/+/Pbuu+9au3btrHDhwpG+F1XBI1ILukyZMsX27t0bvB+RnvNu27Zts9KlS1u9evWCJyB00qFatWpuv7/44gvXIdCmTZuwdah7IEuWLO7kwLhx42zEiBG2cOHCKI8DAAAAgPih7RyXFIVVVZlVOX7ttddcsK5fv76rXFepUsVWrFhhq1evduFb1WcZP368C9pz5swJjqs+f/68W0+2bNncfVWF1bo+atSoC7apCrdCt8aU161b1z02c+ZMK1q0qFuvwr4oiL/yyitWtWpVd1/7oIq5xnrHhdeCrkq6qtFR8Z7TiQC14quCP2nSJPfYSy+95IL36NGjg8u/9dZbbp9//fVXV30XHbOhQ4e6n8uUKeNep+PQpEmTSLepEx6hJz3UVg8AAAAgZlS+cclR0NyzZ48LxM2aNbOlS5e6EK4wrfby48ePW548eYJVct127Njhqt0etWd7wVsKFSrkwnJkNm/e7EL/NddcE3xM6y9Xrpx7zpM+fXoXZj3eZGp+U2u7qvEfffSRZcqUyT2m47BkyZKwY+CdBAg9DqH7G9NxkDFjxriQ790U5gEAAADEjMo3LkmaNVzVWd0GDx7sxnirgturVy8XIBXII1Il2ZMuXbqw5zReWtXwhFDw1XpCK9ja5i+//GJ+efvtt23ixInu/RYpUiT4uE5A3Hrrra4NPiIdn/geB42t79evX1jlmwAOAAAAxIzwjRRBE5mpBVwV8H379rlKdejkYwlRoUIFO3v2rBsX7bWda2b1LVu2uO1GJXXq1K4dXhO76cRAxHHfCsg6iRDZuG+FYs2YHh1Vu3XSQa3m1157bdhzOg4ffPCBOwaRrT++1MrvtfMDAAAAiD3aznFJUejVRGKq+P7444+unfz99993k4W1bNnSXT+7Tp061qpVK/vyyy/dTOPffPONu862Jh6LD42F1ro1zlxjytXSfd9997lKsx6PjsaQqzKslvXp06fbpk2b3Bhyjb/WmGwF8MgoNGvstU4kaNx4RHpcs6Er3Osa57qvm66BLr1793YTzN17771uwja1mi9YsMA6d+4cY6gHAAAAkPgI37ikaOyygqxarTWz91VXXeXazhWMNVmY2qY///xz95yCpiYWU0D9/fff3eW24kszj9eoUcNuueUWF+41nlvbidi2HZEu4fXdd9+5sK7rfCtw33DDDTZr1ix75pln3LjpyEyYMMHNOq7grtdEpFZ2zV6u2crVRu7ddB1xUZVdE8QpaN90001WuXJld+k0tcGrIg8AAAAgaaUKJNWsUABSHI351gmEoV+0sYxZ0if37lzUHr9+RnLvAgAAAOLxu+6RI0cse/bsllCUwAAAAAAA8BnhGwAAAAAAnxG+AQAAAADwGeEbAAAAAACfEb4BAAAAAPAZ4RsAAAAAAJ8RvgEAAAAA8FlavzcAIOXrV+eNRLn2IQAAAJBSUfkGAAAAAMBnhG8AAAAAAHxG+AYAAAAAwGeEbwAAAAAAfEb4BgAAAADAZ4RvAAAAAAB8xqXGACTY7DWtLVMW/jmJzn3XLkjuXQAAAEAyovINAAAAAIDPCN8AAAAAAPiM8A0AAAAAgM8I3wAAAAAA+IzwDQAAAACAzwjfAAAAAAD4jPANAAAAAIDPCN9IFjt37rRUqVLZ+vXr3f2lS5e6+4cPH7ZLzaW87wAAAACSBuEbcbZ7927r0qWLFS5c2NKnT2/Fixe3hx9+2A4ePBjvddatW9f27t1rOXLkSNR9VSj2bmnTprVixYpZv3797NSpU5YS6H1UqlTJevToccFzAwYMsJIlS9qxY8eSZd8AAAAA/B/CN+Lkt99+s5o1a9rWrVtt1qxZtm3bNnvttdds8eLFVqdOHTt06FC81qsQX7BgQReSE9uUKVNcsN+xY4e98sorNmPGDHvqqacsJciQIYNNnz7dpk6dagsWLAg+/t1339nEiRPd49myZUvWfQQAAABA+EYc9e7d2wXlL7/80urXr+8qyc2bN7dFixbZn3/+aU888YRbrkSJEjZ69GhXIVf403Kvv/56rFu3FRpz5szpAmWFChUsa9as1qxZMxeiQ02ePNk9nzFjRitfvrwL1xFpPQr2RYsWtVtuucVatmxpP/zwQ/D57du3u8cKFCjgtlOrVi33fiJWmAcOHOjWocBbunRpe/PNNyN9LydPnnTH5LrrrnPvp1OnTtaqVauwZfr27WsNGjQI3tfPDz74oLup+p83b14bPHiwBQKBGD4Rsxo1arjj3rVrV7e9//77zzp37mwPPfSQ+4xWrFhhN9xwg2XKlMntf58+fezEiRPB1+uYlSlTxh1DHYM777wzxm0CAAAAiBvCN2JNVW2F4V69erkgF0rhtl27djZ79uxgYJwwYYKrkq9bt869pmfPnrZly5ZYb08hdvz48a5S/fXXX9uuXbusf//+wednzpxpQ4YMsVGjRtnmzZtd2FdgnTZtWpTr/PXXX+2rr76ya665JvjY8ePHrUWLFq56r31VyL/11lvd9jwdOnRwlf4XXnjBbWvSpEkuqEek8NukSRM7f/68LVy40AX/2NJ+qzV+9erV9vzzz9uzzz7rTi7EhsK3PgMF6yeffNKdyNDx0IkFvZ877rjDfvzxR/f5KIwr5MvatWvda0aMGOE+my+++MLq1asX5XZ0EuLo0aNhNwAAAAAxSxuLZQBHreYK1qo0R0aP//PPP3bgwAF3X4FWoVtUNVYb9JIlS6xcuXKx2t6ZM2dcS/uVV17p7iswKiR6hg4d6gL+7bff7u5rfPOmTZtcMO7YsWNwuXvvvdfSpEljZ8+edeFR1e9BgwYFn69ataq7eUaOHGkffvihffzxx26bCuzvvfeeC9ONGzd2y5QqVeqC/d23b5/dfffdror8zjvvuA6BuFBVWsdIwVnHaOPGje5+9+7dY3ytQrvaz1UFV/BfuXKlq2SPGTPGnRRRpV20bzqBoIr4q6++6k4wZMmSxR0TdSho/H61atWi3I7WN3z48Di9LwAAAABUvhEPsWmFlipVqgR/VqBUZXb//v2x3k7mzJmDwVsKFSoUfL3aplXVVau1KtDeTWO59XgoBVjNqr5hwwb79NNPXZhu3759WOVbFXWdPFClWutRddurfOu1Cu8KrNFRxVvt6KouxzV4y7XXXhs25l1j6HXC49y5c7F6fcWKFV2FW/uhjgPRe1YLf+gxatq0qQvoGgOvZRW4dTJBx0TdBOo4iIpOWhw5ciR40+R7AAAAAGJG5RuxpmCpcKhg2rp16wue1+O5cuWyfPnyufvp0qULe16vVeiLrche7wV/BWZ54403wlrIRUE5lEK/9l1UUdbs36qGK6jrcQVvVbXV4q77aqnXuOfTp0+710RssY/KzTffbB988IGrvleuXDn4eOrUqS84YaGqvh9UAdfNo+N0//33u9byiDQOXycJNP5dY+41jl9t/MOGDbM1a9ZE2jKv8e66AQAAAIgbwjdiLU+ePK5Sqgm6HnnkkbBQqpZrVU01NtqPGcsj0sRgutSZZl9XW3VceOH833//dX+qRVuTonknFBRYdR1yj4K0ThosW7Ys2HYembFjx7rKcqNGjVyYVSVadDLip59+CltW1fSIJxdWrVoVdl8zlqtNPOLJhLioXr26OxngnXyIjMK63pduauVX6Na4eK+dHwAAAEDC0XaOOHnppZfcuGm1LmsSNLUda5IuhfIiRYq4yc+SisYeawyyxjCrlVxjpHVZMU1UFnESNJ0c2LNnjwvQGjdetmzZ4Nh1Bdy5c+cGW9Pbtm0bVqHXzO0aQ66Z2+fNm+fatRWuNQ48IlXPdTKgYcOG9ssvv7jH9LMmNtOYbLWRK+BGDOOiNnddg1wTn2lytxdffNFdPz0hNNb+m2++cWPX9f60/Y8++ig44Zra8HX89Nzvv//u9lHvPbbj8gEAAADEDpVvxImCqoKkAmSbNm3cDOhq69altPRY7ty5k2xfunXr5saFP/PMM/bYY4+5icNUpfYmF/Posluh4841m7dmAvfasxXWFazr1q3rLvGlwBpxFm9NTva///3PTSB38OBB17Kt+5HRGHON01boVkjXiQrNwj5gwAB3GTBtSx0COlkQSo+pGl+7dm1X7Vbw7tGjR4KOkcbd64SDZkPX5cbU/q5x9JoYTlTl1okHtZpr3/T5KvhXqlQpQdsFAAAAEC5VILazZwHwja7zffXVV9tzzz1nlxKdpNB1yV9f1NAyZeFcXnTuu3ZBcu8CAAAA4vG7riYazp49uyUUbecAAAAAAPiM8A1c5JYvXx52qbCINwAAAAAXP/pEgYuAxoZHRdfs1oRoAAAAAC5dhG/gIqdLukV3qTAAAAAAFz/azgEAAAAA8BnhGwAAAAAAnxG+AQAAAADwGWO+ASTY3bU+TJRrHwIAAAApFZVvAAAAAAB8RvgGAAAAAMBnhG8AAAAAAHxG+AYAAAAAwGeEbwAAAAAAfMZs5wASbOXa+pYla5rk3o2LRr3aa5N7FwAAAHCRofINAAAAAIDPCN8AAAAAAPiM8A0AAAAAgM8I3wAAAAAA+IzwDQAAAACAzwjfAAAAAAD4jPANAAAAAIDPCN8AAAAAAPiM8I2LSqpUqWzevHnRLtOpUydr1apVnNZbokQJe+655yyxNGjQwPr27Zto6wMAAACQshG+kSAKwgrMDzzwwAXP9e7d2z2nZeJj586d7vXr168Pe/z555+3qVOnmp+0/pw5c0b5/Ny5c23kyJGW3LQPhQoVskOHDoU9vmHDBsuQIYN9+umnybZvAAAAAP4P4RsJVrRoUXv33Xft33//DT7233//2TvvvGPFihVL9O3lyJEj2mCcFHLnzm3ZsmWz5DZo0CB3/HWiw3PmzBnr2LGj3XfffXbLLbck6/4BAAAA+H8I30iw6tWruwCoarBHPyt4V6tWLdrW76uvvtqGDRsW6XpLlizp/tQ6VAFXq3dkbed6/MEHH3Q3BfO8efPa4MGDLRAIRLnPhw8ftm7dulm+fPkse/bs1rBhQ1ctjm/bud7b6NGjrUuXLi6U672//vrrYa/ZvXu3tWnTxp04UHhv2bKlq+57li5darVr17YsWbK4Za677jr7/fffo92PtGnT2vTp012r/pw5c9xjo0aNcu9v4sSJMb5P/XzjjTe6fdbzNWrUsLVr18b6OAAAAACIHcI3EoVC55QpU4L333rrLevcuXOC1rl69Wr356JFi2zv3r1h4T6iadOmuSCq16gt/dlnn7XJkydHufxdd91l+/fvt/nz59v333/vTiA0atTogvbtuJgwYYLVrFnT1q1bZ7169bKePXvali1bgtXopk2bupC7fPlyW7lypWXNmtWaNWtmp0+ftrNnz7oTCvXr17cff/zRvv32W+vRo4c76RCT8uXL25gxY9z2FixY4H7WZ6EwHdP7bNeunV1xxRW2Zs0a9/zjjz9u6dKli3Jbp06dsqNHj4bdAAAAAMQsbSyWAWKkFme1QHuVWoVLtaKrmhtfqtZKnjx5rGDBgtEuq8q7Kr0Kq+XKlbONGze6+927d79g2RUrVriQrlCqcdEyfvz4YPVYoTc+WrRo4UK3DBw40G1/yZIlbn9mz55t58+fdycEvECtgKwKt46RQvuRI0dcm/iVV17pnq9QoUKst/3www/bRx995PbhoYcectXs2LzPXbt22WOPPeYCvJQpUyba7SjYDx8+PF7HBwAAALicUflGolBQvvnmm91EZQqV+lnt30nl2muvDasS16lTx7Zu3Wrnzp27YFm1Wh8/ftyFelWfvduOHTts+/bt8d6HKlWqBH/WvuiEgYKvt81t27a5yre3PbWea2y8tqmf1U6v6vitt97qqveq9seWtvfEE0+4gP/kk0/G+n3269fPtaU3btzYxo4dG+P71wkWnSTwbmqlBwAAABAzKt9I1NZzjbuWl19++YLnU6dOfcE4bLVjJzUFUs0QHllVPiETuUVs11YgVhj2tqnx1DNnzoyywq+TFn369LEvvvjCVcoVohcuXOhOLMSG2u5D/4zN+9R4+7Zt29pnn33mWtOHDh3qOhZat24d6TZUQfeq6AAAAABij/CNROONX1boVAU3spAZWs3VeGFVYaOSPn1692dk1euIVq1aFXb/u+++cy3UadKkuWBZjXvet2+fC6maKC0paJsK1Pnz53djsaOiyeV0U4VZ1XvNGB/b8B3f91m2bFl3e+SRR+zee+91JwGiCt8AAAAA4oe2cyQaBd3Nmzfbpk2bIg29mml7xowZbsIxjcnW5bAiW86joJopUyZXCf7rr79cm3NUNHZZLdSa4GzWrFn24osvunHQkVGLtYKtJjj78ssv3Yzj33zzjWvbDp3pW6Ff1xgPven9xYcmNlMbvmY41/vXSQdVpFXp/uOPP9x9BW5NtKZx89ovtc3HZdx3XN+nLg2nTgXth7apcfqaeC0h2wQAAAAQOSrfSFTRVXUVLhUyNamYLgk2cuTIaCvfqti+8MILNmLECBsyZIjdcMMNUU7g1qFDBxcmdakuBXoF76gmTlNl/vPPP3chVDOyHzhwwI3PrlevnhUoUCC4nNq2Qy+VJpoMTWO34ypz5sz29ddfu4nYbr/9djt27JgVKVLEzTyuY6Z9/+WXX9ys7QcPHnTt4rp29/333x/nbcX2feo4aVs6djq5oZMD2jcmVAMAAAASX6pAdBdDBi4Buua2rhce8Rri8J+GDuhEyueLr7YsWaPuYrjc1KvNtdIBAABSyu+6R44cibbIGFu0nQMAAAAA4DPCN3CRq1SpUtilwkJvkc2eDgAAAODiw5hvXPKiGgeeUmjcdlSXZAsdow4AAADg4kX4Bi5yxYsXT+5dAAAAAJBAtJ0DAAAAAOAzwjcAAAAAAD6j7RxAgl1Xc1miXH4BAAAASKmofAMAAAAA4DPCNwAAAAAAPqPtHEC8BQIB9+fRo0eTe1cAAACAROX9juv9zptQhG8A8Xbw4EH3Z9GiRZN7VwAAAABfHDt2zHLkyJHg9RC+AcRb7ty53Z+7du1KlH+Q8H9nWXVCY/fu3Uxkl4g4rv7guPqD4+oPjqs/OK7+4Lgm/3FVxVvBu3DhwomybcI3gHhLnfr/TRuh4M3/FBKfjinHNfFxXP3BcfUHx9UfHFd/cFz9wXFN3uOamAUmJlwDAAAAAMBnhG8AAAAAAHxG+AYQbxkyZLChQ4e6P5F4OK7+4Lj6g+PqD46rPziu/uC4+oPjmvKOa6pAYs2bDgAAAAAAIkXlGwAAAAAAnxG+AQAAAADwGeEbAAAAAACfEb6By9jLL79sJUqUsIwZM9o111xjq1evjnb5999/38qXL++Wr1y5sn3++edhz2sKiSFDhlihQoUsU6ZM1rhxY9u6dWvYMocOHbJ27dq56yrmzJnTunbtasePH7eUJDGP65kzZ2zgwIHu8SxZsljhwoWtQ4cOtmfPnrB1aHupUqUKu40dO9ZSksT+vnbq1OmCY9asWbOwZfi+xv24Rjym3u2ZZ54JLsP3NdzPP/9sd9xxR/C4PPfcc/Fa53///We9e/e2PHnyWNasWd06//rrL0tJEvu4jhkzxmrVqmXZsmWz/PnzW6tWrWzLli1hyzRo0OCC7+sDDzxgKUliH9dhw4ZdcMz078bl9n3149hG9u+nbjqWHr6z4d544w274YYbLFeuXO6m308jLp9kv8NqwjUAl5933303kD59+sBbb70V+PnnnwPdu3cP5MyZM/DXX39FuvzKlSsDadKkCYwbNy6wadOmwJNPPhlIly5dYOPGjcFlxo4dG8iRI0dg3rx5gQ0bNgRuu+22QMmSJQP//vtvcJlmzZoFqlatGvjuu+8Cy5cvD5QuXTpw7733BlKKxD6uhw8fDjRu3Dgwe/bswC+//BL49ttvA7Vr1w7UqFEjbD3FixcPjBgxIrB3797g7fjx44GUwo/va8eOHd33MfSYHTp0KGw9fF/jflxDj6duWneqVKkC27dvDy7D9zXc6tWrA/379w/MmjUrULBgwcDEiRPjtc4HHnggULRo0cDixYsDa9euDVx77bWBunXrBlIKP45r06ZNA1OmTAn89NNPgfXr1wdatGgRKFasWNj3sX79+m5bod/XI0eOBFIKP47r0KFDA5UqVQo7ZgcOHAhbJqV/X/06tvv37w87rgsXLtTk2YElS5YEl+E7G65t27aBl19+ObBu3brA5s2bA506dXK/r/7xxx9J/jss4Ru4TCnA9e7dO3j/3LlzgcKFCwfGjBkT6fJt2rQJ3HzzzWGPXXPNNYH777/f/Xz+/Hn3P4pnnnkm+LyCY4YMGdz/RES/rOt/EGvWrAkuM3/+fPeL+Z9//hlICRL7uEb1P2cdx99//z0szET2P+mUwo/jqvDdsmXLKLfJ9zVxvq86xg0bNgx7jO9r1KI6NjGtU//e6kTI+++/H1xGv2TqO6yTdimBH8c1smCjY7Zs2bKwIPPwww8HUio/jqvCt0JKVC6H72tSfWf13bzyyivd72EevrPRO3v2bCBbtmyBadOmJfnvsLSdA5eh06dP2/fff+9aajypU6d297/99ttIX6PHQ5eXpk2bBpffsWOH7du3L2yZHDlyuFYgbxn9qTadmjVrBpfR8tr2qlWr7FLnx3GNzJEjR1wLmY5lKLXtqn2vWrVqrsX37NmzlhL4eVyXLl3qWk3LlStnPXv2tIMHD4atg+9rwr6vaiH97LPPXGteRHxfE3edel7DVEKXUZtvsWLF4r3dlH5co/r3VXLnzh32+MyZMy1v3rx21VVX2aBBg+zkyZOWEvh5XNWyq6FSpUqVcq26u3btCj6X0r+vSfWd1Tbefvtt69Kli/u9IBTf2ajpWOj75/09T8rfYdPGekkAKcbff/9t586dswIFCoQ9rvu//PJLpK/RP0qRLa/Hvee9x6JbRkEnVNq0ad0/ft4ylzI/jmtEGiOnMeD33nuvG3Pk6dOnj1WvXt0dy2+++cb9j3bv3r327LPP2qXOr+Oq8d233367lSxZ0rZv327/+9//rHnz5u5/sGnSpOH7mgjf12nTprmxtDrOofi+Jv469RmkT5/+gpNy0X0+l/txjej8+fPWt29fu+6661xg8bRt29aKFy/uguSPP/7o/g3WuPC5c+fapc6v46rQMnXqVHdiU3+3hw8f7sbc/vTTT+7fhJT+fU2q7+y8efPs8OHDbg6TUHxno6fjoWPjhe2k/B2W8A0AlwidpW3Tpo2bFOTVV18Ne65fv37Bn6tUqeJ+qbn//vvdZEIZMmRIhr29+N1zzz3BnzVxmI7blVde6arhjRo1StZ9SyneeustV/HShDih+L7iYqQJqxQOV6xYEfZ4jx49wv6t0IRM+jdCJ+30bwYupBOZoX/HFcYVBt97771IO2EQP2+++aY71gqSofjORk1dV++++677f33E/zclBdrOgcuQ2pBU2Ys4q6juFyxYMNLX6PHolvf+jGmZ/fv3hz2vVlPNHhnVdi/34xoxeP/++++2cOHCsKp3ZPSLjo7tzp077VLn53ENpdZIbWvbtm3BdfB9jf9xXb58uau0dOvWLcZ9udy/r4mxTv2pdkxVwRJruyn9uIZ68MEH7dNPP7UlS5bYFVdcEeP3Vbx/Ky5lfh9XjyrcZcuWDfv3NSV/X5Pi2Or3gUWLFsX631i53L+z48ePd+H7yy+/dCeFPEn5OyzhG7gMqcpUo0YNW7x4cVi7ne7XqVMn0tfo8dDlRSHQW16tu/rHJ3SZo0ePunEw3jL6U/+j1Vgdz1dffeW27f2P4VLmx3ENDd4aP6f/0WqcbEzWr1/vxiFFbJG6FPl1XCP6448/3JhvVQi8dfB9jf9xVUVG669atWqM+3K5f18TY516Pl26dGHL6OSHxtnGd7sp/biKOokUvD/88EP391v/L4vN91W8fysuZX4d14h0OSZVXb1jltK/r0lxbKdMmeL+zbz55ptjXJbvrNm4ceNs5MiR9sUXX4SN207y32FjPTUbgBRFl2nQLI5Tp051Mzj26NHDXaZh37597vn27dsHHn/88bBLDKVNmzYwfvx4NyOpZjKN7FJjWsdHH30U+PHHH90sx5FdpqFatWqBVatWBVasWBEoU6ZMirt0U2Ie19OnT7vLXVxxxRXuMjihlw05deqUW+abb75xM6LqeV3O6e233w7ky5cv0KFDh0BKkdjH9dixY+5yLppVd8eOHYFFixYFqlev7r6P//33X3A9fF/j/u+A6JI2mTNnDrz66qsXbJPv64XHVX+XdQkc3QoVKuS+m/p569atsV6nd+kmXSbrq6++cpduqlOnjrulFH4c1549e7rLCy1dujTs39eTJ0+657dt2+Yui6fjqX8r9P+3UqVKBerVqxdIKfw4ro8++qg7pjpm+ndDl8zMmzevm03+cvm++nVsvdm9dewGDhx4wTb5zgYuOK76/VSXJpszZ07Y33P9LpDUv8MSvoHL2Isvvuj+8dY/SLpsg65bGHqZCl2KKdR7770XKFu2rFte1+/87LPPwp7XpRoGDx4cKFCggPtHsVGjRoEtW7aELXPw4EH3D1XWrFkD2bNnD3Tu3DnsH7+UIDGPq/7HqfOkkd28a3p+//337nJP+gUyY8aMgQoVKgRGjx4dFiJTgsQ8rvrF+qabbnKhT+FRl3TRdUJDg4zwfY37vwMyadKkQKZMmdylWiLi+3rhcY3q77mWi+06Rb8k9urVK5ArVy538qN169buF8yUJLGPa1T/vura37Jr1y4XWnLnzu3+v6br+j722GMp6prJfhzXu+++24VHra9IkSLuvkLh5fZ99evfggULFrjHI/6OJXxnAxccV/0/PrLjqhPISf07bCr9J/Z1cgAAAAAAEFeM+QYAAAAAwGeEbwAAAAAAfEb4BgAAAADAZ4RvAAAAAAB8RvgGAAAAAMBnhG8AAAAAAHxG+AYAAAAAwGeEbwAAAAAAfEb4BgAAAADAZ4RvAABwyerUqZO1atXKLlY7d+60VKlS2fr16+1ScODAAevZs6cVK1bMMmTIYAULFrSmTZvaypUrk3vXAOCSlza5dwAAACAlOn36tF1q7rjjDrff06ZNs1KlStlff/1lixcvtoMHD/q2TW0vffr0vq0fAC4WVL4BAECK0aBBA3vooYesb9++litXLitQoIC98cYbduLECevcubNly5bNSpcubfPnzw++ZunSpa46/dlnn1mVKlUsY8aMdu2119pPP/0Utu4PPvjAKlWq5CrCJUqUsAkTJoQ9r8dGjhxpHTp0sOzZs1uPHj2sZMmS7rlq1aq5bWj/ZM2aNdakSRPLmzev5ciRw+rXr28//PBD2Pq0/OTJk61169aWOXNmK1OmjH388cdhy/z88892yy23uO3pvd1www22ffv24PN6fYUKFdx7Kl++vL3yyitRHrvDhw/b8uXL7emnn7Ybb7zRihcvbrVr17ZBgwbZbbfdFrbc/fff746t1nvVVVfZp59+mqDjJCtWrHD7nylTJitatKj16dPHfW4AkFIQvgEAQIqiqq1C7erVq10QVxv1XXfdZXXr1nUB96abbrL27dvbyZMnw1732GOPuaCoYJwvXz679dZb7cyZM+6577//3tq0aWP33HOPbdy40YYNG2aDBw+2qVOnhq1j/PjxVrVqVVu3bp17XvsgixYtsr1799rcuXPd/WPHjlnHjh1d4Pzuu+9csG7RooV7PNTw4cPddn/88Uf3fLt27ezQoUPuuT///NPq1avnQu5XX33l9rFLly529uxZ9/zMmTNtyJAhNmrUKNu8ebONHj3a7ZOOT2SyZs3qbvPmzbNTp05Fusz58+etefPmrg397bfftk2bNtnYsWMtTZo0CTpOOmHQrFkzV3nXe509e7Y7Ng8++GAcPnkAuMgFAAAALlEdO3YMtGzZMni/fv36geuvvz54/+zZs4EsWbIE2rdvH3xs7969Af0K9O2337r7S5Yscffffffd4DIHDx4MZMqUKTB79mx3v23btoEmTZqEbfuxxx4LVKxYMXi/ePHigVatWoUts2PHDrfudevWRfs+zp07F8iWLVvgk08+CT6m1z355JPB+8ePH3ePzZ8/390fNGhQoGTJkoHTp09Hus4rr7wy8M4774Q9NnLkyECdOnWi3I85c+YEcuXKFciYMWOgbt26bhsbNmwIPr9gwYJA6tSpA1u2bIn09fE9Tl27dg306NEj7LHly5e7bf37779R7i8AXEqofAMAgBRFreMeVWTz5MljlStXDj6mdmnZv39/2Ovq1KkT/Dl37txWrlw5VzEW/XndddeFLa/7W7dutXPnzgUfq1mzZqz2UWOpu3fv7ireajtX+/Xx48dt165dUb6XLFmyuOW8/dYkbmrTTpcu3QXrV7u2qsldu3YNVrR1e+qpp8La0iNS5XnPnj2uvV2VaLXkV69ePVi51javuOIKK1u2bKSvj+9x2rBhg9tG6L5qojdV2nfs2BHD0QSASwMTrgEAgBQlYhjV2OnQx3RfFOwSmwJybKjlXJOYPf/8825stVrHFf4jTtIW2Xvx9ltjo6OiIC8a737NNdeEPee1iEdF47g1Hl03tYR369bNhg4d6maWj26bCTlO2l+NI9c474g08zoApASEbwAAADM39toLev/884/9+uuvbrIy0Z8RL7el+6oARxdmvVm8Q6u+3ms1+ZnGccvu3bvt77//jtP+qiqu8dsalx4xpKu6X7hwYfvtt9/cOPGEqFixohsH7m3zjz/+cMcmsup3fI+TqusaP67J8AAgpaLtHAAAwMxGjBjhLqulWc5V5dWkbd41xB999FH3nGbpVvBU6H3ppZesf//+0a4zf/78rlr8xRdfuFbzI0eOuMfVbj5jxgzXpr1q1SoXkONaVdZkZEePHnWTm61du9a1dmudW7ZsCU7WNmbMGHvhhRfcPmsCtClTptizzz4b6fpUiW/YsKGbSE2Tnqnd+/3337dx48ZZy5Yt3TKalV2TvKk9feHChW4ZzRyv95eQ4zRw4ED75ptv3HtSa7vey0cffcSEawBSFMI3AACAmZu1++GHH7YaNWrYvn377JNPPglWrlWZfe+99+zdd991l9bSLOIK6wrp0UmbNq0Lv5MmTXKVaC/Evvnmm666rvVq5nW1Wyuox4XGsmuWc7VsKxRrv9Vm7lXB1S6uS40pcGvMu5bRuGrv8mcRaZy1WtQnTpzoArbep9rONTZdATr0UmK1atWye++911XFBwwYEKzsx/c4qaK+bNkyF9g1jl2XZtNrdcwAIKVIpVnXknsnAAAAkosmFdN1rRWGc+bMmdy7AwBIoah8AwAAAADgM8I3AAAAAAA+o+0cAAAAAACfUfkGAAAAAMBnhG8AAAAAAHxG+AYAAAAAwGeEbwAAAAAAfEb4BgAAAADAZ4RvAAAAAAB8RvgGAAAAAMBnhG8AAAAAAHxG+AYAAAAAwPz1/wHEV74kU8+kagAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 500x400 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Task 2: Predictive Analysis using Machine Learning\n", "\n", "# 1. Libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import os\n", "\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.metrics import classification_report, confusion_matrix, accuracy_score\n", "import joblib\n", "\n", "# 2. Load Dataset\n", "df = pd.read_csv(\"../data/TelcoCustomerChurn.csv\")\n", "df.columns = df.columns.str.strip()  # Clean column names\n", "\n", "# 3. Clean Data\n", "df.dropna(inplace=True)\n", "df[\"TotalCharges\"] = pd.to_numeric(df[\"TotalCharges\"], errors=\"coerce\")\n", "df.dropna(subset=[\"TotalCharges\"], inplace=True)\n", "df[\"Churn\"] = df[\"Churn\"].map({\"Yes\": 1, \"No\": 0})\n", "\n", "# 4. Prepare Features & Target\n", "X = df.drop([\"customerID\", \"Churn\"], axis=1)\n", "y = df[\"Churn\"]\n", "X = pd.get_dummies(X, drop_first=True)\n", "\n", "# 5. Train-Test Split\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "\n", "# 6. <PERSON>\n", "model = RandomForestClassifier(n_estimators=100, random_state=42)\n", "model.fit(X_train, y_train)\n", "\n", "# 7. Predictions\n", "y_pred = model.predict(X_test)\n", "\n", "# 8. Evaluation\n", "print(\"Accuracy:\", accuracy_score(y_test, y_pred))\n", "print(\"\\nClassification Report:\\n\", classification_report(y_test, y_pred))\n", "\n", "# 9. Save Evaluation\n", "with open(\"../output/evaluation.txt\", \"w\") as f:\n", "    f.write(\"Accuracy: {}\\n\\n\".format(accuracy_score(y_test, y_pred)))\n", "    f.write(classification_report(y_test, y_pred))\n", "\n", "# 10. Save Model\n", "joblib.dump(model, \"../models/churn_model.pkl\")\n", "\n", "# 11. Feature Importance\n", "importances = pd.Series(model.feature_importances_, index=X.columns)\n", "top_features = importances.sort_values(ascending=False).head(15)\n", "\n", "plt.figure(figsize=(10, 6))\n", "sns.barplot(x=top_features, y=top_features.index, palette=\"viridis\")\n", "plt.title(\"Top 15 Important Features\")\n", "plt.xlabel(\"Importance Score\")\n", "plt.ylabel(\"Feature\")\n", "plt.tight_layout()\n", "plt.savefig(\"../output/feature_importance.png\")\n", "plt.show()\n", "\n", "# 12. Confusion Matrix\n", "plt.figure(figsize=(5, 4))\n", "sns.heatmap(confusion_matrix(y_test, y_pred), annot=True, fmt=\"d\", cmap=\"Blues\")\n", "plt.title(\"Confusion Matrix\")\n", "plt.xlabel(\"Predicted\")\n", "plt.ylabel(\"Actual\")\n", "plt.savefig(\"../output/confusion_matrix.png\")\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 34, "id": "3a1de559-29e9-4a16-a041-8ce391584bff", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["              precision    recall  f1-score   support\n", "\n", "           0       0.83      0.90      0.86      1033\n", "           1       0.63      0.48      0.54       374\n", "\n", "    accuracy                           0.79      1407\n", "   macro avg       0.73      0.69      0.70      1407\n", "weighted avg       0.77      0.79      0.78      1407\n", "\n"]}], "source": ["from sklearn.metrics import classification_report\n", "report = classification_report(y_test, y_pred)\n", "print(report)  # Confirm it's not empty"]}, {"cell_type": "code", "execution_count": 35, "id": "c678d563-06d9-4169-b1a8-dd2116a4172a", "metadata": {}, "outputs": [], "source": ["import os\n", "os.makedirs(\"output\", exist_ok=True)"]}, {"cell_type": "code", "execution_count": 36, "id": "929ff07b-7ab2-47ca-87a8-a87a7b79858c", "metadata": {}, "outputs": [], "source": ["with open(\"../output/evaluation_metrics.txt\", \"w\") as f:\n", "    f.write(\"This is a test.\\n\")"]}, {"cell_type": "code", "execution_count": 37, "id": "afc44c85-78d1-4790-8c1d-283754cff19b", "metadata": {}, "outputs": [], "source": ["from sklearn.metrics import accuracy_score\n", "\n", "report = classification_report(y_test, y_pred)\n", "acc = accuracy_score(y_test, y_pred)\n", "\n", "with open(\"../output/evaluation_metrics.txt\", \"w\") as f:\n", "    f.write(f\"Accuracy: {acc:.4f}\\n\\n\")\n", "    f.write(\"Classification Report:\\n\")\n", "    f.write(report)"]}, {"cell_type": "code", "execution_count": 38, "id": "d884a7ac-0459-407a-86d3-ef4d9ed13c8a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["REPORT:\n", "               precision    recall  f1-score   support\n", "\n", "           0       0.83      0.90      0.86      1033\n", "           1       0.63      0.48      0.54       374\n", "\n", "    accuracy                           0.79      1407\n", "   macro avg       0.73      0.69      0.70      1407\n", "weighted avg       0.77      0.79      0.78      1407\n", "\n"]}], "source": ["print(\"REPORT:\\n\", report)"]}, {"cell_type": "code", "execution_count": null, "id": "0d544426-c95e-4017-9986-b3a6ed7ae5f5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}