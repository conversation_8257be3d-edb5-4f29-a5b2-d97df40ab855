# Telco Customer Churn Prediction 📞

**COMPANY:** CODTECH IT SOLUTIONS  
**NAME:** BIBI AMINA  
**INTERN ID:** CT06DM754  
**DOMAIN:** DATA ANALYTICS  
**DURATION:** 6 WEEKS  
**MENTOR:** NEELA SANTOSH  

## 🎯 Project Overview

A comprehensive machine learning project that predicts customer churn for a telecommunications company using advanced predictive modeling techniques. The project analyzes customer behavior patterns, service usage, and demographic data to identify customers at risk of churning, enabling proactive retention strategies.

## 📊 Dataset Information

### 📋 **Dataset Details**

- **File**: `TelcoCustomerChurn.csv`
- **Source**: Telco Customer Churn Dataset
- **Records**: 7,043 customer records
- **Features**: 21 attributes including demographics, services, and account information
- **Target Variable**: Churn (Yes/No)

### Key Data Fields:

- **Customer Demographics**: Gender, Age, Senior Citizen status
- **Service Information**: Phone service, Internet service, Online security
- **Account Details**: Contract type, Payment method, Monthly charges
- **Usage Patterns**: Tenure, Total charges, Multiple lines
- **Target**: Churn status (binary classification)

## 🛠️ Technology Stack

### Machine Learning
- **Scikit-learn**: Model training and evaluation
- **Random Forest**: Primary classification algorithm
- **Logistic Regression**: Baseline model comparison
- **XGBoost**: Advanced gradient boosting (optional)

### Data Processing
- **Pandas**: Data manipulation and analysis
- **NumPy**: Numerical computations
- **Feature Engineering**: Encoding, scaling, selection

### Visualization
- **Matplotlib**: Static plotting
- **Seaborn**: Statistical visualizations
- **Plotly**: Interactive charts (optional)

### Model Persistence
- **Joblib/Pickle**: Model serialization

## 🚀 Getting Started

### Prerequisites
- Python 3.7 or higher
- 2GB+ RAM recommended
- Jupyter Notebook (optional)

### Installation

1. **Navigate to project directory**
   ```bash
   cd TelcoCustomerChurn_PredictiveML
   ```

2. **Create virtual environment**
   ```bash
   python -m venv churn_env
   source churn_env/bin/activate  # On Windows: churn_env\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

### Running the Analysis

#### Option 1: Python Script
```bash
python scripts/churn_model.py
```

#### Option 2: Jupyter Notebook
```bash
jupyter notebook notebooks/churn_prediction.ipynb
```

## 📈 Key Features

- **Comprehensive EDA**: Exploratory data analysis with statistical insights
- **Feature Engineering**: Advanced preprocessing and feature selection
- **Multiple Algorithms**: Comparison of different ML models
- **Hyperparameter Tuning**: Grid search for optimal parameters
- **Model Evaluation**: Comprehensive metrics and validation
- **Feature Importance**: Analysis of key churn predictors

## 📊 Model Performance

### Target Metrics
- **Accuracy**: 85%+ target
- **Precision**: 80%+ for churn prediction
- **Recall**: 75%+ for churn detection
- **F1-Score**: 80%+ overall
- **AUC-ROC**: 0.85+ for model discrimination

### Evaluation Methods
- **Train-Test Split**: 80-20 split with stratification
- **Cross-Validation**: 5-fold validation
- **Confusion Matrix**: Visual performance analysis
- **Classification Report**: Detailed metrics per class
- **ROC Curves**: Threshold optimization

## 📁 Project Structure

```
TelcoCustomerChurn_PredictiveML/
├── data/
│   └── TelcoCustomerChurn.csv        # Customer dataset
├── scripts/
│   └── churn_model.py                # Main ML pipeline
├── notebooks/
│   └── churn_prediction.ipynb        # Interactive analysis
├── models/
│   ├── churn_model.pkl               # Trained model
│   └── model.pkl                     # Alternative model
├── output/
│   ├── churn_distribution.png        # Data visualizations
│   ├── confusion_matrix.png          # Model evaluation
│   ├── feature_importance.png        # Feature analysis
│   ├── evaluation.txt                # Performance metrics
│   └── evaluation_metrics.txt        # Detailed results
├── requirements.txt                  # Dependencies
└── README.md                         # This file
```

## 📸 Output Images

The following output images are generated from the churn prediction analysis:

![Churn Distribution](output/churn_distribution.png)
*Distribution of churned vs retained customers in the dataset*

![Confusion Matrix](output/confusion_matrix.png)
*Model performance evaluation showing prediction accuracy*

![Feature Importance](output/feature_importance.png)
*Key features that influence customer churn decisions*

**Additional Output Files:**
- `output/evaluation.txt` - Model performance summary
- `output/evaluation_metrics.txt` - Detailed classification metrics
- `models/churn_model.pkl` - Trained model for deployment

## 🎯 Key Insights

### Top Churn Predictors
- **Contract Type**: Month-to-month contracts show higher churn
- **Tenure**: New customers (< 12 months) are at higher risk
- **Monthly Charges**: Higher charges correlate with increased churn
- **Payment Method**: Electronic check users churn more frequently
- **Internet Service**: Fiber optic customers show different patterns

### Business Recommendations
- **Retention Programs**: Target month-to-month contract customers
- **Pricing Strategy**: Review pricing for high-charge customers
- **Service Quality**: Improve fiber optic service satisfaction
- **Payment Options**: Encourage automatic payment methods
- **Early Engagement**: Focus on new customer onboarding

## 🔧 Usage Examples

### Quick Prediction
```python
import joblib
import pandas as pd

# Load trained model
model = joblib.load('models/churn_model.pkl')

# Predict churn for new customer
customer_data = pd.DataFrame({
    'tenure': [12],
    'MonthlyCharges': [70.0],
    'TotalCharges': [840.0],
    'Contract': ['Month-to-month'],
    # ... other features
})

prediction = model.predict(customer_data)
probability = model.predict_proba(customer_data)

print(f"Churn Prediction: {prediction[0]}")
print(f"Churn Probability: {probability[0][1]:.2%}")
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/NewFeature`)
3. Commit your changes (`git commit -m 'Add new feature'`)
4. Push to the branch (`git push origin feature/NewFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Telco Dataset**: For providing comprehensive customer data
- **Scikit-learn Team**: For the excellent machine learning library
- **Open Source Community**: For inspiration and best practices
- **CodTech IT Solutions**: For internship opportunity and guidance

---

### 📬 Contact

**Intern**: Bibi Amina  
**Project**: CodTech Internship – Data Analytics  
**Task**: Telco Customer Churn Prediction with Machine Learning
