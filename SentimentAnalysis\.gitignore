# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# Jupyter Notebook
.ipynb_checkpoints

# Large Data files (excluded due to GitHub size limits)
*.csv
data/*.csv
data/sentimentdataset.csv

# Note: Download dataset from Kaggle or other sources
# See README.md for download instructions

# spaCy models and trained models
*.pkl
output/*.pkl
models/*.pkl

# Output files
output/*.png
output/*.txt

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
*.log

# Word clouds
*.png
