import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score, roc_auc_score, roc_curve
import joblib
import warnings
warnings.filterwarnings('ignore')

class TelcoChurnPredictor:
    def __init__(self):
        self.model = None
        self.scaler = StandardScaler()
        self.label_encoders = {}
        self.feature_names = None

    def load_data(self, data_path):
        """Load and initial data exploration"""
        print("Loading Telco Customer Churn dataset...")

        if not os.path.exists(data_path):
            raise FileNotFoundError(f"Data file not found: {data_path}")

        self.df = pd.read_csv(data_path)
        print(f"Dataset shape: {self.df.shape}")
        print(f"Columns: {list(self.df.columns)}")

        # Display basic info
        print("\nDataset Info:")
        print(self.df.info())
        print(f"\nMissing values:\n{self.df.isnull().sum()}")
        print(f"\nChurn distribution:\n{self.df['Churn'].value_counts()}")

        return self.df

    def preprocess_data(self):
        """Clean and preprocess the data"""
        print("\nPreprocessing data...")

        # Handle TotalCharges column (convert to numeric)
        self.df['TotalCharges'] = pd.to_numeric(self.df['TotalCharges'], errors='coerce')

        # Fill missing values
        self.df['TotalCharges'].fillna(self.df['TotalCharges'].median(), inplace=True)

        # Convert binary categorical variables
        binary_cols = ['Partner', 'Dependents', 'PhoneService', 'PaperlessBilling', 'Churn']
        for col in binary_cols:
            if col in self.df.columns:
                self.df[col] = self.df[col].map({'Yes': 1, 'No': 0})

        # Handle gender
        if 'gender' in self.df.columns:
            self.df['gender'] = self.df['gender'].map({'Male': 1, 'Female': 0})

        # Encode categorical variables
        categorical_cols = ['MultipleLines', 'InternetService', 'OnlineSecurity', 'OnlineBackup',
                          'DeviceProtection', 'TechSupport', 'StreamingTV', 'StreamingMovies',
                          'Contract', 'PaymentMethod']

        for col in categorical_cols:
            if col in self.df.columns:
                le = LabelEncoder()
                self.df[col] = le.fit_transform(self.df[col].astype(str))
                self.label_encoders[col] = le

        # Drop customerID as it's not useful for prediction
        if 'customerID' in self.df.columns:
            self.df.drop('customerID', axis=1, inplace=True)

        print("Preprocessing completed!")
        return self.df

    def create_visualizations(self, output_dir):
        """Create exploratory data analysis visualizations"""
        print("\nCreating visualizations...")
        os.makedirs(output_dir, exist_ok=True)

        plt.style.use('seaborn-v0_8')

        # 1. Churn distribution
        plt.figure(figsize=(8, 6))
        churn_counts = self.df['Churn'].value_counts()
        plt.pie(churn_counts.values, labels=['No Churn', 'Churn'], autopct='%1.1f%%', startangle=90)
        plt.title('Customer Churn Distribution')
        plt.savefig(f'{output_dir}/churn_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()

        # 2. Correlation heatmap
        plt.figure(figsize=(12, 10))
        correlation_matrix = self.df.corr()
        sns.heatmap(correlation_matrix, annot=False, cmap='coolwarm', center=0)
        plt.title('Feature Correlation Heatmap')
        plt.tight_layout()
        plt.savefig(f'{output_dir}/correlation_heatmap.png', dpi=300, bbox_inches='tight')
        plt.close()

        print(f"Visualizations saved to {output_dir}/")

    def prepare_features(self):
        """Prepare features and target for modeling"""
        print("\nPreparing features for modeling...")

        # Separate features and target
        X = self.df.drop('Churn', axis=1)
        y = self.df['Churn']

        self.feature_names = X.columns.tolist()

        # Split the data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )

        # Scale the features
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)

        print(f"Training set size: {X_train_scaled.shape}")
        print(f"Test set size: {X_test_scaled.shape}")

        return X_train_scaled, X_test_scaled, y_train, y_test

    def train_model(self, X_train, y_train):
        """Train the churn prediction model"""
        print("\nTraining Random Forest model...")

        # Define parameter grid for hyperparameter tuning
        param_grid = {
            'n_estimators': [100, 200],
            'max_depth': [10, 20, None],
            'min_samples_split': [2, 5],
            'min_samples_leaf': [1, 2]
        }

        # Create Random Forest classifier
        rf = RandomForestClassifier(random_state=42)

        # Perform grid search
        grid_search = GridSearchCV(
            rf, param_grid, cv=5, scoring='roc_auc', n_jobs=-1, verbose=1
        )

        grid_search.fit(X_train, y_train)

        # Get the best model
        self.model = grid_search.best_estimator_

        print(f"Best parameters: {grid_search.best_params_}")
        print(f"Best cross-validation score: {grid_search.best_score_:.4f}")

        return self.model

    def evaluate_model(self, X_test, y_test, output_dir):
        """Evaluate the trained model"""
        print("\nEvaluating model...")

        # Make predictions
        y_pred = self.model.predict(X_test)
        y_pred_proba = self.model.predict_proba(X_test)[:, 1]

        # Calculate metrics
        accuracy = accuracy_score(y_test, y_pred)
        roc_auc = roc_auc_score(y_test, y_pred_proba)

        print(f"Accuracy: {accuracy:.4f}")
        print(f"ROC AUC: {roc_auc:.4f}")

        # Classification report
        class_report = classification_report(y_test, y_pred)
        print(f"\nClassification Report:\n{class_report}")

        # Save evaluation metrics
        with open(f'{output_dir}/evaluation_metrics.txt', 'w') as f:
            f.write(f"Model Evaluation Metrics\n")
            f.write(f"========================\n")
            f.write(f"Accuracy: {accuracy:.4f}\n")
            f.write(f"ROC AUC: {roc_auc:.4f}\n\n")
            f.write(f"Classification Report:\n{class_report}")

        # Confusion Matrix
        cm = confusion_matrix(y_test, y_pred)
        plt.figure(figsize=(8, 6))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')
        plt.title('Confusion Matrix')
        plt.ylabel('Actual')
        plt.xlabel('Predicted')
        plt.savefig(f'{output_dir}/confusion_matrix.png', dpi=300, bbox_inches='tight')
        plt.close()

        # Feature importance
        feature_importance = pd.DataFrame({
            'feature': self.feature_names,
            'importance': self.model.feature_importances_
        }).sort_values('importance', ascending=False)

        plt.figure(figsize=(10, 8))
        sns.barplot(data=feature_importance.head(15), x='importance', y='feature')
        plt.title('Top 15 Feature Importances')
        plt.xlabel('Importance')
        plt.tight_layout()
        plt.savefig(f'{output_dir}/feature_importance.png', dpi=300, bbox_inches='tight')
        plt.close()

        print(f"Evaluation results saved to {output_dir}/")
        return accuracy, roc_auc

    def save_model(self, model_dir):
        """Save the trained model and preprocessors"""
        os.makedirs(model_dir, exist_ok=True)

        # Save the model
        joblib.dump(self.model, f'{model_dir}/churn_model.pkl')
        joblib.dump(self.scaler, f'{model_dir}/scaler.pkl')
        joblib.dump(self.label_encoders, f'{model_dir}/label_encoders.pkl')

        print(f"Model saved to {model_dir}/")

def main():
    """Main execution function"""
    print("=== Telco Customer Churn Prediction ===\n")

    # Initialize predictor
    predictor = TelcoChurnPredictor()

    # Paths
    data_path = "data/TelcoCustomerChurn.csv"
    output_dir = "output"
    model_dir = "models"

    try:
        # Load and preprocess data
        predictor.load_data(data_path)
        predictor.preprocess_data()

        # Create visualizations
        predictor.create_visualizations(output_dir)

        # Prepare features
        X_train, X_test, y_train, y_test = predictor.prepare_features()

        # Train model
        predictor.train_model(X_train, y_train)

        # Evaluate model
        accuracy, roc_auc = predictor.evaluate_model(X_test, y_test, output_dir)

        # Save model
        predictor.save_model(model_dir)

        print(f"\n=== FINAL RESULTS ===")
        print(f"Model Accuracy: {accuracy:.4f}")
        print(f"ROC AUC Score: {roc_auc:.4f}")
        print(f"Model training completed successfully!")

    except Exception as e:
        print(f"Error occurred: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()