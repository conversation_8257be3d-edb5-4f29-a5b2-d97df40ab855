# NLP Sentiment Analysis 💬

**COMPANY:** CODTECH IT SOLUTIONS
**NAME:** BIBI AMINA
**INTERN ID:** CT06DM754
**DOMAIN:** DATA ANALYTICS
**DURATION:** 6 WEEKS
**MENTOR:** NEELA SANTOSH

## 🎯 Project Overview

Build a comprehensive NLP machine learning model to predict sentiment (positive/negative/neutral) from textual data using advanced text processing and classification techniques.

## 📊 Dataset Information

### 📥 **Dataset Download Required**

⚠️ **Note**: Due to GitHub file size limitations, the dataset is not included in this repository.

**🔗 Download Instructions:**

1. **Primary Source**: [Sentiment140 Dataset](https://www.kaggle.com/datasets/kazanova/sentiment140)
2. **Alternative Sources**:
   - [Twitter Sentiment Analysis Dataset](https://www.kaggle.com/datasets/jp797498e/twitter-entity-sentiment-analysis)
   - [Amazon Product Reviews](https://www.kaggle.com/datasets/bittlingmayer/amazonreviews)
   - [IMDB Movie Reviews](https://www.kaggle.com/datasets/lakshmi25npathi/imdb-dataset-of-50k-movie-reviews)

### 🔧 **Setup Instructions**

```bash
# Create data directory (if not exists)
mkdir data

# Download and place the dataset
# File should be: data/sentimentdataset.csv

# Verify file exists before running analysis
ls data/sentimentdataset.csv
```

### 📋 **Dataset Details**

- **File**: `sentimentdataset.csv`
- **Format**: CSV with text and sentiment labels
- **Size**: Variable (depends on chosen dataset)
- **Labels**: Positive, Negative, Neutral (or binary)

## 🛠️ Technology Stack

### Machine Learning & NLP

- **Scikit-learn**: Model training and evaluation
- **spaCy**: Advanced NLP processing
- **NLTK**: Natural language toolkit
- **TextBlob**: Sentiment analysis utilities

### Data Processing

- **Pandas**: Data manipulation and analysis
- **NumPy**: Numerical computations
- **TF-IDF Vectorizer**: Feature extraction

### Visualization

- **Matplotlib**: Static plotting
- **Seaborn**: Statistical visualizations
- **WordCloud**: Text visualization

### Model Persistence

- **Joblib**: Model serialization

## 🚀 Execution Steps

1. **Install dependencies**: `pip install -r requirements.txt`
2. **Download spaCy model**: `python -m spacy download en_core_web_sm`
3. **Run notebook**: `jupyter notebook notebooks/sentiment_analysis.ipynb`
4. **Or run script**: `python scripts/sentiment_model.py`

## 📈 Key Features

- **Advanced Text Preprocessing**: URL removal, tokenization, lemmatization
- **Feature Engineering**: TF-IDF vectorization with optimal parameters
- **Machine Learning**: Logistic Regression with hyperparameter tuning
- **Comprehensive Evaluation**: Classification metrics, confusion matrix, ROC curves
- **Model Persistence**: Save trained model for deployment

## 📁 Project Structure

```
SentimentAnalysis/
├── data/
│   └── sentimentdataset.csv          # Dataset (download required)
├── scripts/
│   └── sentiment_model.py            # Main ML pipeline
├── notebooks/
│   └── sentiment_analysis.ipynb      # Interactive analysis
├── output/
│   ├── confusion_matrix.png          # Model evaluation
│   ├── classification_report.txt     # Performance metrics
│   └── sentiment_model.pkl           # Trained model
├── requirements.txt                  # Dependencies
└── README.md                         # This file
```

## 🎯 Expected Outcomes

- **High-accuracy sentiment classifier** (85%+ accuracy target)
- **Comprehensive model evaluation** with multiple metrics
- **Deployment-ready model** saved as pickle file
- **Professional documentation** and code quality

## 📊 Model Performance Metrics

### Target Metrics

- **Accuracy**: 85%+ target
- **Precision**: 80%+ for each class
- **Recall**: 80%+ for each class
- **F1-Score**: 80%+ overall

### Evaluation Methods

- **Cross-validation**: 5-fold stratified validation
- **Confusion Matrix**: Visual performance analysis
- **Classification Report**: Detailed metrics per class
- **ROC Curves**: Threshold optimization

## 🚀 Getting Started

### Prerequisites

- Python 3.7 or higher
- 2GB+ RAM recommended
- Internet connection for downloading models

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd SentimentAnalysis
   ```

2. **Create virtual environment**

   ```bash
   python -m venv sentiment_env
   source sentiment_env/bin/activate  # On Windows: sentiment_env\Scripts\activate
   ```

3. **Install dependencies**

   ```bash
   pip install -r requirements.txt
   ```

4. **Download spaCy model**
   ```bash
   python -m spacy download en_core_web_sm
   ```

## 🔧 Usage Examples

### Quick Prediction

```python
import joblib
import pandas as pd

# Load trained model
model = joblib.load('output/sentiment_model.pkl')

# Predict sentiment
text = "I love this product!"
prediction = model.predict([text])
print(f"Sentiment: {prediction[0]}")
```

### Batch Processing

```python
# Load and predict multiple texts
texts = ["Great service!", "Terrible experience", "It's okay"]
predictions = model.predict(texts)
```

## 📸 Output Images

The following output images are generated from the sentiment analysis:

![Confusion Matrix](output/confusion_matrix.png)
_Model performance evaluation showing prediction accuracy across sentiment classes_

**Additional Output Files:**

- `output/classification_report.txt` - Detailed performance metrics
- `output/sentiment_model.pkl` - Trained model for deployment
- `output/vectorizer.pkl` - Text vectorizer for preprocessing

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/NewFeature`)
3. Commit your changes (`git commit -m 'Add new feature'`)
4. Push to the branch (`git push origin feature/NewFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Kaggle Community**: For providing excellent datasets
- **Scikit-learn Team**: For the comprehensive ML library
- **spaCy Team**: For advanced NLP capabilities
- **Open Source Community**: For inspiration and best practices

---

### 📬 Contact

**Intern**: Bibi Amina
**Project**: CodTech Internship – Data Analytics
**Task**: NLP Sentiment Analysis with Machine Learning
