{"cells": [{"cell_type": "code", "execution_count": null, "id": "4aa69241", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["from pyspark.sql import SparkSession\n", "\n", "spark = SparkSession.builder \\\n", "    .appName(\"YellowTaxiAnalysis\") \\\n", "    .getOrCreate()"]}, {"cell_type": "code", "execution_count": null, "id": "b5be40ad", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["df = spark.read.parquet(\"../data/yellow_taxi_trip_2023.parquet\")\n", "df.printSchema()\n", "df.show(5)"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}