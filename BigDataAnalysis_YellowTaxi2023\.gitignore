# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# Jupyter Notebook
.ipynb_checkpoints

# Spark
metastore_db/
derby.log
spark-warehouse/

# Large Data files (excluded due to GitHub size limits)
*.parquet
*.csv
data/*.parquet
data/*.csv
data/yellow_taxi_trip_2023.parquet

# Note: Download dataset from NYC TLC website
# See README.md for download instructions

# Output files
output/*.png
output/*.csv
output/*.txt

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
*.log
