# Big Data Analysis - NYC Yellow Taxi 2023 🚕

**COMPANY:** CODTECH IT SOLUTIONS
**NAME:** BIBI AMINA
**INTERN ID:** CT06DM754
**DOMAIN:** DATA ANALYTICS
**DURATION:** 6 WEEKS
**MENTOR:** NEELA SANTOSH

## 📋 Project Overview

This project performs comprehensive big data analysis on NYC Yellow Taxi trip data from 2023 using Apache Spark. The analysis provides insights into taxi usage patterns, fare structures, and temporal trends to help understand urban transportation dynamics.

## 🎯 Objectives

- Analyze large-scale taxi trip data using distributed computing (PySpark)
- Identify peak usage hours and passenger patterns
- Examine fare distribution and pricing trends
- Generate actionable insights for taxi operations optimization

## 📊 Dataset

### 📥 **Dataset Download Required**

⚠️ **Note**: Due to GitHub file size limitations (45.4MB), the dataset is not included in this repository.

**🔗 Download Instructions:**

1. **Primary Source**: [NYC TLC Trip Record Data](https://www.nyc.gov/site/tlc/about/tlc-trip-record-data.page)

   - Navigate to **"Yellow Taxi Trip Records"**
   - Download **January 2023** data (`yellow_tripdata_2023-01.parquet`)
   - Place file in `data/` folder as `yellow_taxi_trip_2023.parquet`

2. **Alternative Sources**:
   - [Kaggle NYC Taxi Dataset](https://www.kaggle.com/datasets/elemento/nyc-yellow-taxi-trip-data)
   - [AWS Open Data Registry](https://registry.opendata.aws/nyc-tlc-trip-records-pds/)

### 📋 **Dataset Information**

- **File**: `yellow_taxi_trip_2023.parquet` (January 2023)
- **Source**: NYC Taxi & Limousine Commission (TLC)
- **Size**: ~2.3M records (45.4MB)
- **Format**: Parquet (optimized for big data processing)

### 🔧 **Setup Instructions**

```bash
# Create data directory (if not exists)
mkdir data

# Download and place the dataset
# File should be: data/yellow_taxi_trip_2023.parquet

# Verify file exists before running analysis
ls data/yellow_taxi_trip_2023.parquet
```

### Key Columns:

- `tpep_pickup_datetime`: Trip start time
- `tpep_dropoff_datetime`: Trip end time
- `passenger_count`: Number of passengers
- `trip_distance`: Distance traveled
- `fare_amount`: Base fare
- `total_amount`: Total charge including tips and fees

## 🛠️ Technology Stack

- **Apache Spark (PySpark)**: Distributed data processing
- **Pandas**: Data manipulation and analysis
- **Matplotlib & Seaborn**: Data visualization
- **Jupyter Notebook**: Interactive development
- **Python 3.8+**: Programming language

## 🚀 Getting Started

### Prerequisites

- Python 3.8 or higher
- Java 8 or 11 (required for Spark)
- At least 4GB RAM recommended

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd BigDataAnalysis_YellowTaxi2023
   ```

2. **Create virtual environment**

   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**

   ```bash
   pip install -r requirements.txt
   ```

4. **Download dataset** (if not included)
   - Visit [NYC TLC Trip Record Data](https://www.nyc.gov/site/tlc/about/tlc-trip-record-data.page)
   - Download January 2023 Yellow Taxi data
   - Place in `data/` directory

### Running the Analysis

#### Option 1: Command Line

```bash
python scripts/analysis.py
```

#### Option 2: Jupyter Notebook

```bash
jupyter notebook notebook/bigdata_analysis.ipynb
```

## 📈 Key Findings

### 🕐 Temporal Patterns

- **Peak Hours**: 5 PM - 8 PM (evening rush hour)
- **Lowest Activity**: 4 AM - 6 AM
- **Weekend vs Weekday**: Different usage patterns observed

### 👥 Passenger Analysis

- **Average Fare by Passenger Count**:
  - 1 passenger: $13.52
  - 2 passengers: $15.23
  - 3+ passengers: $17.89
- **Most Common**: Single passenger trips (68%)

### 💰 Fare Insights

- **Average Fare**: $14.75
- **Median Trip Distance**: 1.8 miles
- **Fare per Mile**: ~$8.19 average

## 📊 Visualizations

The analysis generates several visualizations:

1. **Trips by Hour**: `output/trips_by_hour.png`
   ![Trips by Hour](images/trips_by_hour.png)

2. **Fare by Passenger Count**: `output/fare_by_passenger.png`

3. **Trip Distance Distribution**: Generated during analysis

## 📸 Output Images

The following output images are available from the analysis:

### Trips by Hour Analysis

![Trips by Hour Analysis](images/trips_by_hour.png)
_Hourly trip distribution showing peak usage patterns throughout the day_

**Available Output Files:**

- `images/trips_by_hour.png` - Visualization of taxi trip patterns by hour
- `output/insights.csv` - Processed analysis results and insights

## 📁 Project Structure

```
BigDataAnalysis_YellowTaxi2023/
├── data/
│   └── yellow_taxi_trip_2023.parquet
├── scripts/
│   └── analysis.py                    # Main analysis script
├── notebook/
│   └── bigdata_analysis.ipynb         # Jupyter notebook
├── output/
│   ├── fare_by_passenger.csv         # Analysis results
│   ├── trips_by_hour.csv
│   ├── trips_by_hour.png             # Visualizations
│   └── fare_by_passenger.png
├── images/
│   └── trips_by_hour.png             # Sample outputs
├── requirements.txt                   # Dependencies
└── README.md                         # This file
```

## 🔧 Configuration

The analysis script includes several configurable parameters:

- Spark session configuration for memory optimization
- Data filtering thresholds
- Visualization styling options

## 📝 Output Files

- `output/fare_by_passenger.csv`: Average fare by passenger count
- `output/trips_by_hour.csv`: Trip counts and average fares by hour
- `output/*.png`: Generated visualizations

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **NYC TLC**: For providing comprehensive taxi trip datasets
- **Apache Spark Community**: For excellent documentation and support
- **Python Data Science Ecosystem**: pandas, matplotlib, seaborn contributors
- **Open Source Community**: For inspiration and best practices

---

### 📬 Contact

**Intern**: Bibi Amina
**Project**: CodTech Internship – Data Analytics
**Task**: Big Data Analysis with Apache Spark
