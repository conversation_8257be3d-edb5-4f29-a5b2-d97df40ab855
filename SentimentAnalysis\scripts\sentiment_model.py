import pandas as pd
import re
from sklearn.model_selection import train_test_split
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import classification_report, confusion_matrix

# Try to import spaCy, but make it optional
try:
    import spacy
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False
    print("WARNING: spaCy not available. Using basic text processing.")

# Load spaCy language model with error handling
def load_spacy_model():
    """Load spaCy model with fallback options"""
    if not SPACY_AVAILABLE:
        return None

    try:
        nlp = spacy.load("en_core_web_sm", disable=["ner", "parser"])
        print("SUCCESS: spaCy model loaded successfully")
        return nlp
    except OSError:
        print("WARNING: spaCy model not found. Please install it using:")
        print("python -m spacy download en_core_web_sm")
        print("Using basic text processing instead...")
        return None

nlp = load_spacy_model()

# Load dataset
import os

def load_sentiment_data():
    """Load sentiment dataset with proper error handling"""
    data_paths = [
        "data/sentimentdataset.csv",
        "../data/sentimentdataset.csv",
        "data/sentiment_data.csv"
    ]

    for path in data_paths:
        if os.path.exists(path):
            try:
                df = pd.read_csv(path)
                print(f"SUCCESS: Data loaded from: {path}")
                return df
            except Exception as e:
                print(f"ERROR: Error loading {path}: {e}")
                continue

    # If no data file found, create sample data for testing
    print("WARNING: No data file found. Creating sample data for testing...")
    sample_data = {
        'text': [
            'I love this product!',
            'This is terrible',
            'Great service',
            'Bad experience',
            'Amazing quality',
            'Poor customer support',
            'Excellent value for money',
            'Waste of time',
            'Highly recommended',
            'Not satisfied'
        ],
        'Sentiment': ['positive', 'negative', 'positive', 'negative', 'positive',
                     'negative', 'positive', 'negative', 'positive', 'negative']
    }
    return pd.DataFrame(sample_data)

df = load_sentiment_data()

# Use correct text column name
df.rename(columns={"Text": "text"}, inplace=True)

# Define preprocessing function
def clean_text(text):
    """Clean text with or without spaCy"""
    text = re.sub(r"http\S+|@\S+|#\S+|[^A-Za-z\s]", "", str(text).lower())

    if nlp is not None:
        # Use spaCy for advanced processing
        doc = nlp(text)
        return " ".join([token.lemma_ for token in doc if token.is_alpha and not token.is_stop])
    else:
        # Basic processing without spaCy
        from sklearn.feature_extraction.text import ENGLISH_STOP_WORDS
        words = text.split()
        return " ".join([word for word in words if word not in ENGLISH_STOP_WORDS and word.isalpha()])

# Apply preprocessing
df["cleaned"] = df["text"].apply(clean_text)

# Check class distribution
print("Number of samples:", len(df))
print("Number of unique sentiment classes:", df["Sentiment"].nunique())
print(df["Sentiment"].value_counts())

# Optional: Reduce number of sentiment classes if needed
top_sentiments = df["Sentiment"].value_counts().nlargest(5).index
df = df[df["Sentiment"].isin(top_sentiments)]

# Split dataset
X_train, X_test, y_train, y_test = train_test_split(df["cleaned"], df["Sentiment"], test_size=0.2, random_state=42)

# Vectorization
vectorizer = TfidfVectorizer(max_features=5000)
X_train_vec = vectorizer.fit_transform(X_train)
X_test_vec = vectorizer.transform(X_test)

# Train model
model = LogisticRegression(max_iter=1000)
model.fit(X_train_vec, y_train)

# Evaluate model
y_pred = model.predict(X_test_vec)
print("Classification Report:\n", classification_report(y_test, y_pred))
print("Confusion Matrix:\n", confusion_matrix(y_test, y_pred))

# Save results
import joblib
import matplotlib.pyplot as plt
import seaborn as sns

def save_results():
    """Save model and evaluation results"""
    try:
        # Create output directory
        os.makedirs("output", exist_ok=True)

        # Save model and vectorizer
        joblib.dump(model, "output/sentiment_model.pkl")
        joblib.dump(vectorizer, "output/vectorizer.pkl")

        # Save classification report
        with open("output/classification_report.txt", "w") as f:
            f.write("Sentiment Analysis Model Evaluation\n")
            f.write("=====================================\n\n")
            f.write("Classification Report:\n")
            f.write(classification_report(y_test, y_pred))
            f.write("\n\nConfusion Matrix:\n")
            f.write(str(confusion_matrix(y_test, y_pred)))

        # Create and save confusion matrix plot
        plt.figure(figsize=(8, 6))
        cm = confusion_matrix(y_test, y_pred)
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')
        plt.title('Confusion Matrix - Sentiment Analysis')
        plt.ylabel('Actual')
        plt.xlabel('Predicted')
        plt.tight_layout()
        plt.savefig("output/confusion_matrix.png", dpi=300, bbox_inches='tight')
        plt.close()

        print("SUCCESS: Results saved to output/ directory")
        print("Files created:")
        print("   - sentiment_model.pkl")
        print("   - vectorizer.pkl")
        print("   - classification_report.txt")
        print("   - confusion_matrix.png")

    except Exception as e:
        print(f"ERROR: Error saving results: {e}")

# Save all results
save_results()

print("\nSUCCESS: Sentiment Analysis completed successfully!")
