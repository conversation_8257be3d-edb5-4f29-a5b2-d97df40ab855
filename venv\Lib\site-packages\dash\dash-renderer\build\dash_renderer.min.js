/*! For license information please see dash_renderer.min.js.LICENSE.txt */
!function(){var t={56:function(t,r,e){"use strict";t.exports=function(t){var r=e.nc;r&&t.setAttribute("nonce",r)}},63:function(t,r,e){"use strict";var n=e(609),o="function"==typeof Object.is?Object.is:function(t,r){return t===r&&(0!==t||1/t==1/r)||t!=t&&r!=r},i=n.useState,a=n.useEffect,u=n.useLayoutEffect,c=n.useDebugValue;function s(t){var r=t.getSnapshot;t=t.value;try{var e=r();return!o(t,e)}catch(t){return!0}}var l="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(t,r){return r()}:function(t,r){var e=r(),n=i({inst:{value:e,getSnapshot:r}}),o=n[0].inst,l=n[1];return u((function(){o.value=e,o.getSnapshot=r,s(o)&&l({inst:o})}),[t,e,r]),a((function(){return s(o)&&l({inst:o}),t((function(){s(o)&&l({inst:o})}))}),[t]),c(e),e};r.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:l},72:function(t){"use strict";var r=[];function e(t){for(var e=-1,n=0;n<r.length;n++)if(r[n].identifier===t){e=n;break}return e}function n(t,n){for(var i={},a=[],u=0;u<t.length;u++){var c=t[u],s=n.base?c[0]+n.base:c[0],l=i[s]||0,f="".concat(s," ").concat(l);i[s]=l+1;var p=e(f),y={css:c[1],media:c[2],sourceMap:c[3],supports:c[4],layer:c[5]};if(-1!==p)r[p].references++,r[p].updater(y);else{var h=o(y,n);n.byIndex=u,r.splice(u,0,{identifier:f,updater:h,references:1})}a.push(f)}return a}function o(t,r){var e=r.domAPI(r);return e.update(t),function(r){if(r){if(r.css===t.css&&r.media===t.media&&r.sourceMap===t.sourceMap&&r.supports===t.supports&&r.layer===t.layer)return;e.update(t=r)}else e.remove()}}t.exports=function(t,o){var i=n(t=t||[],o=o||{});return function(t){t=t||[];for(var a=0;a<i.length;a++){var u=e(i[a]);r[u].references--}for(var c=n(t,o),s=0;s<i.length;s++){var l=e(i[s]);0===r[l].references&&(r[l].updater(),r.splice(l,1))}i=c}}},113:function(t){"use strict";t.exports=function(t,r){if(r.styleSheet)r.styleSheet.cssText=t;else{for(;r.firstChild;)r.removeChild(r.firstChild);r.appendChild(document.createTextNode(t))}}},131:function(t,r){function e(t,r,e,n){var i={};return function(a){if(!i[a]){var u={},c=[],s=[];for(s.push({node:a,processed:!1});s.length>0;){var l=s[s.length-1],f=l.processed,p=l.node;if(f)s.pop(),c.pop(),u[p]=!1,i[p]=!0,r&&0!==t[p].length||e.push(p);else{if(i[p]){s.pop();continue}if(u[p]){if(n){s.pop();continue}throw c.push(p),new o(c)}u[p]=!0,c.push(p);for(var y=t[p],h=y.length-1;h>=0;h--)s.push({node:y[h],processed:!1});l.processed=!0}}}}}var n=r.w=function(t){this.nodes={},this.outgoingEdges={},this.incomingEdges={},this.circular=t&&!!t.circular};n.prototype={size:function(){return Object.keys(this.nodes).length},addNode:function(t,r){this.hasNode(t)||(this.nodes[t]=2===arguments.length?r:t,this.outgoingEdges[t]=[],this.incomingEdges[t]=[])},removeNode:function(t){this.hasNode(t)&&(delete this.nodes[t],delete this.outgoingEdges[t],delete this.incomingEdges[t],[this.incomingEdges,this.outgoingEdges].forEach((function(r){Object.keys(r).forEach((function(e){var n=r[e].indexOf(t);n>=0&&r[e].splice(n,1)}),this)})))},hasNode:function(t){return this.nodes.hasOwnProperty(t)},getNodeData:function(t){if(this.hasNode(t))return this.nodes[t];throw new Error("Node does not exist: "+t)},setNodeData:function(t,r){if(!this.hasNode(t))throw new Error("Node does not exist: "+t);this.nodes[t]=r},addDependency:function(t,r){if(!this.hasNode(t))throw new Error("Node does not exist: "+t);if(!this.hasNode(r))throw new Error("Node does not exist: "+r);return-1===this.outgoingEdges[t].indexOf(r)&&this.outgoingEdges[t].push(r),-1===this.incomingEdges[r].indexOf(t)&&this.incomingEdges[r].push(t),!0},removeDependency:function(t,r){var e;this.hasNode(t)&&(e=this.outgoingEdges[t].indexOf(r))>=0&&this.outgoingEdges[t].splice(e,1),this.hasNode(r)&&(e=this.incomingEdges[r].indexOf(t))>=0&&this.incomingEdges[r].splice(e,1)},clone:function(){var t=this,r=new n;return Object.keys(t.nodes).forEach((function(e){r.nodes[e]=t.nodes[e],r.outgoingEdges[e]=t.outgoingEdges[e].slice(0),r.incomingEdges[e]=t.incomingEdges[e].slice(0)})),r},directDependenciesOf:function(t){if(this.hasNode(t))return this.outgoingEdges[t].slice(0);throw new Error("Node does not exist: "+t)},directDependantsOf:function(t){if(this.hasNode(t))return this.incomingEdges[t].slice(0);throw new Error("Node does not exist: "+t)},dependenciesOf:function(t,r){if(this.hasNode(t)){var n=[];e(this.outgoingEdges,r,n,this.circular)(t);var o=n.indexOf(t);return o>=0&&n.splice(o,1),n}throw new Error("Node does not exist: "+t)},dependantsOf:function(t,r){if(this.hasNode(t)){var n=[];e(this.incomingEdges,r,n,this.circular)(t);var o=n.indexOf(t);return o>=0&&n.splice(o,1),n}throw new Error("Node does not exist: "+t)},overallOrder:function(t){var r=this,n=[],o=Object.keys(this.nodes);if(0===o.length)return n;if(!this.circular){var i=e(this.outgoingEdges,!1,[],this.circular);o.forEach((function(t){i(t)}))}var a=e(this.outgoingEdges,t,n,this.circular);return o.filter((function(t){return 0===r.incomingEdges[t].length})).forEach((function(t){a(t)})),this.circular&&o.filter((function(t){return-1===n.indexOf(t)})).forEach((function(t){a(t)})),n},entryNodes:function(){var t=this;return Object.keys(this.nodes).filter((function(r){return 0===t.incomingEdges[r].length}))}},n.prototype.directDependentsOf=n.prototype.directDependantsOf,n.prototype.dependentsOf=n.prototype.dependantsOf;var o=function(t){var r="Dependency Cycle Found: "+t.join(" -> "),e=new Error(r);return e.cyclePath=t,Object.setPrototypeOf(e,Object.getPrototypeOf(this)),Error.captureStackTrace&&Error.captureStackTrace(e,o),e};o.prototype=Object.create(Error.prototype,{constructor:{value:Error,enumerable:!1,writable:!0,configurable:!0}}),Object.setPrototypeOf(o,Error)},146:function(t,r,e){"use strict";var n=e(363),o={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},i={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},a={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},u={};function c(t){return n.isMemo(t)?a:u[t.$$typeof]||o}u[n.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},u[n.Memo]=a;var s=Object.defineProperty,l=Object.getOwnPropertyNames,f=Object.getOwnPropertySymbols,p=Object.getOwnPropertyDescriptor,y=Object.getPrototypeOf,h=Object.prototype;t.exports=function t(r,e,n){if("string"!=typeof e){if(h){var o=y(e);o&&o!==h&&t(r,o,n)}var a=l(e);f&&(a=a.concat(f(e)));for(var u=c(r),d=c(e),v=0;v<a.length;++v){var b=a[v];if(!(i[b]||n&&n[b]||d&&d[b]||u&&u[b])){var m=p(e,b);try{s(r,b,m)}catch(t){}}}}return r}},217:function(t,r,e){"use strict";var n=e(601),o=e.n(n),i=e(314),a=e.n(i)()(o());a.push([t.id,"._dash-undo-redo {\n    position: fixed;\n    bottom: 30px;\n    left: 30px;\n    font-size: 20px;\n    text-align: center;\n    z-index: 9999;\n    background-color: rgba(255, 255, 255, 0.9);\n}\n._dash-undo-redo > div {\n    position: relative;\n}\n._dash-undo-redo-link {\n    color: #0074d9;\n    cursor: pointer;\n    margin-left: 10px;\n    margin-right: 10px;\n    display: inline-block;\n    opacity: 0.2;\n}\n._dash-undo-redo-link:hover {\n    opacity: 1;\n}\n._dash-undo-redo-link ._dash-icon-undo {\n    font-size: 20px;\n    transform: rotate(270deg);\n}\n._dash-undo-redo-link ._dash-icon-redo {\n    font-size: 20px;\n    transform: rotate(90deg);\n}\n._dash-undo-redo-link ._dash-undo-redo-label {\n    font-size: 15px;\n}\n",""]),r.A=a},242:function(t,r,e){"use strict";t.exports=e(940)},296:function(t,r,e){var n;window,t.exports=(n=e(609),function(t){var r={};function e(n){if(r[n])return r[n].exports;var o=r[n]={i:n,l:!1,exports:{}};return t[n].call(o.exports,o,o.exports,e),o.l=!0,o.exports}return e.m=t,e.c=r,e.d=function(t,r,n){e.o(t,r)||Object.defineProperty(t,r,{enumerable:!0,get:n})},e.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},e.t=function(t,r){if(1&r&&(t=e(t)),8&r)return t;if(4&r&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(e.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&r&&"string"!=typeof t)for(var o in t)e.d(n,o,function(r){return t[r]}.bind(null,o));return n},e.n=function(t){var r=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(r,"a",r),r},e.o=function(t,r){return Object.prototype.hasOwnProperty.call(t,r)},e.p="",e(e.s=1)}([function(t,r){t.exports=n},function(t,r,e){"use strict";e.r(r),e.d(r,"asyncDecorator",(function(){return a})),e.d(r,"inheritAsyncDecorator",(function(){return u})),e.d(r,"isReady",(function(){return c})),e.d(r,"History",(function(){return f}));var n=e(0);function o(t,r,e,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void e(t)}u.done?r(c):Promise.resolve(c).then(n,o)}function i(t){return function(){var r=this,e=arguments;return new Promise((function(n,i){var a=t.apply(r,e);function u(t){o(a,n,i,u,c,"next",t)}function c(t){o(a,n,i,u,c,"throw",t)}u(void 0)}))}}var a=function(t,r){var e,o={isReady:new Promise((function(t){e=t})),get:Object(n.lazy)((function(){return Promise.resolve(r()).then((function(t){return setTimeout(i(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e(!0);case 2:o.isReady=!0;case 3:case"end":return t.stop()}}),t)}))),0),t}))}))};return Object.defineProperty(t,"_dashprivate_isLazyComponentReady",{get:function(){return o.isReady}}),o.get},u=function(t,r){Object.defineProperty(t,"_dashprivate_isLazyComponentReady",{get:function(){return c(r)}})},c=function(t){return t&&t._dashprivate_isLazyComponentReady};function s(t,r){for(var e=0;e<r.length;e++){var n=r[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}var l="_dashprivate_historychange",f=function(){function t(){!function(t,r){if(!(t instanceof r))throw new TypeError("Cannot call a class as a function")}(this,t)}var r,e;return r=t,e=[{key:"dispatchChangeEvent",value:function(){window.dispatchEvent(new CustomEvent(l))}},{key:"onChange",value:function(t){return window.addEventListener(l,t),function(){return window.removeEventListener(l,t)}}}],null&&s(r.prototype,null),e&&s(r,e),Object.defineProperty(r,"prototype",{writable:!1}),t}()}]))},311:function(t){"use strict";t.exports=function(t,r,e,n,o,i,a,u){if(!t){var c;if(void 0===r)c=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var s=[e,n,o,i,a,u],l=0;(c=new Error(r.replace(/%s/g,(function(){return s[l++]})))).name="Invariant Violation"}throw c.framesToPop=1,c}}},314:function(t){"use strict";t.exports=function(t){var r=[];return r.toString=function(){return this.map((function(r){var e="",n=void 0!==r[5];return r[4]&&(e+="@supports (".concat(r[4],") {")),r[2]&&(e+="@media ".concat(r[2]," {")),n&&(e+="@layer".concat(r[5].length>0?" ".concat(r[5]):""," {")),e+=t(r),n&&(e+="}"),r[2]&&(e+="}"),r[4]&&(e+="}"),e})).join("")},r.i=function(t,e,n,o,i){"string"==typeof t&&(t=[[null,t,void 0]]);var a={};if(n)for(var u=0;u<this.length;u++){var c=this[u][0];null!=c&&(a[c]=!0)}for(var s=0;s<t.length;s++){var l=[].concat(t[s]);n&&a[l[0]]||(void 0!==i&&(void 0===l[5]||(l[1]="@layer".concat(l[5].length>0?" ".concat(l[5]):""," {").concat(l[1],"}")),l[5]=i),e&&(l[2]?(l[1]="@media ".concat(l[2]," {").concat(l[1],"}"),l[2]=e):l[2]=e),o&&(l[4]?(l[1]="@supports (".concat(l[4],") {").concat(l[1],"}"),l[4]=o):l[4]="".concat(o)),r.push(l))}},r}},363:function(t,r,e){"use strict";t.exports=e(799)},365:function(t,r,e){"use strict";var n=e(886);t.exports=function(t){var r=typeof t;if("string"===r){var e=t;if(0==(t=+t)&&n(e))return!1}else if("number"!==r)return!1;return t-t<1}},427:function(t,r){"use strict";r.parse=function(t,r){if("string"!=typeof t)throw new TypeError("argument str must be a string");for(var e={},n=(r||{}).decode||o,i=0;i<t.length;){var u=t.indexOf("=",i);if(-1===u)break;var c=t.indexOf(";",i);if(-1===c)c=t.length;else if(c<u){i=t.lastIndexOf(";",u-1)+1;continue}var s=t.slice(i,u).trim();if(void 0===e[s]){var l=t.slice(u+1,c).trim();34===l.charCodeAt(0)&&(l=l.slice(1,-1)),e[s]=a(l,n)}i=c+1}return e},r.serialize=function(t,r,o){var a=o||{},u=a.encode||i;if("function"!=typeof u)throw new TypeError("option encode is invalid");if(!n.test(t))throw new TypeError("argument name is invalid");var c=u(r);if(c&&!n.test(c))throw new TypeError("argument val is invalid");var s=t+"="+c;if(null!=a.maxAge){var l=a.maxAge-0;if(isNaN(l)||!isFinite(l))throw new TypeError("option maxAge is invalid");s+="; Max-Age="+Math.floor(l)}if(a.domain){if(!n.test(a.domain))throw new TypeError("option domain is invalid");s+="; Domain="+a.domain}if(a.path){if(!n.test(a.path))throw new TypeError("option path is invalid");s+="; Path="+a.path}if(a.expires){var f=a.expires;if(!function(t){return"[object Date]"===e.call(t)||t instanceof Date}(f)||isNaN(f.valueOf()))throw new TypeError("option expires is invalid");s+="; Expires="+f.toUTCString()}if(a.httpOnly&&(s+="; HttpOnly"),a.secure&&(s+="; Secure"),a.partitioned&&(s+="; Partitioned"),a.priority)switch("string"==typeof a.priority?a.priority.toLowerCase():a.priority){case"low":s+="; Priority=Low";break;case"medium":s+="; Priority=Medium";break;case"high":s+="; Priority=High";break;default:throw new TypeError("option priority is invalid")}if(a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:s+="; SameSite=Strict";break;case"lax":s+="; SameSite=Lax";break;case"strict":s+="; SameSite=Strict";break;case"none":s+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}return s};var e=Object.prototype.toString,n=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/;function o(t){return-1!==t.indexOf("%")?decodeURIComponent(t):t}function i(t){return encodeURIComponent(t)}function a(t,r){try{return r(t)}catch(r){return t}}},516:function(t,r,e){"use strict";t.exports=e(712)},540:function(t){"use strict";t.exports=function(t){var r=document.createElement("style");return t.setAttributes(r,t.attributes),t.insert(r,t.options),r}},601:function(t){"use strict";t.exports=function(t){return t[1]}},609:function(t){"use strict";t.exports=window.React},659:function(t){"use strict";var r={};t.exports=function(t,e){var n=function(t){if(void 0===r[t]){var e=document.querySelector(t);if(window.HTMLIFrameElement&&e instanceof window.HTMLIFrameElement)try{e=e.contentDocument.head}catch(t){e=null}r[t]=e}return r[t]}(t);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");n.appendChild(e)}},712:function(t,r){"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},e(t)}var n=Symbol.for("react.element"),o=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),c=Symbol.for("react.provider"),s=Symbol.for("react.context"),l=Symbol.for("react.server_context"),f=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),y=Symbol.for("react.suspense_list"),h=Symbol.for("react.memo"),d=Symbol.for("react.lazy");Symbol.for("react.offscreen");Symbol.for("react.module.reference"),r.isContextConsumer=function(t){return function(t){if("object"===e(t)&&null!==t){var r=t.$$typeof;switch(r){case n:switch(t=t.type){case i:case u:case a:case p:case y:return t;default:switch(t=t&&t.$$typeof){case l:case s:case f:case d:case h:case c:return t;default:return r}}case o:return r}}}(t)===s}},799:function(t,r){"use strict";var e="function"==typeof Symbol&&Symbol.for,n=e?Symbol.for("react.element"):60103,o=e?Symbol.for("react.portal"):60106,i=e?Symbol.for("react.fragment"):60107,a=e?Symbol.for("react.strict_mode"):60108,u=e?Symbol.for("react.profiler"):60114,c=e?Symbol.for("react.provider"):60109,s=e?Symbol.for("react.context"):60110,l=e?Symbol.for("react.async_mode"):60111,f=e?Symbol.for("react.concurrent_mode"):60111,p=e?Symbol.for("react.forward_ref"):60112,y=e?Symbol.for("react.suspense"):60113,h=e?Symbol.for("react.suspense_list"):60120,d=e?Symbol.for("react.memo"):60115,v=e?Symbol.for("react.lazy"):60116,b=e?Symbol.for("react.block"):60121,m=e?Symbol.for("react.fundamental"):60117,g=e?Symbol.for("react.responder"):60118,w=e?Symbol.for("react.scope"):60119;function O(t){if("object"==typeof t&&null!==t){var r=t.$$typeof;switch(r){case n:switch(t=t.type){case l:case f:case i:case u:case a:case y:return t;default:switch(t=t&&t.$$typeof){case s:case p:case v:case d:case c:return t;default:return r}}case o:return r}}}function S(t){return O(t)===f}r.AsyncMode=l,r.ConcurrentMode=f,r.ContextConsumer=s,r.ContextProvider=c,r.Element=n,r.ForwardRef=p,r.Fragment=i,r.Lazy=v,r.Memo=d,r.Portal=o,r.Profiler=u,r.StrictMode=a,r.Suspense=y,r.isAsyncMode=function(t){return S(t)||O(t)===l},r.isConcurrentMode=S,r.isContextConsumer=function(t){return O(t)===s},r.isContextProvider=function(t){return O(t)===c},r.isElement=function(t){return"object"==typeof t&&null!==t&&t.$$typeof===n},r.isForwardRef=function(t){return O(t)===p},r.isFragment=function(t){return O(t)===i},r.isLazy=function(t){return O(t)===v},r.isMemo=function(t){return O(t)===d},r.isPortal=function(t){return O(t)===o},r.isProfiler=function(t){return O(t)===u},r.isStrictMode=function(t){return O(t)===a},r.isSuspense=function(t){return O(t)===y},r.isValidElementType=function(t){return"string"==typeof t||"function"==typeof t||t===i||t===f||t===u||t===a||t===y||t===h||"object"==typeof t&&null!==t&&(t.$$typeof===v||t.$$typeof===d||t.$$typeof===c||t.$$typeof===s||t.$$typeof===p||t.$$typeof===m||t.$$typeof===g||t.$$typeof===w||t.$$typeof===b)},r.typeOf=O},825:function(t){"use strict";t.exports=function(t){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var r=t.insertStyleElement(t);return{update:function(e){!function(t,r,e){var n="";e.supports&&(n+="@supports (".concat(e.supports,") {")),e.media&&(n+="@media ".concat(e.media," {"));var o=void 0!==e.layer;o&&(n+="@layer".concat(e.layer.length>0?" ".concat(e.layer):""," {")),n+=e.css,o&&(n+="}"),e.media&&(n+="}"),e.supports&&(n+="}");var i=e.sourceMap;i&&"undefined"!=typeof btoa&&(n+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(i))))," */")),r.styleTagTransform(n,t,r.options)}(r,t,e)},remove:function(){!function(t){if(null===t.parentNode)return!1;t.parentNode.removeChild(t)}(r)}}}},886:function(t){"use strict";t.exports=function(t){for(var r,e=t.length,n=0;n<e;n++)if(((r=t.charCodeAt(n))<9||r>13)&&32!==r&&133!==r&&160!==r&&5760!==r&&6158!==r&&(r<8192||r>8205)&&8232!==r&&8233!==r&&8239!==r&&8287!==r&&8288!==r&&12288!==r&&65279!==r)return!1;return!0}},888:function(t,r,e){"use strict";t.exports=e(63)},925:function(t){"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},940:function(t,r,e){"use strict";var n=e(609),o=e(888),i="function"==typeof Object.is?Object.is:function(t,r){return t===r&&(0!==t||1/t==1/r)||t!=t&&r!=r},a=o.useSyncExternalStore,u=n.useRef,c=n.useEffect,s=n.useMemo,l=n.useDebugValue;r.useSyncExternalStoreWithSelector=function(t,r,e,n,o){var f=u(null);if(null===f.current){var p={hasValue:!1,value:null};f.current=p}else p=f.current;f=s((function(){function t(t){if(!c){if(c=!0,a=t,t=n(t),void 0!==o&&p.hasValue){var r=p.value;if(o(r,t))return u=r}return u=t}if(r=u,i(a,t))return r;var e=n(t);return void 0!==o&&o(r,e)?r:(a=t,u=e)}var a,u,c=!1,s=void 0===e?null:e;return[function(){return t(r())},null===s?void 0:function(){return t(s())}]}),[r,e,n,o]);var y=a(t,f[0],f[1]);return c((function(){p.hasValue=!0,p.value=y}),[y]),l(y),y}}},r={};function e(n){var o=r[n];if(void 0!==o)return o.exports;var i=r[n]={id:n,exports:{}};return t[n](i,i.exports,e),i.exports}e.n=function(t){var r=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(r,{a:r}),r},e.d=function(t,r){for(var n in r)e.o(r,n)&&!e.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:r[n]})},e.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),e.o=function(t,r){return Object.prototype.hasOwnProperty.call(t,r)},e.nc=void 0,function(){"use strict";var t="undefined"!=typeof globalThis&&globalThis||"undefined"!=typeof self&&self||void 0!==e.g&&e.g||{},r="URLSearchParams"in t,n="Symbol"in t&&"iterator"in Symbol,o="FileReader"in t&&"Blob"in t&&function(){try{return new Blob,!0}catch(t){return!1}}(),i="FormData"in t,a="ArrayBuffer"in t;if(a)var u=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],c=ArrayBuffer.isView||function(t){return t&&u.indexOf(Object.prototype.toString.call(t))>-1};function s(t){if("string"!=typeof t&&(t=String(t)),/[^a-z0-9\-#$%&'*+.^_`|~!]/i.test(t)||""===t)throw new TypeError('Invalid character in header field name: "'+t+'"');return t.toLowerCase()}function l(t){return"string"!=typeof t&&(t=String(t)),t}function f(t){var r={next:function(){var r=t.shift();return{done:void 0===r,value:r}}};return n&&(r[Symbol.iterator]=function(){return r}),r}function p(t){this.map={},t instanceof p?t.forEach((function(t,r){this.append(r,t)}),this):Array.isArray(t)?t.forEach((function(t){if(2!=t.length)throw new TypeError("Headers constructor: expected name/value pair to be length 2, found"+t.length);this.append(t[0],t[1])}),this):t&&Object.getOwnPropertyNames(t).forEach((function(r){this.append(r,t[r])}),this)}function y(t){if(!t._noBody)return t.bodyUsed?Promise.reject(new TypeError("Already read")):void(t.bodyUsed=!0)}function h(t){return new Promise((function(r,e){t.onload=function(){r(t.result)},t.onerror=function(){e(t.error)}}))}function d(t){var r=new FileReader,e=h(r);return r.readAsArrayBuffer(t),e}function v(t){if(t.slice)return t.slice(0);var r=new Uint8Array(t.byteLength);return r.set(new Uint8Array(t)),r.buffer}function b(){return this.bodyUsed=!1,this._initBody=function(t){var e;this.bodyUsed=this.bodyUsed,this._bodyInit=t,t?"string"==typeof t?this._bodyText=t:o&&Blob.prototype.isPrototypeOf(t)?this._bodyBlob=t:i&&FormData.prototype.isPrototypeOf(t)?this._bodyFormData=t:r&&URLSearchParams.prototype.isPrototypeOf(t)?this._bodyText=t.toString():a&&o&&(e=t)&&DataView.prototype.isPrototypeOf(e)?(this._bodyArrayBuffer=v(t.buffer),this._bodyInit=new Blob([this._bodyArrayBuffer])):a&&(ArrayBuffer.prototype.isPrototypeOf(t)||c(t))?this._bodyArrayBuffer=v(t):this._bodyText=t=Object.prototype.toString.call(t):(this._noBody=!0,this._bodyText=""),this.headers.get("content-type")||("string"==typeof t?this.headers.set("content-type","text/plain;charset=UTF-8"):this._bodyBlob&&this._bodyBlob.type?this.headers.set("content-type",this._bodyBlob.type):r&&URLSearchParams.prototype.isPrototypeOf(t)&&this.headers.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"))},o&&(this.blob=function(){var t=y(this);if(t)return t;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(new Blob([this._bodyArrayBuffer]));if(this._bodyFormData)throw new Error("could not read FormData body as blob");return Promise.resolve(new Blob([this._bodyText]))}),this.arrayBuffer=function(){if(this._bodyArrayBuffer)return y(this)||(ArrayBuffer.isView(this._bodyArrayBuffer)?Promise.resolve(this._bodyArrayBuffer.buffer.slice(this._bodyArrayBuffer.byteOffset,this._bodyArrayBuffer.byteOffset+this._bodyArrayBuffer.byteLength)):Promise.resolve(this._bodyArrayBuffer));if(o)return this.blob().then(d);throw new Error("could not read as ArrayBuffer")},this.text=function(){var t,r,e,n,o,i=y(this);if(i)return i;if(this._bodyBlob)return t=this._bodyBlob,e=h(r=new FileReader),o=(n=/charset=([A-Za-z0-9_-]+)/.exec(t.type))?n[1]:"utf-8",r.readAsText(t,o),e;if(this._bodyArrayBuffer)return Promise.resolve(function(t){for(var r=new Uint8Array(t),e=new Array(r.length),n=0;n<r.length;n++)e[n]=String.fromCharCode(r[n]);return e.join("")}(this._bodyArrayBuffer));if(this._bodyFormData)throw new Error("could not read FormData body as text");return Promise.resolve(this._bodyText)},i&&(this.formData=function(){return this.text().then(w)}),this.json=function(){return this.text().then(JSON.parse)},this}p.prototype.append=function(t,r){t=s(t),r=l(r);var e=this.map[t];this.map[t]=e?e+", "+r:r},p.prototype.delete=function(t){delete this.map[s(t)]},p.prototype.get=function(t){return t=s(t),this.has(t)?this.map[t]:null},p.prototype.has=function(t){return this.map.hasOwnProperty(s(t))},p.prototype.set=function(t,r){this.map[s(t)]=l(r)},p.prototype.forEach=function(t,r){for(var e in this.map)this.map.hasOwnProperty(e)&&t.call(r,this.map[e],e,this)},p.prototype.keys=function(){var t=[];return this.forEach((function(r,e){t.push(e)})),f(t)},p.prototype.values=function(){var t=[];return this.forEach((function(r){t.push(r)})),f(t)},p.prototype.entries=function(){var t=[];return this.forEach((function(r,e){t.push([e,r])})),f(t)},n&&(p.prototype[Symbol.iterator]=p.prototype.entries);var m=["CONNECT","DELETE","GET","HEAD","OPTIONS","PATCH","POST","PUT","TRACE"];function g(r,e){if(!(this instanceof g))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');var n,o,i=(e=e||{}).body;if(r instanceof g){if(r.bodyUsed)throw new TypeError("Already read");this.url=r.url,this.credentials=r.credentials,e.headers||(this.headers=new p(r.headers)),this.method=r.method,this.mode=r.mode,this.signal=r.signal,i||null==r._bodyInit||(i=r._bodyInit,r.bodyUsed=!0)}else this.url=String(r);if(this.credentials=e.credentials||this.credentials||"same-origin",!e.headers&&this.headers||(this.headers=new p(e.headers)),this.method=(o=(n=e.method||this.method||"GET").toUpperCase(),m.indexOf(o)>-1?o:n),this.mode=e.mode||this.mode||null,this.signal=e.signal||this.signal||function(){if("AbortController"in t)return(new AbortController).signal}(),this.referrer=null,("GET"===this.method||"HEAD"===this.method)&&i)throw new TypeError("Body not allowed for GET or HEAD requests");if(this._initBody(i),!("GET"!==this.method&&"HEAD"!==this.method||"no-store"!==e.cache&&"no-cache"!==e.cache)){var a=/([?&])_=[^&]*/;a.test(this.url)?this.url=this.url.replace(a,"$1_="+(new Date).getTime()):this.url+=(/\?/.test(this.url)?"&":"?")+"_="+(new Date).getTime()}}function w(t){var r=new FormData;return t.trim().split("&").forEach((function(t){if(t){var e=t.split("="),n=e.shift().replace(/\+/g," "),o=e.join("=").replace(/\+/g," ");r.append(decodeURIComponent(n),decodeURIComponent(o))}})),r}function O(t,r){if(!(this instanceof O))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');if(r||(r={}),this.type="default",this.status=void 0===r.status?200:r.status,this.status<200||this.status>599)throw new RangeError("Failed to construct 'Response': The status provided (0) is outside the range [200, 599].");this.ok=this.status>=200&&this.status<300,this.statusText=void 0===r.statusText?"":""+r.statusText,this.headers=new p(r.headers),this.url=r.url||"",this._initBody(t)}g.prototype.clone=function(){return new g(this,{body:this._bodyInit})},b.call(g.prototype),b.call(O.prototype),O.prototype.clone=function(){return new O(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new p(this.headers),url:this.url})},O.error=function(){var t=new O(null,{status:200,statusText:""});return t.ok=!1,t.status=0,t.type="error",t};var S=[301,302,303,307,308];O.redirect=function(t,r){if(-1===S.indexOf(r))throw new RangeError("Invalid status code");return new O(null,{status:r,headers:{location:t}})};var E=t.DOMException;try{new E}catch(t){(E=function(t,r){this.message=t,this.name=r;var e=Error(t);this.stack=e.stack}).prototype=Object.create(Error.prototype),E.prototype.constructor=E}function j(r,e){return new Promise((function(n,i){var u=new g(r,e);if(u.signal&&u.signal.aborted)return i(new E("Aborted","AbortError"));var c=new XMLHttpRequest;function f(){c.abort()}if(c.onload=function(){var t,r,e={statusText:c.statusText,headers:(t=c.getAllResponseHeaders()||"",r=new p,t.replace(/\r?\n[\t ]+/g," ").split("\r").map((function(t){return 0===t.indexOf("\n")?t.substr(1,t.length):t})).forEach((function(t){var e=t.split(":"),n=e.shift().trim();if(n){var o=e.join(":").trim();try{r.append(n,o)}catch(t){console.warn("Response "+t.message)}}})),r)};0===u.url.indexOf("file://")&&(c.status<200||c.status>599)?e.status=200:e.status=c.status,e.url="responseURL"in c?c.responseURL:e.headers.get("X-Request-URL");var o="response"in c?c.response:c.responseText;setTimeout((function(){n(new O(o,e))}),0)},c.onerror=function(){setTimeout((function(){i(new TypeError("Network request failed"))}),0)},c.ontimeout=function(){setTimeout((function(){i(new TypeError("Network request timed out"))}),0)},c.onabort=function(){setTimeout((function(){i(new E("Aborted","AbortError"))}),0)},c.open(u.method,function(r){try{return""===r&&t.location.href?t.location.href:r}catch(t){return r}}(u.url),!0),"include"===u.credentials?c.withCredentials=!0:"omit"===u.credentials&&(c.withCredentials=!1),"responseType"in c&&(o?c.responseType="blob":a&&(c.responseType="arraybuffer")),e&&"object"==typeof e.headers&&!(e.headers instanceof p||t.Headers&&e.headers instanceof t.Headers)){var y=[];Object.getOwnPropertyNames(e.headers).forEach((function(t){y.push(s(t)),c.setRequestHeader(t,l(e.headers[t]))})),u.headers.forEach((function(t,r){-1===y.indexOf(r)&&c.setRequestHeader(r,t)}))}else u.headers.forEach((function(t,r){c.setRequestHeader(r,t)}));u.signal&&(u.signal.addEventListener("abort",f),c.onreadystatechange=function(){4===c.readyState&&u.signal.removeEventListener("abort",f)}),c.send(void 0===u._bodyInit?null:u._bodyInit)}))}j.polyfill=!0,t.fetch||(t.fetch=j,t.Headers=p,t.Request=g,t.Response=O)}(),function(){"use strict";var t=e(609),r=e.n(t),n=window.ReactDOM,o=e.n(n),i=window.PropTypes,a=e.n(i),u=e(888),c=e(242),s=function(t){t()},l=function(){return s},f=(0,t.createContext)(null);function p(){return(0,t.useContext)(f)}var y=function(){throw new Error("uSES not initialized!")},h=y,d=function(t,r){return t===r};function v(){var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f,e=r===f?p:function(){return(0,t.useContext)(r)};return function(r){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:d,o=e(),i=o.store,a=o.subscription,u=o.getServerState,c=h(a.addNestedSub,i.getState,u||i.getState,r,n);return(0,t.useDebugValue)(c),c}}var b=v();function m(){return m=Object.assign?Object.assign.bind():function(t){for(var r=1;r<arguments.length;r++){var e=arguments[r];for(var n in e)({}).hasOwnProperty.call(e,n)&&(t[n]=e[n])}return t},m.apply(null,arguments)}function g(t,r){if(null==t)return{};var e={};for(var n in t)if({}.hasOwnProperty.call(t,n)){if(-1!==r.indexOf(n))continue;e[n]=t[n]}return e}var w=e(146),O=e.n(w),S=e(516),E=["initMapStateToProps","initMapDispatchToProps","initMergeProps"];function j(t,r,e,n,o){var i,a,u,c,s,l=o.areStatesEqual,f=o.areOwnPropsEqual,p=o.areStatePropsEqual,y=!1;return function(o,h){return y?function(o,y){var h,d,v=!f(y,a),b=!l(o,i,y,a);return i=o,a=y,v&&b?(u=t(i,a),r.dependsOnOwnProps&&(c=r(n,a)),s=e(u,c,a)):v?(t.dependsOnOwnProps&&(u=t(i,a)),r.dependsOnOwnProps&&(c=r(n,a)),s=e(u,c,a)):b?(h=t(i,a),d=!p(h,u),u=h,d&&(s=e(u,c,a)),s):s}(o,h):(u=t(i=o,a=h),c=r(n,a),s=e(u,c,a),y=!0,s)}}function _(t){return function(r){var e=t(r);function n(){return e}return n.dependsOnOwnProps=!1,n}}function x(t){return t.dependsOnOwnProps?Boolean(t.dependsOnOwnProps):1!==t.length}function P(t,r){return function(r,e){e.displayName;var n=function(t,r){return n.dependsOnOwnProps?n.mapToProps(t,r):n.mapToProps(t,void 0)};return n.dependsOnOwnProps=!0,n.mapToProps=function(r,e){n.mapToProps=t,n.dependsOnOwnProps=x(t);var o=n(r,e);return"function"==typeof o&&(n.mapToProps=o,n.dependsOnOwnProps=x(o),o=n(r,e)),o},n}}function A(t){return A="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},A(t)}function k(t,r){return function(e,n){throw new Error("Invalid value of type ".concat(A(t)," for ").concat(r," argument when connecting component ").concat(n.wrappedComponentName,"."))}}function T(t){return T="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},T(t)}function L(t,r,e){return m({},e,t,r)}var I={notify:function(){},get:function(){return[]}};function C(t,r){var e,n=I;function o(){a.onStateChange&&a.onStateChange()}function i(){e||(e=r?r.addNestedSub(o):t.subscribe(o),n=function(){var t=l(),r=null,e=null;return{clear:function(){r=null,e=null},notify:function(){t((function(){for(var t=r;t;)t.callback(),t=t.next}))},get:function(){for(var t=[],e=r;e;)t.push(e),e=e.next;return t},subscribe:function(t){var n=!0,o=e={callback:t,next:null,prev:e};return o.prev?o.prev.next=o:r=o,function(){n&&null!==r&&(n=!1,o.next?o.next.prev=o.prev:e=o.prev,o.prev?o.prev.next=o.next:r=o.next)}}}}())}var a={addNestedSub:function(t){return i(),n.subscribe(t)},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:o,isSubscribed:function(){return Boolean(e)},trySubscribe:i,tryUnsubscribe:function(){e&&(e(),e=void 0,n.clear(),n=I)},getListeners:function(){return n}};return a}var N="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?t.useLayoutEffect:t.useEffect;function R(t){return R="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},R(t)}function D(t,r){return t===r?0!==t||0!==r||1/t==1/r:t!=t&&r!=r}function M(t,r){if(D(t,r))return!0;if("object"!==R(t)||null===t||"object"!==R(r)||null===r)return!1;var e=Object.keys(t),n=Object.keys(r);if(e.length!==n.length)return!1;for(var o=0;o<e.length;o++)if(!Object.prototype.hasOwnProperty.call(r,e[o])||!D(t[e[o]],r[e[o]]))return!1;return!0}function U(t,r){return function(t){if(Array.isArray(t))return t}(t)||function(t,r){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var n,o,i,a,u=[],c=!0,s=!1;try{if(i=(e=e.call(t)).next,0===r){if(Object(e)!==e)return;c=!1}else for(;!(c=(n=i.call(e)).done)&&(u.push(n.value),u.length!==r);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(t,r)||q(t,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function q(t,r){if(t){if("string"==typeof t)return G(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?G(t,r):void 0}}function G(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}var F=["reactReduxForwardedRef"],B=y,H=[null,null];function $(t,r,e,n,o,i){t.current=n,e.current=!1,o.current&&(o.current=null,i())}function z(t,r){return t===r}var J=function(e,n,o){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},a=(i.pure,i.areStatesEqual),u=void 0===a?z:a,c=i.areOwnPropsEqual,s=void 0===c?M:c,l=i.areStatePropsEqual,p=void 0===l?M:l,y=i.areMergedPropsEqual,h=void 0===y?M:y,d=i.forwardRef,v=void 0!==d&&d,b=i.context,w=void 0===b?f:b,x=function(t){return t?"function"==typeof t?P(t):k(t,"mapStateToProps"):_((function(){return{}}))}(e),A=function(t){return t&&"object"===T(t)?_((function(r){return function(t,r){var e={},n=function(){var n=t[o];"function"==typeof n&&(e[o]=function(){return r(n.apply(void 0,arguments))})};for(var o in t)n();return e}(t,r)})):t?"function"==typeof t?P(t):k(t,"mapDispatchToProps"):_((function(t){return{dispatch:t}}))}(n),I=function(t){return t?"function"==typeof t?function(t){return function(r,e){e.displayName;var n,o=e.areMergedPropsEqual,i=!1;return function(r,e,a){var u=t(r,e,a);return i?o(u,n)||(n=u):(i=!0,n=u),n}}}(t):k(t,"mergeProps"):function(){return L}}(o),R=Boolean(e);return function(e){var n=e.displayName||e.name||"Component",o="Connect(".concat(n,")"),i={shouldHandleStateChanges:R,displayName:o,wrappedComponentName:n,WrappedComponent:e,initMapStateToProps:x,initMapDispatchToProps:A,initMergeProps:I,areStatesEqual:u,areStatePropsEqual:p,areOwnPropsEqual:s,areMergedPropsEqual:h};function a(n){var o=(0,t.useMemo)((function(){var t=n.reactReduxForwardedRef,r=g(n,F);return[n.context,t,r]}),[n]),a=U(o,3),u=a[0],c=a[1],s=a[2],l=(0,t.useMemo)((function(){return u&&u.Consumer&&(0,S.isContextConsumer)(r().createElement(u.Consumer,null))?u:w}),[u,w]),f=(0,t.useContext)(l),p=Boolean(n.store)&&Boolean(n.store.getState)&&Boolean(n.store.dispatch),y=Boolean(f)&&Boolean(f.store),h=p?n.store:f.store,d=y?f.getServerState:h.getState,v=(0,t.useMemo)((function(){return function(t,r){var e=r.initMapStateToProps,n=r.initMapDispatchToProps,o=r.initMergeProps,i=g(r,E);return j(e(t,i),n(t,i),o(t,i),t,i)}(h.dispatch,i)}),[h]),b=(0,t.useMemo)((function(){if(!R)return H;var t=C(h,p?void 0:f.subscription),r=t.notifyNestedSubs.bind(t);return[t,r]}),[h,p,f]),O=U(b,2),_=O[0],x=O[1],P=(0,t.useMemo)((function(){return p?f:m({},f,{subscription:_})}),[p,f,_]),A=(0,t.useRef)(),k=(0,t.useRef)(s),T=(0,t.useRef)(),L=(0,t.useRef)(!1),I=((0,t.useRef)(!1),(0,t.useRef)(!1)),D=(0,t.useRef)();N((function(){return I.current=!0,function(){I.current=!1}}),[]);var M,z,J,Y=(0,t.useMemo)((function(){return function(){return T.current&&s===k.current?T.current:v(h.getState(),s)}}),[h,s]),W=(0,t.useMemo)((function(){return function(t){return _?function(t,r,e,n,o,i,a,u,c,s,l){if(!t)return function(){};var f=!1,p=null,y=function(){if(!f&&u.current){var t,e,y=r.getState();try{t=n(y,o.current)}catch(t){e=t,p=t}e||(p=null),t===i.current?a.current||s():(i.current=t,c.current=t,a.current=!0,l())}};return e.onStateChange=y,e.trySubscribe(),y(),function(){if(f=!0,e.tryUnsubscribe(),e.onStateChange=null,p)throw p}}(R,h,_,v,k,A,L,I,T,x,t):function(){}}}),[_]);M=$,z=[k,A,L,s,T,x],N((function(){return M.apply(void 0,function(t){if(Array.isArray(t))return G(t)}(t=z)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||q(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}());var t}),undefined);try{J=B(W,Y,d?function(){return v(d(),s)}:Y)}catch(t){throw D.current&&(t.message+="\nThe error may be correlated with this previous error:\n".concat(D.current.stack,"\n\n")),t}N((function(){D.current=void 0,T.current=void 0,A.current=J}));var V=(0,t.useMemo)((function(){return r().createElement(e,m({},J,{ref:c}))}),[c,e,J]);return(0,t.useMemo)((function(){return R?r().createElement(l.Provider,{value:P},V):V}),[l,V,P])}var c=r().memo(a);if(c.WrappedComponent=e,c.displayName=a.displayName=o,v){var l=r().forwardRef((function(t,e){return r().createElement(c,m({},t,{reactReduxForwardedRef:e}))}));return l.displayName=o,l.WrappedComponent=e,O()(l,e)}return O()(c,e)}},Y=function(e){var n=e.store,o=e.context,i=e.children,a=e.serverState,u=(0,t.useMemo)((function(){var t=C(n);return{store:n,subscription:t,getServerState:a?function(){return a}:void 0}}),[n,a]),c=(0,t.useMemo)((function(){return n.getState()}),[n]);N((function(){var t=u.subscription;return t.onStateChange=t.notifyNestedSubs,t.trySubscribe(),c!==n.getState()&&t.notifyNestedSubs(),function(){t.tryUnsubscribe(),t.onStateChange=void 0}}),[u,c]);var s=o||f;return r().createElement(s.Provider,{value:u},i)};function W(){var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f,e=r===f?p:function(){return(0,t.useContext)(r)};return function(){return e().store}}var V=W();function K(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f,r=t===f?V:W(t);return function(){return r().dispatch}}var Q,X,Z=K();function tt(t,r){switch(t){case 0:return function(){return r.apply(this,arguments)};case 1:return function(t){return r.apply(this,arguments)};case 2:return function(t,e){return r.apply(this,arguments)};case 3:return function(t,e,n){return r.apply(this,arguments)};case 4:return function(t,e,n,o){return r.apply(this,arguments)};case 5:return function(t,e,n,o,i){return r.apply(this,arguments)};case 6:return function(t,e,n,o,i,a){return r.apply(this,arguments)};case 7:return function(t,e,n,o,i,a,u){return r.apply(this,arguments)};case 8:return function(t,e,n,o,i,a,u,c){return r.apply(this,arguments)};case 9:return function(t,e,n,o,i,a,u,c,s){return r.apply(this,arguments)};case 10:return function(t,e,n,o,i,a,u,c,s,l){return r.apply(this,arguments)};default:throw new Error("First argument to _arity must be a non-negative integer no greater than ten")}}function rt(t){return rt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},rt(t)}function et(t){return null!=t&&"object"===rt(t)&&!0===t["@@functional/placeholder"]}function nt(t){return function r(e){return 0===arguments.length||et(e)?r:t.apply(this,arguments)}}Q=c.useSyncExternalStoreWithSelector,h=Q,function(t){B=t}(u.useSyncExternalStore),X=n.unstable_batchedUpdates,s=X;var ot=nt((function(t){var r,e=!1;return tt(t.length,(function(){return e?r:(e=!0,r=t.apply(this,arguments))}))})),it=ot;function at(t){return at="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},at(t)}function ut(t,r,e){return(r=function(t){var r=function(t){if("object"!=at(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var e=r.call(t,"string");if("object"!=at(e))return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==at(r)?r:r+""}(r))in t?Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[r]=e,t}function ct(t,r){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);r&&(n=n.filter((function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable}))),e.push.apply(e,n)}return e}function st(t){for(var r=1;r<arguments.length;r++){var e=null!=arguments[r]?arguments[r]:{};r%2?ct(Object(e),!0).forEach((function(r){ut(t,r,e[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):ct(Object(e)).forEach((function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(e,r))}))}return t}function lt(t){return"Minified Redux error #"+t+"; visit https://redux.js.org/Errors?code="+t+" for the full message or use the non-minified dev environment for full errors. "}var ft="function"==typeof Symbol&&Symbol.observable||"@@observable",pt=function(){return Math.random().toString(36).substring(7).split("").join(".")},yt={INIT:"@@redux/INIT"+pt(),REPLACE:"@@redux/REPLACE"+pt(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+pt()}};function ht(t,r,e){var n;if("function"==typeof r&&"function"==typeof e||"function"==typeof e&&"function"==typeof arguments[3])throw new Error(lt(0));if("function"==typeof r&&void 0===e&&(e=r,r=void 0),void 0!==e){if("function"!=typeof e)throw new Error(lt(1));return e(ht)(t,r)}if("function"!=typeof t)throw new Error(lt(2));var o=t,i=r,a=[],u=a,c=!1;function s(){u===a&&(u=a.slice())}function l(){if(c)throw new Error(lt(3));return i}function f(t){if("function"!=typeof t)throw new Error(lt(4));if(c)throw new Error(lt(5));var r=!0;return s(),u.push(t),function(){if(r){if(c)throw new Error(lt(6));r=!1,s();var e=u.indexOf(t);u.splice(e,1),a=null}}}function p(t){if(!function(t){if("object"!=typeof t||null===t)return!1;for(var r=t;null!==Object.getPrototypeOf(r);)r=Object.getPrototypeOf(r);return Object.getPrototypeOf(t)===r}(t))throw new Error(lt(7));if(void 0===t.type)throw new Error(lt(8));if(c)throw new Error(lt(9));try{c=!0,i=o(i,t)}finally{c=!1}for(var r=a=u,e=0;e<r.length;e++)(0,r[e])();return t}return p({type:yt.INIT}),(n={dispatch:p,subscribe:f,getState:l,replaceReducer:function(t){if("function"!=typeof t)throw new Error(lt(10));o=t,p({type:yt.REPLACE})}})[ft]=function(){var t,r=f;return t={subscribe:function(t){if("object"!=typeof t||null===t)throw new Error(lt(11));function e(){t.next&&t.next(l())}return e(),{unsubscribe:r(e)}}},t[ft]=function(){return this},t},n}function dt(){for(var t=arguments.length,r=new Array(t),e=0;e<t;e++)r[e]=arguments[e];return 0===r.length?function(t){return t}:1===r.length?r[0]:r.reduce((function(t,r){return function(){return t(r.apply(void 0,arguments))}}))}function vt(){for(var t=arguments.length,r=new Array(t),e=0;e<t;e++)r[e]=arguments[e];return function(t){return function(){var e=t.apply(void 0,arguments),n=function(){throw new Error(lt(15))},o={getState:e.getState,dispatch:function(){return n.apply(void 0,arguments)}},i=r.map((function(t){return t(o)}));return n=dt.apply(void 0,i)(e.dispatch),st(st({},e),{},{dispatch:n})}}}function bt(t){return function(r){var e=r.dispatch,n=r.getState;return function(r){return function(o){return"function"==typeof o?o(e,n,t):r(o)}}}}var mt=bt();mt.withExtraArgument=bt;var gt=mt;function wt(t){return function r(e,n){switch(arguments.length){case 0:return r;case 1:return et(e)?r:nt((function(r){return t(e,r)}));default:return et(e)&&et(n)?r:et(e)?nt((function(r){return t(r,n)})):et(n)?nt((function(r){return t(e,r)})):t(e,n)}}}function Ot(t){for(var r,e=[];!(r=t.next()).done;)e.push(r.value);return e}function St(t,r,e){for(var n=0,o=e.length;n<o;){if(t(r,e[n]))return!0;n+=1}return!1}function Et(t,r){return Object.prototype.hasOwnProperty.call(r,t)}var jt="function"==typeof Object.is?Object.is:function(t,r){return t===r?0!==t||1/t==1/r:t!=t&&r!=r},_t=Object.prototype.toString,xt=function(){return"[object Arguments]"===_t.call(arguments)?function(t){return"[object Arguments]"===_t.call(t)}:function(t){return Et("callee",t)}}(),Pt=xt,At=!{toString:null}.propertyIsEnumerable("toString"),kt=["constructor","valueOf","isPrototypeOf","toString","propertyIsEnumerable","hasOwnProperty","toLocaleString"],Tt=function(){return arguments.propertyIsEnumerable("length")}(),Lt=function(t,r){for(var e=0;e<t.length;){if(t[e]===r)return!0;e+=1}return!1},It="function"!=typeof Object.keys||Tt?nt((function(t){if(Object(t)!==t)return[];var r,e,n=[],o=Tt&&Pt(t);for(r in t)!Et(r,t)||o&&"length"===r||(n[n.length]=r);if(At)for(e=kt.length-1;e>=0;)Et(r=kt[e],t)&&!Lt(n,r)&&(n[n.length]=r),e-=1;return n})):nt((function(t){return Object(t)!==t?[]:Object.keys(t)})),Ct=nt((function(t){return null===t?"Null":void 0===t?"Undefined":Object.prototype.toString.call(t).slice(8,-1)}));function Nt(t){return Nt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Nt(t)}function Rt(t,r,e,n){var o=Ot(t);function i(t,r){return Dt(t,r,e.slice(),n.slice())}return!St((function(t,r){return!St(i,r,t)}),Ot(r),o)}function Dt(t,r,e,n){if(jt(t,r))return!0;var o,i,a=Ct(t);if(a!==Ct(r))return!1;if("function"==typeof t["fantasy-land/equals"]||"function"==typeof r["fantasy-land/equals"])return"function"==typeof t["fantasy-land/equals"]&&t["fantasy-land/equals"](r)&&"function"==typeof r["fantasy-land/equals"]&&r["fantasy-land/equals"](t);if("function"==typeof t.equals||"function"==typeof r.equals)return"function"==typeof t.equals&&t.equals(r)&&"function"==typeof r.equals&&r.equals(t);switch(a){case"Arguments":case"Array":case"Object":if("function"==typeof t.constructor&&"Promise"===(o=t.constructor,null==(i=String(o).match(/^function (\w*)/))?"":i[1]))return t===r;break;case"Boolean":case"Number":case"String":if(Nt(t)!==Nt(r)||!jt(t.valueOf(),r.valueOf()))return!1;break;case"Date":if(!jt(t.valueOf(),r.valueOf()))return!1;break;case"Error":return t.name===r.name&&t.message===r.message;case"RegExp":if(t.source!==r.source||t.global!==r.global||t.ignoreCase!==r.ignoreCase||t.multiline!==r.multiline||t.sticky!==r.sticky||t.unicode!==r.unicode)return!1}for(var u=e.length-1;u>=0;){if(e[u]===t)return n[u]===r;u-=1}switch(a){case"Map":return t.size===r.size&&Rt(t.entries(),r.entries(),e.concat([t]),n.concat([r]));case"Set":return t.size===r.size&&Rt(t.values(),r.values(),e.concat([t]),n.concat([r]));case"Arguments":case"Array":case"Object":case"Boolean":case"Number":case"String":case"Date":case"Error":case"RegExp":case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"ArrayBuffer":break;default:return!1}var c=It(t);if(c.length!==It(r).length)return!1;var s=e.concat([t]),l=n.concat([r]);for(u=c.length-1;u>=0;){var f=c[u];if(!Et(f,r)||!Dt(r[f],t[f],s,l))return!1;u-=1}return!0}var Mt=wt((function(t,r){return Dt(t,r,[],[])}));function Ut(t){return Ut="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ut(t)}function qt(t,r,e){var n,o;if("function"==typeof t.indexOf)switch(Ut(r)){case"number":if(0===r){for(n=1/r;e<t.length;){if(0===(o=t[e])&&1/o===n)return e;e+=1}return-1}if(r!=r){for(;e<t.length;){if("number"==typeof(o=t[e])&&o!=o)return e;e+=1}return-1}return t.indexOf(r,e);case"string":case"boolean":case"function":case"undefined":return t.indexOf(r,e);case"object":if(null===r)return t.indexOf(r,e)}for(;e<t.length;){if(Mt(t[e],r))return e;e+=1}return-1}function Gt(t,r){return qt(r,t,0)>=0}var Ft=wt(Gt);function Bt(t){return function r(e,n,o){switch(arguments.length){case 0:return r;case 1:return et(e)?r:wt((function(r,n){return t(e,r,n)}));case 2:return et(e)&&et(n)?r:et(e)?wt((function(r,e){return t(r,n,e)})):et(n)?wt((function(r,n){return t(e,r,n)})):nt((function(r){return t(e,n,r)}));default:return et(e)&&et(n)&&et(o)?r:et(e)&&et(n)?wt((function(r,e){return t(r,e,o)})):et(e)&&et(o)?wt((function(r,e){return t(r,n,e)})):et(n)&&et(o)?wt((function(r,n){return t(e,r,n)})):et(e)?nt((function(r){return t(r,n,o)})):et(n)?nt((function(r){return t(e,r,o)})):et(o)?nt((function(r){return t(e,n,r)})):t(e,n,o)}}}var Ht=Number.isInteger||function(t){return(0|t)===t};function $t(t){return"[object String]"===Object.prototype.toString.call(t)}function zt(t,r){var e=t<0?r.length+t:t;return $t(r)?r.charAt(e):r[e]}function Jt(t,r){for(var e=r,n=0;n<t.length;n+=1){if(null==e)return;var o=t[n];e=Ht(o)?zt(o,e):e[o]}return e}var Yt=wt((function(t,r){return null==r||r!=r?t:r})),Wt=Bt((function(t,r,e){return Yt(t,Jt(r,e))})),Vt=Array.isArray||function(t){return null!=t&&t.length>=0&&"[object Array]"===Object.prototype.toString.call(t)},Kt=nt((function(t){return null==t}));function Qt(t){return Qt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Qt(t)}var Xt=Bt((function t(r,e,n){if(0===r.length)return e;var o=r[0];if(r.length>1){var i=!Kt(n)&&Et(o,n)&&"object"===Qt(n[o])?n[o]:Ht(r[1])?[]:{};e=t(Array.prototype.slice.call(r,1),e,i)}return function(t,r,e){if(Ht(t)&&Vt(e)){var n=[].concat(e);return n[t]=r,n}var o={};for(var i in e)o[i]=e[i];return o[t]=r,o}(o,e,n)})),Zt=Bt((function(t,r,e){return Xt([t],r,e)}));function tr(t,r){return function(){var e=arguments.length;if(0===e)return r();var n=arguments[e-1];return Vt(n)||"function"!=typeof n[t]?r.apply(this,arguments):n[t].apply(n,Array.prototype.slice.call(arguments,0,e-1))}}var rr=wt(tr("forEach",(function(t,r){for(var e=r.length,n=0;n<e;)t(r[n]),n+=1;return r}))),er=rr,nr=wt(Jt);function or(t){return"[object Object]"===Object.prototype.toString.call(t)}var ir=nt((function(t){return null!=t&&"function"==typeof t["fantasy-land/empty"]?t["fantasy-land/empty"]():null!=t&&null!=t.constructor&&"function"==typeof t.constructor["fantasy-land/empty"]?t.constructor["fantasy-land/empty"]():null!=t&&"function"==typeof t.empty?t.empty():null!=t&&null!=t.constructor&&"function"==typeof t.constructor.empty?t.constructor.empty():Vt(t)?[]:$t(t)?"":or(t)?{}:Pt(t)?function(){return arguments}():(r=t,"[object Uint8ClampedArray]"===(e=Object.prototype.toString.call(r))||"[object Int8Array]"===e||"[object Uint8Array]"===e||"[object Int16Array]"===e||"[object Uint16Array]"===e||"[object Int32Array]"===e||"[object Uint32Array]"===e||"[object Float32Array]"===e||"[object Float64Array]"===e||"[object BigInt64Array]"===e||"[object BigUint64Array]"===e?t.constructor.from(""):void 0);var r,e})),ar=ir,ur=nt((function(t){return null!=t&&Mt(t,ar(t))})),cr=Bt((function(t,r,e){var n,o={};for(n in e=e||{},r=r||{})Et(n,r)&&(o[n]=Et(n,e)?t(n,r[n],e[n]):r[n]);for(n in e)Et(n,e)&&!Et(n,o)&&(o[n]=e[n]);return o})),sr=cr,lr=Bt((function(t,r,e){return sr((function(r,e,n){return t(e,n)}),r,e)})),fr=lr,pr=wt((function(t,r){if(null!=r)return Ht(t)?zt(t,r):r[t]})),yr=wt((function(t,r){return t.map((function(t){return pr(t,r)}))}));function hr(t,r,e){for(var n=0,o=e.length;n<o;)r=t(r,e[n]),n+=1;return r}function dr(t,r,e){return function(){if(0===arguments.length)return e();var n=arguments[arguments.length-1];if(!Vt(n)){for(var o=0;o<t.length;){if("function"==typeof n[t[o]])return n[t[o]].apply(n,Array.prototype.slice.call(arguments,0,-1));o+=1}if(function(t){return null!=t&&"function"==typeof t["@@transducer/step"]}(n))return r.apply(null,Array.prototype.slice.call(arguments,0,-1))(n)}return e.apply(this,arguments)}}function vr(t,r){for(var e=0,n=r.length,o=[];e<n;)t(r[e])&&(o[o.length]=r[e]),e+=1;return o}var br=function(){return this.xf["@@transducer/init"]()},mr=function(t){return this.xf["@@transducer/result"](t)},gr=function(){function t(t,r){this.xf=r,this.f=t}return t.prototype["@@transducer/init"]=br,t.prototype["@@transducer/result"]=mr,t.prototype["@@transducer/step"]=function(t,r){return this.f(r)?this.xf["@@transducer/step"](t,r):t},t}();function wr(t){return function(r){return new gr(t,r)}}var Or=wt(dr(["fantasy-land/filter","filter"],wr,(function(t,r){return or(r)?hr((function(e,n){return t(r[n])&&(e[n]=r[n]),e}),{},It(r)):vr(t,r)})));function Sr(t){return Sr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Sr(t)}var Er=nt((function(t){return!!Vt(t)||!!t&&"object"===Sr(t)&&!$t(t)&&(0===t.length||t.length>0&&t.hasOwnProperty(0)&&t.hasOwnProperty(t.length-1))}));function jr(t){return function r(e){for(var n,o,i,a=[],u=0,c=e.length;u<c;){if(Er(e[u]))for(i=0,o=(n=t?r(e[u]):e[u]).length;i<o;)a[a.length]=n[i],i+=1;else a[a.length]=e[u];u+=1}return a}}var _r=nt(jr(!0));function xr(t,r){for(var e=0,n=r.length,o=Array(n);e<n;)o[e]=t(r[e]),e+=1;return o}var Pr=function(){function t(t,r){this.xf=r,this.f=t}return t.prototype["@@transducer/init"]=br,t.prototype["@@transducer/result"]=mr,t.prototype["@@transducer/step"]=function(t,r){return this.xf["@@transducer/step"](t,this.f(r))},t}(),Ar=function(t){return function(r){return new Pr(t,r)}};function kr(t,r,e){return function(){for(var n=[],o=0,i=t,a=0,u=!1;a<r.length||o<arguments.length;){var c;a<r.length&&(!et(r[a])||o>=arguments.length)?c=r[a]:(c=arguments[o],o+=1),n[a]=c,et(c)?u=!0:i-=1,a+=1}return!u&&i<=0?e.apply(this,n):tt(Math.max(0,i),kr(t,n,e))}}var Tr=wt((function(t,r){return 1===t?nt(r):tt(t,kr(t,[],r))})),Lr=Tr,Ir=wt(dr(["fantasy-land/map","map"],Ar,(function(t,r){switch(Object.prototype.toString.call(r)){case"[object Function]":return Lr(r.length,(function(){return t.call(this,r.apply(this,arguments))}));case"[object Object]":return hr((function(e,n){return e[n]=t(r[n]),e}),{},It(r));default:return xr(t,r)}}))),Cr=Ir,Nr="undefined"!=typeof Symbol?Symbol.iterator:"@@iterator";function Rr(t,r,e){return function(n,o,i){if(Er(i))return t(n,o,i);if(null==i)return o;if("function"==typeof i["fantasy-land/reduce"])return r(n,o,i,"fantasy-land/reduce");if(null!=i[Nr])return e(n,o,i[Nr]());if("function"==typeof i.next)return e(n,o,i);if("function"==typeof i.reduce)return r(n,o,i,"reduce");throw new TypeError("reduce: list must be array or iterable")}}function Dr(t,r,e){for(var n=0,o=e.length;n<o;){if((r=t["@@transducer/step"](r,e[n]))&&r["@@transducer/reduced"]){r=r["@@transducer/value"];break}n+=1}return t["@@transducer/result"](r)}var Mr=wt((function(t,r){return tt(t.length,(function(){return t.apply(r,arguments)}))})),Ur=Mr;function qr(t,r,e){for(var n=e.next();!n.done;){if((r=t["@@transducer/step"](r,n.value))&&r["@@transducer/reduced"]){r=r["@@transducer/value"];break}n=e.next()}return t["@@transducer/result"](r)}function Gr(t,r,e,n){return t["@@transducer/result"](e[n](Ur(t["@@transducer/step"],t),r))}var Fr=Rr(Dr,Gr,qr),Br=function(){function t(t){this.f=t}return t.prototype["@@transducer/init"]=function(){throw new Error("init not implemented on XWrap")},t.prototype["@@transducer/result"]=function(t){return t},t.prototype["@@transducer/step"]=function(t,r){return this.f(t,r)},t}();function Hr(t){return new Br(t)}var $r=Bt((function(t,r,e){return Fr("function"==typeof t?Hr(t):t,r,e)}));function zr(t){var r=Object.prototype.toString.call(t);return"[object Function]"===r||"[object AsyncFunction]"===r||"[object GeneratorFunction]"===r||"[object AsyncGeneratorFunction]"===r}function Jr(t){return'"'+t.replace(/\\/g,"\\\\").replace(/[\b]/g,"\\b").replace(/\f/g,"\\f").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/\t/g,"\\t").replace(/\v/g,"\\v").replace(/\0/g,"\\0").replace(/"/g,'\\"')+'"'}var Yr=function(t){return(t<10?"0":"")+t},Wr="function"==typeof Date.prototype.toISOString?function(t){return t.toISOString()}:function(t){return t.getUTCFullYear()+"-"+Yr(t.getUTCMonth()+1)+"-"+Yr(t.getUTCDate())+"T"+Yr(t.getUTCHours())+":"+Yr(t.getUTCMinutes())+":"+Yr(t.getUTCSeconds())+"."+(t.getUTCMilliseconds()/1e3).toFixed(3).slice(2,5)+"Z"},Vr=wt((function(t,r){return Or((e=t,function(){return!e.apply(this,arguments)}),r);var e})),Kr=Vr;function Qr(t){return Qr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Qr(t)}function Xr(t,r){var e=function(e){var n=r.concat([t]);return Gt(e,n)?"<Circular>":Xr(e,n)},n=function(t,r){return xr((function(r){return Jr(r)+": "+e(t[r])}),r.slice().sort())};switch(Object.prototype.toString.call(t)){case"[object Arguments]":return"(function() { return arguments; }("+xr(e,t).join(", ")+"))";case"[object Array]":return"["+xr(e,t).concat(n(t,Kr((function(t){return/^\d+$/.test(t)}),It(t)))).join(", ")+"]";case"[object Boolean]":return"object"===Qr(t)?"new Boolean("+e(t.valueOf())+")":t.toString();case"[object Date]":return"new Date("+(isNaN(t.valueOf())?e(NaN):Jr(Wr(t)))+")";case"[object Map]":return"new Map("+e(Array.from(t))+")";case"[object Null]":return"null";case"[object Number]":return"object"===Qr(t)?"new Number("+e(t.valueOf())+")":1/t==-1/0?"-0":t.toString(10);case"[object Set]":return"new Set("+e(Array.from(t).sort())+")";case"[object String]":return"object"===Qr(t)?"new String("+e(t.valueOf())+")":Jr(t);case"[object Undefined]":return"undefined";default:if("function"==typeof t.toString){var o=t.toString();if("[object Object]"!==o)return o}return"{"+n(t,It(t)).join(", ")+"}"}}var Zr=nt((function(t){return Xr(t,[])})),te=wt((function(t,r){if(Vt(t)){if(Vt(r))return t.concat(r);throw new TypeError(Zr(r)+" is not an array")}if($t(t)){if($t(r))return t+r;throw new TypeError(Zr(r)+" is not a string")}if(null!=t&&zr(t["fantasy-land/concat"]))return t["fantasy-land/concat"](r);if(null!=t&&zr(t.concat))return t.concat(r);throw new TypeError(Zr(t)+' does not have a method named "concat" or "fantasy-land/concat"')}));function re(t){return t&&t["@@transducer/reduced"]?t:{"@@transducer/value":t,"@@transducer/reduced":!0}}var ee=function(){function t(t,r){this.xf=r,this.f=t,this.all=!0}return t.prototype["@@transducer/init"]=br,t.prototype["@@transducer/result"]=function(t){return this.all&&(t=this.xf["@@transducer/step"](t,!0)),this.xf["@@transducer/result"](t)},t.prototype["@@transducer/step"]=function(t,r){return this.f(r)||(this.all=!1,t=re(this.xf["@@transducer/step"](t,!1))),t},t}();function ne(t){return function(r){return new ee(t,r)}}var oe=wt(dr(["all"],ne,(function(t,r){for(var e=0;e<r.length;){if(!t(r[e]))return!1;e+=1}return!0}))),ie=oe;function ae(t){return ae="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ae(t)}var ue=wt((function(t,r){if(t===r)return r;function e(t,r){if(t>r!=r>t)return r>t?r:t}var n=e(t,r);if(void 0!==n)return n;var o=e(ae(t),ae(r));if(void 0!==o)return o===ae(t)?t:r;var i=Zr(t),a=e(i,Zr(r));return void 0!==a&&a===i?t:r})),ce=wt((function(t,r){return Cr(pr(t),r)})),se=wt((function(t,r){return Lr($r(ue,0,ce("length",r)),(function(){var e=arguments,n=this;return t.apply(n,xr((function(t){return t.apply(n,e)}),r))}))})),le=se,fe=nt((function(t){return le((function(){return Array.prototype.slice.call(arguments,0)}),t)})),pe=fe([Or,Kr]);function ye(t){return ye="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ye(t)}function he(t,r,e){var n,o=ye(t);switch(o){case"string":case"number":return 0===t&&1/t==-1/0?!!e._items["-0"]||(r&&(e._items["-0"]=!0),!1):null!==e._nativeSet?r?(n=e._nativeSet.size,e._nativeSet.add(t),e._nativeSet.size===n):e._nativeSet.has(t):o in e._items?t in e._items[o]||(r&&(e._items[o][t]=!0),!1):(r&&(e._items[o]={},e._items[o][t]=!0),!1);case"boolean":if(o in e._items){var i=t?1:0;return!!e._items[o][i]||(r&&(e._items[o][i]=!0),!1)}return r&&(e._items[o]=t?[!1,!0]:[!0,!1]),!1;case"function":return null!==e._nativeSet?r?(n=e._nativeSet.size,e._nativeSet.add(t),e._nativeSet.size===n):e._nativeSet.has(t):o in e._items?!!Gt(t,e._items[o])||(r&&e._items[o].push(t),!1):(r&&(e._items[o]=[t]),!1);case"undefined":return!!e._items[o]||(r&&(e._items[o]=!0),!1);case"object":if(null===t)return!!e._items.null||(r&&(e._items.null=!0),!1);default:return(o=Object.prototype.toString.call(t))in e._items?!!Gt(t,e._items[o])||(r&&e._items[o].push(t),!1):(r&&(e._items[o]=[t]),!1)}}var de=function(){function t(){this._nativeSet="function"==typeof Set?new Set:null,this._items={}}return t.prototype.add=function(t){return!he(t,!0,this)},t.prototype.has=function(t){return he(t,!1,this)},t}(),ve=wt((function(t,r){for(var e=[],n=0,o=t.length,i=r.length,a=new de,u=0;u<i;u+=1)a.add(r[u]);for(;n<o;)a.add(t[n])&&(e[e.length]=t[n]),n+=1;return e})),be=wt((function(t,r){var e={};for(var n in r)t(r[n],n,r)&&(e[n]=r[n]);return e})),me=wt((function(t,r){for(var e=0,n=Math.min(t.length,r.length),o={};e<n;)o[t[e]]=r[e],e+=1;return o})),ge=me,we=e(131),Oe=e(365),Se=e.n(Oe),Ee=wt((function(t,r){for(var e=It(r),n=0;n<e.length;){var o=e[n];t(r[o],o,r),n+=1}return r})),je=Ee,_e=wt((function(t,r){for(var e=Math.min(t.length,r.length),n=Array(e),o=0;o<e;)n[o]=[t[o],r[o]],o+=1;return n}));function xe(t){return t}var Pe=nt(xe),Ae=function(){function t(t,r){this.xf=r,this.f=t,this.set=new de}return t.prototype["@@transducer/init"]=br,t.prototype["@@transducer/result"]=mr,t.prototype["@@transducer/step"]=function(t,r){return this.set.add(this.f(r))?this.xf["@@transducer/step"](t,r):t},t}();function ke(t){return function(r){return new Ae(t,r)}}var Te=wt(dr([],ke,(function(t,r){for(var e,n,o=new de,i=[],a=0;a<r.length;)e=t(n=r[a]),o.add(e)&&i.push(n),a+=1;return i}))),Le=Te(Pe),Ie=wt((function(t,r){for(var e=new de,n=0;n<t.length;n+=1)e.add(t[n]);return Le(vr(e.has.bind(e),r))})),Ce=nt((function(t){for(var r=It(t),e=r.length,n=[],o=0;o<e;)n[o]=t[r[o]],o+=1;return n}));function Ne(t){return Ne="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ne(t)}var Re=wt((function t(r,e){if(!or(e)&&!Vt(e))return e;var n,o,i,a=e instanceof Array?[]:{};for(o in e)i=Ne(n=r[o]),a[o]="function"===i?n(e[o]):n&&"object"===i?t(n,e[o]):e[o];return a}));function De(t,r){var e;r=r||[];var n=(t=t||[]).length,o=r.length,i=[];for(e=0;e<n;)i[i.length]=t[e],e+=1;for(e=0;e<o;)i[i.length]=r[e],e+=1;return i}function Me(t,r,e){for(var n=e.next();!n.done;)r=t(r,n.value),n=e.next();return r}function Ue(t,r,e,n){return e[n](t,r)}var qe=Rr(hr,Ue,Me),Ge=wt((function(t,r){return"function"==typeof r["fantasy-land/ap"]?r["fantasy-land/ap"](t):"function"==typeof t.ap?t.ap(r):"function"==typeof t?function(e){return t(e)(r(e))}:qe((function(t,e){return De(t,Cr(e,r))}),[],t)})),Fe=function(){function t(t,r){this.xf=r,this.f=t,this.idx=-1,this.found=!1}return t.prototype["@@transducer/init"]=br,t.prototype["@@transducer/result"]=function(t){return this.found||(t=this.xf["@@transducer/step"](t,-1)),this.xf["@@transducer/result"](t)},t.prototype["@@transducer/step"]=function(t,r){return this.idx+=1,this.f(r)&&(this.found=!0,t=re(this.xf["@@transducer/step"](t,this.idx))),t},t}();function Be(t){return function(r){return new Fe(t,r)}}var He=wt(dr([],Be,(function(t,r){for(var e=0,n=r.length;e<n;){if(t(r[e]))return e;e+=1}return-1}))),$e=He,ze="function"==typeof Object.assign?Object.assign:function(t){if(null==t)throw new TypeError("Cannot convert undefined or null to object");for(var r=Object(t),e=1,n=arguments.length;e<n;){var o=arguments[e];if(null!=o)for(var i in o)Et(i,o)&&(r[i]=o[i]);e+=1}return r},Je=wt((function(t,r){return ze({},t,r)})),Ye=function(){function t(t,r){this.xf=r,this.f=t,this.any=!1}return t.prototype["@@transducer/init"]=br,t.prototype["@@transducer/result"]=function(t){return this.any||(t=this.xf["@@transducer/step"](t,!1)),this.xf["@@transducer/result"](t)},t.prototype["@@transducer/step"]=function(t,r){return this.f(r)&&(this.any=!0,t=re(this.xf["@@transducer/step"](t,!0))),t},t}();function We(t){return function(r){return new Ye(t,r)}}var Ve=wt(dr(["any"],We,(function(t,r){for(var e=0;e<r.length;){if(t(r[e]))return!0;e+=1}return!1}))),Ke=Ve,Qe=function(){function t(t,r){this.xf=r,this.n=t,this.i=0}return t.prototype["@@transducer/init"]=br,t.prototype["@@transducer/result"]=mr,t.prototype["@@transducer/step"]=function(t,r){this.i+=1;var e=0===this.n?t:this.xf["@@transducer/step"](t,r);return this.n>=0&&this.i>=this.n?re(e):e},t}();function Xe(t){return function(r){return new Qe(t,r)}}var Ze=Bt(tr("slice",(function(t,r,e){return Array.prototype.slice.call(e,t,r)}))),tn=wt(dr(["take"],Xe,(function(t,r){return Ze(0,t<0?1/0:t,r)}))),rn=wt((function(t,r){return Mt(tn(t.length,r),t)})),en=wt((function(t,r){return"function"!=typeof r.indexOf||Vt(r)?qt(r,t,0):r.indexOf(t)})),nn=Bt((function(t,r,e){t=t<e.length&&t>=0?t:e.length;var n=Array.prototype.slice.call(e,0);return n.splice(t,0,r),n})),on=function(){function t(t,r){this.xf=r,this.f=t,this.found=!1}return t.prototype["@@transducer/init"]=br,t.prototype["@@transducer/result"]=function(t){return this.found||(t=this.xf["@@transducer/step"](t,void 0)),this.xf["@@transducer/result"](t)},t.prototype["@@transducer/step"]=function(t,r){return this.f(r)&&(this.found=!0,t=re(this.xf["@@transducer/step"](t,r))),t},t}();function an(t){return function(r){return new on(t,r)}}var un=wt(dr(["find"],an,(function(t,r){for(var e=0,n=r.length;e<n;){if(t(r[e]))return r[e];e+=1}}))),cn=un,sn=Bt((function(t,r,e){return Mt(t,pr(r,e))})),ln=wt((function(t,r){if(0===t.length||Kt(r))return!1;for(var e=r,n=0;n<t.length;){if(Kt(e)||!Et(t[n],e))return!1;e=e[t[n]],n+=1}return!0})),fn=ln,pn=wt((function(t,r){return fn([t],r)})),yn=wt((function(t,r){return De(r,[t])}));function hn(t){return hn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},hn(t)}function dn(t,r){for(var e=0;e<r.length;e++){var n=r[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,vn(n.key),n)}}function vn(t){var r=function(t){if("object"!=hn(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var e=r.call(t,"string");if("object"!=hn(e))return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==hn(r)?r:r+""}function bn(t){return function(t){if(Array.isArray(t))return gn(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||mn(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function mn(t,r){if(t){if("string"==typeof t)return gn(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?gn(t,r):void 0}}function gn(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function wn(t){var r=pn("url_base_pathname",t),e=pn("requests_pathname_prefix",t);if("Object"!==Ct(t)||!r&&!e)throw new Error('\n            Trying to make an API request but neither\n            "url_base_pathname" nor "requests_pathname_prefix"\n            is in `config`. `config` is: ',t);var n=e?t.requests_pathname_prefix:t.url_base_pathname;return"/"===n.charAt(n.length-1)?n:n+"/"}var On=["props","children"],Sn=function(t,r){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:void 0;if(Array.isArray(t))t.forEach((function(t,o){if(n){var i=$e((function(t){return Ft("{}",t)}),n);if(-1!==i){var a=Ze(0,i,n),u=Ze(i,n.length,n);if(a.length)Sn(nr(a,t),r,te(e,te([o],a)),u);else{var c,s=u.map((function(t){return t.replace("{}","")})).filter((function(t){return t})),l=te([o],s);for(var f in c=s.length?nr(s,t):t){var p=c[f];Sn(p,r,te(e,l.concat([f])))}}}else Sn(nr(n,t),r,te(e,te([o],n)))}else Sn(t,r,yn(o,e))}));else if("Object"===Ct(t)){r(t,e);var o=nr(On,t);if(o){var i=te(e,On);Sn(o,r,i)}Wt([],[t.namespace,t.type],window.__dashprivate_childrenProps).forEach((function(n){if(n.includes("[]")){var o=(w=n.split("[]").map((function(t){return t.split(".").filter((function(t){return t}))})),O=2,function(t){if(Array.isArray(t))return t}(w)||function(t,r){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var n,o,i,a,u=[],c=!0,s=!1;try{if(i=(e=e.call(t)).next,0===r){if(Object(e)!==e)return;c=!1}else for(;!(c=(n=i.call(e)).done)&&(u.push(n.value),u.length!==r);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(w,O)||mn(w,O)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),i=o[0],a=o[1],u=te(["props"],i),c=te(e,u);Sn(nr(u,t),r,c,a)}else if(n.includes("{}")){for(var s=n.split("."),l=[],f=[],p=!1,y=0;y<s.length;y++){var h=s[y];!p&&h.includes("{}")?(p=!0,l.push(h.replace("{}",""))):p?f.push(h):l.push(h)}var d=te(e,["props"].concat(l)),v=nr(["props"].concat(l),t);if(void 0!==v)for(var b in v){var m=v[b];f.length?Sn(nr(f,m),r,te(d,[b].concat(f))):Sn(m,r,[].concat(bn(d),[b]))}}else{var g=te(e,["props"].concat(bn(n.split("."))));Sn(nr(["props"].concat(bn(n.split("."))),t),r,g)}var w,O}))}},En=function(){return t=function t(){!function(t,r){if(!(t instanceof r))throw new TypeError("Cannot call a class as a function")}(this,t),this._ev={}},r=[{key:"on",value:function(t,r){var e=this;return(this._ev[t]=this._ev[t]||[]).push(r),function(){return e.removeListener(t,r)}}},{key:"removeListener",value:function(t,r){var e=this._ev[t];if(e){var n=e.indexOf(r);n>-1&&e.splice(n,1)}}},{key:"emit",value:function(t){for(var r=this,e=arguments.length,n=new Array(e>1?e-1:0),o=1;o<e;o++)n[o-1]=arguments[o];var i=this._ev[t];i&&i.forEach((function(t){return t.apply(r,n)}))}},{key:"once",value:function(t,r){var e=this,n=this.on(t,(function(){n();for(var t=arguments.length,o=new Array(t),i=0;i<t;i++)o[i]=arguments[i];r.apply(e,o)}))}}],r&&dn(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,r}();function jn(t){return jn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},jn(t)}function _n(t,r,e,n){var o=e||{strs:{},objs:{}},i=o.strs,a=o.objs,u=function(t){return r.some((function(r,e){return t[e]!==r}))},c=r.length,s=c?Or(u,i):{},l={};return c&&je((function(t,r){var e=Or((function(t){var r=t.path;return u(r)}),t);e.length&&(l[r]=e)}),a),Sn(t,(function(t,e){var n=nr(["props","id"],t);if(n)if("object"===jn(n)){var o=Object.keys(n).sort(),i=yr(o,n),u=o.join(","),c=l[u]=l[u]||[],f=a[u]||[],p={values:i,path:te(r,e)},y=en(p,f);-1===y?c.push(p):l[u]=nn(y,p,c)}else s[n]=te(r,e)})),{strs:s,objs:l,events:n||e.events}}function xn(t,r){if("object"===jn(r)){var e=Object.keys(r).sort(),n=e.join(","),o=t.objs[n];if(!o)return!1;var i=yr(e,r),a=cn(sn(i,"values"),o);return a&&a.path}return t.strs[r]}var Pn=function(t){var r=t.type,e=t.namespace,n=window[e];if(n){if(n[r])return n[r];throw new Error("Component ".concat(r," not found in ").concat(e))}throw new Error("".concat(e," was not found."))};function An(t,r){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);r&&(n=n.filter((function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable}))),e.push.apply(e,n)}return e}function kn(t){for(var r=1;r<arguments.length;r++){var e=null!=arguments[r]?arguments[r]:{};r%2?An(Object(e),!0).forEach((function(r){Tn(t,r,e[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):An(Object(e)).forEach((function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(e,r))}))}return t}function Tn(t,r,e){return(r=function(t){var r=function(t){if("object"!=Nn(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var e=r.call(t,"string");if("object"!=Nn(e))return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Nn(r)?r:r+""}(r))in t?Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[r]=e,t}function Ln(t,r){return function(t){if(Array.isArray(t))return t}(t)||function(t,r){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var n,o,i,a,u=[],c=!0,s=!1;try{if(i=(e=e.call(t)).next,0===r){if(Object(e)!==e)return;c=!1}else for(;!(c=(n=i.call(e)).done)&&(u.push(n.value),u.length!==r);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(t,r)||In(t,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function In(t,r){if(t){if("string"==typeof t)return Cn(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Cn(t,r):void 0}}function Cn(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function Nn(t){return Nn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Nn(t)}var Rn=function(t){return t.startsWith("..")},Dn={wild:"ALL",multi:1},Mn={wild:"MATCH"},Un={wild:"ALLSMALLER",multi:1,expand:1},qn={ALL:Dn,MATCH:Mn,ALLSMALLER:Un},Gn={Output:{ALL:Dn,MATCH:Mn},Input:qn,State:qn},Fn=["string","number","boolean"],Bn=[".","{"];function Hn(t){var r=t.lastIndexOf(".");return{id:$n(t.substr(0,r)),property:t.substr(r+1)}}function $n(t){return function(t){return t.startsWith("{")}(t)?function(t){return Cr((function(t){return Array.isArray(t)&&qn[t[0]]||t}),JSON.parse(t))}(t):t}function zn(t){return"object"!==Nn(t)?t:"{"+Object.keys(t).sort().map((function(r){return JSON.stringify(r)+":"+((e=t[r])&&e.wild||JSON.stringify(e));var e})).join(",")+"}"}function Jn(t,r){var e=Se()(r);if(Se()(t)){if(e){var n=Number(t),o=Number(r);return n>o?1:n<o?-1:0}return-1}if(e)return 1;var i="boolean"==typeof t;return i!==("boolean"==typeof r)?i?-1:1:t>r?1:t<r?-1:0}var Yn=function(t){return"string"==typeof t?t+"z":"z"};function Wn(t,r,e,n){var o=t[r]=t[r]||{};(o[e]=o[e]||[]).push(n)}function Vn(t,r,e,n){for(var o=Object.keys(r).sort(),i=o.join(","),a=yr(o,r),u=t[i]=t[i]||{},c=u[e]=u[e]||[],s=!1,l=0;l<c.length;l++)if(Mt(a,c[l].values)){s=c[l];break}s||(s={keys:o,values:a,callbacks:[]},c.push(s)),s.callbacks.push(n)}var Kn=function(t){var r=Ln(t,2),e=r[0],n=r[1],o=e&&e.wild,i=n&&n.wild;return o&&i?!(e===Mn&&n===Un||e===Un&&n===Mn):e===n||o||i};function Qn(t,r){var e,n=t.id,o=t.property,i=It(n).sort(),a=yr(i,n),u=function(t){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=In(t))){r&&(t=r);var e=0,n=function(){};return{s:n,n:function(){return e>=t.length?{done:!0}:{done:!1,value:t[e++]}},e:function(t){throw t},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,a=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return i=t.done,t},e:function(t){a=!0,o=t},f:function(){try{i||null==r.return||r.return()}finally{if(a)throw o}}}}(r);try{for(u.s();!(e=u.n()).done;){var c=e.value,s=c.id;if(c.property===o&&"string"!=typeof s&&Mt(It(s).sort(),i)&&ie(Kn,_e(a,yr(i,s))))return c}}catch(t){u.e(t)}finally{u.f()}return!1}function Xn(t,r){var e=new we.w,n={},o=Cr(Re({id:$n})),i=Cr((function(t){var r,e=t.output,n=t.no_output,i=Re({inputs:o,state:o},t);return n?(i.outputs=[],i.noOutput=!0):i.outputs=Cr((function(t){return Zt("out",!0,Hn(t))}),Rn(e)?(r=e).substr(2,r.length-4).split("..."):[e]),i}),t),a=!1;!function(t,r){var e={},n=[];t.forEach((function(t){var o=t.inputs,i=t.outputs,a=t.state,u=!0;1!==i.length||i[0].id||i[0].property||(u=!1);var c="In the callback for output(s):\n  "+i.map(mo).join("\n  ");o.length||r("A callback is missing Inputs",[c,"there are no `Input` elements.","Without `Input` elements, it will never get called.","","Subscribing to `Input` components will cause the","callback to be called whenever their values change."]),[[i,"Output"],[o,"Input"],[a,"State"]].forEach((function(t){var e=Ln(t,2),n=e[0],o=e[1];("Output"!==o||u)&&(Array.isArray(n)||r("Callback ".concat(o,"(s) must be an Array"),[c,"For ".concat(o,"(s) we found:"),JSON.stringify(n),"but we expected an Array."]),n.forEach((function(t,e){!function(t,r,e,n,o){var i=t.id,a=t.property;if("string"==typeof a&&a||o("Callback property error",[r,"".concat(e,"[").concat(n,"].property = ").concat(JSON.stringify(a)),"but we expected `property` to be a non-empty string."]),"object"===Nn(i))ur(i)&&o("Callback item missing ID",[r,"".concat(e,"[").concat(n,"].id = {}"),"Every item linked to a callback needs an ID"]),je((function(t,i){i||o("Callback wildcard ID error",[r,"".concat(e,"[").concat(n,'].id has key "').concat(i,'"'),"Keys must be non-empty strings."]),"object"===Nn(t)&&t.wild?Gn[e][t.wild]!==t&&o("Callback wildcard ID error",[r,"".concat(e,"[").concat(n,'].id["').concat(i,'"] = ').concat(t.wild),"Allowed wildcards for ".concat(e,"s are:"),It(Gn[e]).join(", ")]):Ft(Nn(t),Fn)||o("Callback wildcard ID error",[r,"".concat(e,"[").concat(n,'].id["').concat(i,'"] = ').concat(JSON.stringify(t)),"Wildcard callback ID values must be either wildcards","or constants of one of these types:",Fn.join(", ")])}),i);else if("string"==typeof i){i||o("Callback item missing ID",[r,"".concat(e,"[").concat(n,'].id = "').concat(i,'"'),"Every item linked to a callback needs an ID"]);var u=Bn.filter((function(t){return Ft(t,i)}));u.length&&o("Callback invalid ID string",[r,"".concat(e,"[").concat(n,"].id = '").concat(i,"'"),"characters '".concat(u.join("', '"),"' are not allowed.")])}else o("Callback ID type error",[r,"".concat(e,"[").concat(n,"].id = ").concat(JSON.stringify(i)),"IDs must be strings or wildcard-compatible objects."])}(t,c,o,e,r)})))})),u&&(function(t,r,e,n,o){var i={},a=[];t.forEach((function(t,u){var c=t.id,s=t.property;if("string"==typeof c){var l=mo({id:c,property:s});i[l]?e("Duplicate callback Outputs",[r,"Output ".concat(u," (").concat(l,") is already used by this callback.")]):n[l]?e("Duplicate callback outputs",[r,"Output ".concat(u," (").concat(l,") is already in use."),"To resolve this, set `allow_duplicate=True` on","duplicate outputs, or combine the outputs into","one callback function, distinguishing the trigger","by using `dash.callback_context` if necessary."]):i[l]=1}else{var f={id:c,property:s},p=Qn(f,a),y=p||Qn(f,o);if(p||y){var h=mo(f),d=mo(p||y);e("Overlapping wildcard callback outputs",[r,"Output ".concat(u," (").concat(h,")"),"overlaps another output (".concat(d,")"),"used in ".concat(p?"this":"a different"," callback.")])}else a.push(f)}})),It(i).forEach((function(t){n[t]=1})),a.forEach((function(t){o.push(t)}))}(i,c,r,e,n),function(t,r,e,n,o){var i=Zn(t.length?t[0].id:void 0).matchKeys;t.forEach((function(r,e){e&&!Mt(Zn(r.id).matchKeys,i)&&o("Mismatched `MATCH` wildcards across `Output`s",[n,"Output ".concat(e," (").concat(mo(r),")"),"does not have MATCH wildcards on the same keys as","Output 0 (".concat(mo(t[0]),")."),"MATCH wildcards must be on the same keys for all Outputs.","ALL wildcards need not match, only MATCH."])})),[[r,"Input"],[e,"State"]].forEach((function(r){var e=Ln(r,2),a=e[0],u=e[1];a.forEach((function(r,e){var a=Zn(r.id),c=a.matchKeys,s=a.allsmallerKeys,l=c.concat(s),f=ve(l,i);f.length&&(f.sort(),o("`Input` / `State` wildcards not in `Output`s",[n,"".concat(u," ").concat(e," (").concat(mo(r),")"),"has MATCH or ALLSMALLER on key(s) ".concat(f.join(", ")),"where Output 0 (".concat(mo(t[0]),")"),"does not have a MATCH wildcard. Inputs and State do not","need every MATCH from the Output(s), but they cannot have","extras beyond the Output(s)."]))}))}))}(i,o,a,c,r))}))}(i,(function(t,e){a=!0,r(t,e)}));var u={},c={},s={},l={},f={MultiGraph:e,outputMap:u,inputMap:c,outputPatterns:s,inputPatterns:l,callbacks:i};if(a)return f;function p(t,r){var e=[{}];return je((function(t,o){var i=n[o].vals,a=i.indexOf(r[o]),u=[t];t&&t.wild&&(u=t===Un?a>0?i.slice(0,a):[]:-1===a||t===Dn?i:[r[o]]),e=Ge(Ge([Zt(o)],u),e)}),t),e}i.forEach((function(t){var r=t.outputs,e=t.inputs;r.concat(e).forEach((function(t){var r=t.id;"object"===Nn(r)&&je((function(t,r){n[r]||(n[r]={exact:[],expand:0});var e=n[r];t&&t.wild?t.expand&&(e.expand+=1):-1===e.exact.indexOf(t)&&e.exact.push(t)}),r)}))})),je((function(t){var r,e=t.exact,n=t.expand,o=e.slice().sort(Jn);if(n)for(var i=0;i<n;i++)e.length?(o.splice(0,0,[(r=o[0],Se()(r)?r-1:0)]),o.push(Yn(o[o.length-1]))):o.push(i);else e.length||o.push(0);t.vals=o}),n);var y="__output",h=[],d=[],v=[];function b(t,r){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];e.addNode(t),e.addDependency(t,r),n&&(d[d.length-1].push(t),v[v.length-1].push(r))}return i.forEach((function(t){var r=t.outputs,n=t.inputs;function o(t,r){e.addNode(r),n.forEach((function(e){var n=e.id,o=e.property;"object"===Nn(n)?p(n,t).forEach((function(t){b(mo({id:t,property:o}),r)})):b(mo(e),r)}))}d.push([]),v.push([]);var i=Zn(r.length?r[0].id:void 0).matchKeys,a=$e((function(t){return!eo(t.id)}),r),f=Je({matchKeys:i,firstSingleOutput:a,outputs:r},t);r.forEach((function(t){var r=t.id,e=t.property,i=function(t,r){var e=t.id,n=t.property;return r.some((function(r){var o=r.id,i=r.property;if(n!==i||Nn(e)!==Nn(o))return!1;if("string"==typeof e){if(e===o)return!0}else if(Qn(r,[t]))return!0;return!1}))}(t,n);if("object"===Nn(r))p(r,{}).forEach((function(t){var r={id:t,property:e},n=mo(r);i&&(h.push(r),n+=y),o(t,n)})),Vn(s,r,e,f);else{var a=mo(t);i&&(h.push(t),a+=y),o({},a),Wn(u,r,e,f)}})),n.forEach((function(t){var r=t.id,e=t.property;"object"===Nn(r)?Vn(l,r,e,f):Wn(c,r,e,f)}))})),h.forEach((function(t){for(var r=mo(t),e=r.concat(y),n=0;n<d.length;n++)d[n].some((function(t){return t===r}))&&(v[n].some((function(t){return t===e}))||v[n].forEach((function(t){b(e,t,!1)})))})),f}function Zn(t){var r=[],e=[];return"object"===Nn(t)&&(je((function(t,n){t===Mn?r.push(n):t===Un&&e.push(n)}),t),r.sort(),e.sort()),{matchKeys:r,allsmallerKeys:e}}function to(t,r,e,n,o,i){for(var a=0;a<t.length;a++){var u=r[a],c=e[a];if(c.wild){if(n&&c!==Dn){var s=n.indexOf(t[a]),l=i[s];if(c===Un&&l===Un)throw new Error("invalid wildcard id pair: "+JSON.stringify({keys:t,patternVals:e,vals:r,refKeys:n,refPatternVals:i,refVals:o}));if(Jn(u,o[s])!==(c===Un?-1:l===Un?1:0))return!1}}else if(u!==c)return!1}return!0}function ro(t,r){for(var e=[],n=0;n<t.length;n++)t[n]===Mn&&e.push(r[n]);return e.length?JSON.stringify(e):""}function eo(t){var r=t.id;return"object"===Nn(r)&&Ke((function(t){return t.multi}),Ce(r))}function no(t,r,e,n){var o,i,a="";if("string"==typeof e){var u=(t.outputMap[e]||{})[n];u&&(i=u[0],o=Po())}else{var c=Object.keys(e).sort(),s=yr(c,e),l=c.join(","),f=(t.outputPatterns[l]||{})[n];if(f)for(var p=0;p<f.length;p++){var y=f[p].values;if(to(c,s,y)){i=f[p].callbacks[0],o=Po(c,s,y),a=ro(y,s);break}}}return!!o&&_o(i,o,a)}function oo(t,r,e,n){var o=Object.keys(r.id).sort(),i=yr(o,r.id),a={};e.forEach((function(r){var e=r.id,u=yr(o,e),c=_o(t,Po(o,u,i),ro(i,u)),s=c.resolvedId;a[s]||(n.push(c),a[s]=!0)}))}function io(t,r,e){return function(n){var o=n.matchKeys,i=n.firstSingleOutput,a=n.outputs;if(o.length){var u=a[i];if(u)oo(n,u,t(r)(u),e);else{var c={};a.forEach((function(i){var a=t(r)(i).filter((function(t){var r=JSON.stringify(yr(o,t.id));return!c[r]&&(c[r]=1,!0)}));oo(n,i,a,e)}))}}else{var s=_o(n,t,"");e.push(s)}}}function ao(t){return ao="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ao(t)}function uo(t,r){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);r&&(n=n.filter((function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable}))),e.push.apply(e,n)}return e}function co(t){for(var r=1;r<arguments.length;r++){var e=null!=arguments[r]?arguments[r]:{};r%2?uo(Object(e),!0).forEach((function(r){so(t,r,e[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):uo(Object(e)).forEach((function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(e,r))}))}return t}function so(t,r,e){return(r=function(t){var r=function(t){if("object"!=ao(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var e=r.call(t,"string");if("object"!=ao(e))return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==ao(r)?r:r+""}(r))in t?Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[r]=e,t}function lo(t,r){return function(t){if(Array.isArray(t))return t}(t)||function(t,r){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var n,o,i,a,u=[],c=!0,s=!1;try{if(i=(e=e.call(t)).next,0===r){if(Object(e)!==e)return;c=!1}else for(;!(c=(n=i.call(e)).done)&&(u.push(n.value),u.length!==r);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(t,r)||po(t,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function fo(t){return function(t){if(Array.isArray(t))return yo(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||po(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function po(t,r){if(t){if("string"==typeof t)return yo(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?yo(t,r):void 0}}function yo(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}var ho=2,vo=1,bo=fr(Math.max),mo=function(t){var r=t.id,e=t.property;return"".concat(zn(r),".").concat(e)};function go(t,r,e,n,o){var i=!(arguments.length>5&&void 0!==arguments[5])||arguments[5],a=[],u=mo({id:e,property:n});if("string"==typeof e){var c=(t.inputMap[e]||{})[n];if(!c)return[];c.forEach(io(Po(),r,a))}else{var s=Object.keys(e).sort(),l=yr(s,e),f=s.join(","),p=(t.inputPatterns[f]||{})[n];if(!p)return[];p.forEach((function(t){to(s,l,t.values)&&t.callbacks.forEach(io(Po(s,l,t.values),r,a))}))}return a.forEach((function(e){e.changedPropIds[u]=o||ho,i&&(e.priority=wo(t,r,e))})),a}function wo(t,r,e){for(var n=[e],o={},i={},a=[];n.length;){n=Or((function(t){var r=i[t.resolvedId];return i[t.resolvedId]=!0,r}),n);var u=Or((function(t){return!o[mo(t)]}),_r(Cr((function(t){return _r(t.getOutputs(r))}),n)));u.forEach((function(t){return o[mo(t)]=!0})),(n=_r(Cr((function(e){var n=e.id,o=e.property;return go(t,r,n,o,vo,!1)}),u))).length&&a.push(n.length)}return a.unshift(a.length),Cr((function(t){return Math.min(t,35).toString(36)}),a).join("")}var Oo=function(t,r){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:r,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};if(!r.length)return[];var o=Cr(mo,$r((function(r,e){return te(r,_r(e.getOutputs(t)))}),[],e)),i={};if(o.forEach((function(t){return i[t]=!0})),Object.keys(n).length){var a=_r(Cr((function(r){return function(t,r,e){for(var n=[e],o={};n.length;){var i=Or((function(t){return!o[mo(t)]}),_r(Cr((function(t){return _r(t.getOutputs(r))}),n)));o=$r((function(t,r){return Zt(mo(r),!0,t)}),o,i),n=_r(Cr((function(e){var n=e.id,o=e.property;return go(t,r,n,o,vo,!1)}),i))}return o}(n,t,r)}),e));a.length>0&&(i=Object.assign.apply(Object,[a[0]].concat(fo(a))))}return Or((function(r){return ie((function(t){return!i[mo(t)]}),function(t,r){return t.filter((function(t){return!r.some((function(r){return mo(t)===mo(r)}))}))}(_r(r.getInputs(t)),_r(r.getOutputs(t))))}),r)},So=function(t,r,e,n){for(var o=[],i=function(t,r,e,n){var o=n.outputsOnly,i=n.removedArrayInputsOnly,a=n.newPaths,u=n.chunkPath,c={},s=[];function l(t){if(t){var r=c[t.resolvedId];if(void 0!==r){var e=s[r];e.changedPropIds=bo(e.changedPropIds,t.changedPropIds),t.initialCall&&(e.initialCall=!0)}else c[t.resolvedId]=s.length,s.push(t)}}function f(e,n,c){if(n)for(var s in n){var f=no(t,0,e,s);f&&(f.callback.prevent_initial_call||(f.initialCall=!0,l(f)))}if(!o&&c){var p=i?(d=zn(e),function(t){return t.getInputs(r).some((function(r){return!(!Array.isArray(r)||!r.some((function(t){return zn(t.id)===d}))||(_r(t.getOutputs(a)).length&&(t.initialCall=!0,t.changedPropIds={},l(t)),0))}))}):l,y=p;for(var h in u&&(y=function(t){ie(rn(u),ce("path",_r(t.getOutputs(r))))||p(t)}),c)go(t,r,e,h,vo).forEach(y)}var d}return Sn(e,(function(r){var e=nr(["props","id"],r);if(e)if("string"!=typeof e||i){var n=Object.keys(e).sort().join(",");f(e,!i&&t.outputPatterns[n],t.inputPatterns[n])}else f(e,t.outputMap[e],t.inputMap[e])})),Cr((function(e){return kn(kn({},e),{},{priority:wo(t,r,e)})}),s)}(t,r,e,n);;){var a=lo(pe((function(t){var e=t.callback.inputs,n=t.getInputs;return ie(eo,e)||!ur(ve(Cr(mo,_r(n(r))),o))}),i),2),u=a[0],c=a[1];if(!c.length)break;i=u,o=te(o,Cr(mo,_r(Cr((function(t){return(0,t.getOutputs)(r)}),c))))}if(n.filterRoot){var s=nr(["props","id"],e);s&&(s=zn(s),i=i.filter((function(t){return t.callback.inputs.reduce((function(t,r){return t||zn(r.id)==s&&n.filterRoot.includes(r.property)}),!1)})))}var l=Math.random().toString(16);return Cr((function(t){return co(co({},t),{},{executionGroup:l})}),i)},Eo=function(t){var r=t.anyVals,e=t.callback,n=e.inputs,o=e.outputs,i=e.state;return te(Cr(mo,[].concat(fo(n),fo(o),fo(i))),Array.isArray(r)?r:""===r?[]:[r]).join(",")};function jo(t,r,e,n){return _r(Cr((function(r){return go(e,n,t,r)}),It(r)))}var _o=function(t,r,e){return{callback:t,anyVals:e,resolvedId:t.output+e,getOutputs:function(e){return t.outputs.map(r(e))},getInputs:function(e){return t.inputs.map(r(e))},getState:function(e){return t.state.map(r(e))},changedPropIds:{},initialCall:!1}};function xo(t,r){var e=lo(pe((function(t){var e=t.getOutputs,n=t.callback.outputs;return _r(e(r)).length===n.length}),t),2)[1],n=lo(pe((function(t){var e=t.getOutputs;return!_r(e(r)).length}),e),2)[1];return{added:Cr((function(t){return Zt("changedPropIds",be((function(t,e){return xn(r,Hn(e).id)}),t.changedPropIds),t)}),n),removed:e}}function Po(t,r,e){return function(n){return function(o){var i=o.id,a=o.property;if("string"==typeof i){var u=xn(n,i);return u?[{id:i,property:a,path:u}]:[]}var c=Object.keys(i).sort(),s=yr(c,i),l=c.join(","),f=n.objs[l];if(!f)return[];var p=[];return f.forEach((function(n){var o=n.values,i=n.path;to(c,o,s,t,r,e)&&p.push({id:ge(c,o),property:a,path:i})})),p}}}var Ao={ON_PROP_CHANGE:1,SET_REQUEST_QUEUE:1,SET_GRAPHS:1,SET_PATHS:1,SET_LAYOUT:1,SET_APP_LIFECYCLE:1,SET_CONFIG:1,ADD_HTTP_HEADERS:1,ON_ERROR:1,SET_HOOKS:1,INSERT_COMPONENT:1,REMOVE_COMPONENT:1},ko=function(t){if(Ao[t])return t;throw new Error("".concat(t," is not defined."))};function To(t){var r={STARTED:"STARTED",HYDRATED:"HYDRATED",DESTROYED:"DESTROYED"};if(r[t])return r[t];throw new Error("".concat(t," is not a valid app state."))}var Lo,Io,Co,No,Ro=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:To("STARTED"),r=arguments.length>1?arguments[1]:void 0;return r.type===ko("SET_APP_LIFECYCLE")?To(r.payload):t};function Do(t){return Do="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Do(t)}function Mo(t,r){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);r&&(n=n.filter((function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable}))),e.push.apply(e,n)}return e}function Uo(t){for(var r=1;r<arguments.length;r++){var e=null!=arguments[r]?arguments[r]:{};r%2?Mo(Object(e),!0).forEach((function(r){qo(t,r,e[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):Mo(Object(e)).forEach((function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(e,r))}))}return t}function qo(t,r,e){return(r=function(t){var r=function(t){if("object"!=Do(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var e=r.call(t,"string");if("object"!=Do(e))return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Do(r)?r:r+""}(r))in t?Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[r]=e,t}!function(t){t.AddBlocked="Callbacks.AddBlocked",t.AddExecuted="Callbacks.AddExecuted",t.AddExecuting="Callbacks.AddExecuting",t.AddPrioritized="Callbacks.AddPrioritized",t.AddRequested="Callbacks.AddRequested",t.AddStored="Callbacks.AddStored",t.AddWatched="Callbacks.AddWatched",t.RemoveBlocked="Callbacks.RemoveBlocked",t.RemoveExecuted="Callbacks.RemoveExecuted",t.RemoveExecuting="Callbacks.RemoveExecuting",t.RemovePrioritized="Callbacks.RemovePrioritized",t.RemoveRequested="Callbacks.RemoveRequested",t.RemoveStored="Callbacks.RemoveStored",t.RemoveWatched="Callbacks.RemoveWatched"}(Co||(Co={})),function(t){t.AddCompleted="Callbacks.Completed",t.Aggregate="Callbacks.Aggregate"}(No||(No={}));var Go={blocked:[],executed:[],executing:[],prioritized:[],requested:[],stored:[],watched:[],completed:0},Fo=(qo(qo(qo(qo(qo(qo(qo(qo(qo(qo(Lo={},Co.AddBlocked,te),Co.AddExecuted,te),Co.AddExecuting,te),Co.AddPrioritized,te),Co.AddRequested,te),Co.AddStored,te),Co.AddWatched,te),Co.RemoveBlocked,ve),Co.RemoveExecuted,ve),Co.RemoveExecuting,ve),qo(qo(qo(qo(Lo,Co.RemovePrioritized,ve),Co.RemoveRequested,ve),Co.RemoveStored,ve),Co.RemoveWatched,ve)),Bo=(qo(qo(qo(qo(qo(qo(qo(qo(qo(qo(Io={},Co.AddBlocked,"blocked"),Co.AddExecuted,"executed"),Co.AddExecuting,"executing"),Co.AddPrioritized,"prioritized"),Co.AddRequested,"requested"),Co.AddStored,"stored"),Co.AddWatched,"watched"),Co.RemoveBlocked,"blocked"),Co.RemoveExecuted,"executed"),Co.RemoveExecuting,"executing"),qo(qo(qo(qo(Io,Co.RemovePrioritized,"prioritized"),Co.RemoveRequested,"requested"),Co.RemoveStored,"stored"),Co.RemoveWatched,"watched")),Ho=function(){var t=arguments.length>1?arguments[1]:void 0;return $r((function(t,r){return null===r?t:r.type===No.AddCompleted?function(t,r){return Uo(Uo({},t),{},{completed:t.completed+r.payload})}(t,r):function(t,r){var e=Fo[r.type],n=Bo[r.type];return e&&n&&0!==r.payload.length?Uo(Uo({},t),{},qo({},n,e(t[n],r.payload))):t}(t,r)}),arguments.length>0&&void 0!==arguments[0]?arguments[0]:Go,t.type===No.Aggregate?t.payload:[t])},$o=Bt((function t(r,e,n){return sr((function(e,n,o){return or(n)&&or(o)?t(r,n,o):r(e,n,o)}),e,n)})),zo=$o,Jo=wt((function(t,r){return zo((function(t,r,e){return e}),t,r)}));function Yo(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,r=arguments.length>1?arguments[1]:void 0;return r.type===ko("SET_CONFIG")?(window.__dashprivate_childrenProps=Jo(window.__dashprivate_childrenProps||{},r.payload.children_props),r.payload):r.type===ko("ADD_HTTP_HEADERS")?Jo(t,{fetch:{headers:r.payload}}):t}var Wo={},Vo=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Wo,r=arguments.length>1?arguments[1]:void 0;return"SET_GRAPHS"===r.type?r.payload:t};function Ko(t){return function(t){if(Array.isArray(t))return Qo(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,r){if(t){if("string"==typeof t)return Qo(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Qo(t,r):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Qo(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}var Xo={frontEnd:[],backEnd:[],backEndConnected:!0};function Zo(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Xo,r=arguments.length>1?arguments[1]:void 0;switch(r.type){case"ON_ERROR":var e=t.frontEnd,n=t.backEnd,o=t.backEndConnected;return console.error(r.payload.error),"frontEnd"===r.payload.type?{frontEnd:[Je(r.payload,{timestamp:new Date})].concat(Ko(e)),backEnd:n,backEndConnected:o}:"backEnd"===r.payload.type?{frontEnd:e,backEnd:[Je(r.payload,{timestamp:new Date})].concat(Ko(n)),backEndConnected:o}:t;case"SET_CONNECTION_STATUS":return Je(t,{backEndConnected:r.payload});default:return t}}function ti(t){return function(t){if(Array.isArray(t))return ri(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,r){if(t){if("string"==typeof t)return ri(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?ri(t,r):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ri(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}var ei={past:[],present:{},future:[]},ni=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:ei;switch((arguments.length>1?arguments[1]:void 0).type){case"UNDO":var r=t.past,e=t.present,n=t.future,o=r[r.length-1];return{past:r.slice(0,r.length-1),present:o,future:[e].concat(ti(n))};case"REDO":var i=t.past,a=t.present,u=t.future,c=u[0],s=u.slice(1);return{past:[].concat(ti(i),[a]),present:c,future:s};case"REVERT":var l=t.past,f=t.future,p=l[l.length-1];return{past:l.slice(0,l.length-1),present:p,future:ti(f)};default:return t}},oi=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{layout_pre:null,layout_post:null,request_pre:null,request_post:null,callback_resolved:null,request_refresh_jwt:null,bear:!1},r=arguments.length>1?arguments[1]:void 0;return"SET_HOOKS"===r.type?r.payload:t};function ii(t){return ii="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ii(t)}function ai(t,r,e){if(e||(e=new ui),o=ii(n=t),null==n||"object"!=o&&"function"!=o)return t;var n,o,i,a=function(n){var o=e.get(t);if(o)return o;for(var i in e.set(t,n),t)Object.prototype.hasOwnProperty.call(t,i)&&(n[i]=r?ai(t[i],!0,e):t[i]);return n};switch(Ct(t)){case"Object":return a(Object.create(Object.getPrototypeOf(t)));case"Array":return a(Array(t.length));case"Date":return new Date(t.valueOf());case"RegExp":return i=t,new RegExp(i.source,i.flags?i.flags:(i.global?"g":"")+(i.ignoreCase?"i":"")+(i.multiline?"m":"")+(i.sticky?"y":"")+(i.unicode?"u":"")+(i.dotAll?"s":""));case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"BigInt64Array":case"BigUint64Array":return t.slice();default:return t}}var ui=function(){function t(){this.map={},this.length=0}return t.prototype.set=function(t,r){var e=this.hash(t),n=this.map[e];n||(this.map[e]=n=[]),n.push([t,r]),this.length+=1},t.prototype.hash=function(t){var r=[];for(var e in t)r.push(Object.prototype.toString.call(t[e]));return r.join()},t.prototype.get=function(t){if(this.length<=180){for(var r in this.map)for(var e=this.map[r],n=0;n<e.length;n+=1)if((i=e[n])[0]===t)return i[1]}else{var o=this.hash(t);if(e=this.map[o])for(n=0;n<e.length;n+=1){var i;if((i=e[n])[0]===t)return i[1]}}},t}(),ci=nt((function(t){return null!=t&&"function"==typeof t.clone?t.clone():ai(t,!0)}));function si(t){return si="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},si(t)}function li(t,r,e){return(r=function(t){var r=function(t){if("object"!=si(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var e=r.call(t,"string");if("object"!=si(e))return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==si(r)?r:r+""}(r))in t?Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[r]=e,t}var fi,pi="JWT Expired",yi=200,hi=li(li({},yi,"SUCCESS"),204,"NO_UPDATE"),di=["__dash_client","__dash_server","__dash_upload","__dash_download"],vi={count:0,total:0,compute:0,network:{time:0,upload:0,download:0},resources:{},status:{latest:null},result:{}},bi={updated:[],resources:{},callbacks:{},graphLayout:null},mi=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:bi,r=arguments.length>1?arguments[1]:void 0;if("UPDATE_RESOURCE_USAGE"===r.type){var e=r.payload,n=e.id,o=e.usage,i=e.status,a=hi[i]||i,u={updated:[n],resources:t.resources,callbacks:t.callbacks,graphLayout:t.graphLayout};u.callbacks[n]=u.callbacks[n]||ci(vi);var c=u.callbacks[n],s=c.resources,l=u.resources;if(c.count+=1,c.status.latest=a,c.status[a]=(c.status[a]||0)+1,c.result=r.payload.result,c.inputs=r.payload.inputs,c.state=r.payload.state,o){var f=o.__dash_client,p=o.__dash_server,y=o.__dash_upload,h=o.__dash_download,d=function(t,r){if(null==t)return{};var e,n,o=function(t,r){if(null==t)return{};var e={};for(var n in t)if({}.hasOwnProperty.call(t,n)){if(-1!==r.indexOf(n))continue;e[n]=t[n]}return e}(t,r);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)e=i[n],-1===r.indexOf(e)&&{}.propertyIsEnumerable.call(t,e)&&(o[e]=t[e])}return o}(o,di);for(var v in c.total+=f,c.compute+=p,c.network.time+=f-p,c.network.upload+=y,c.network.download+=h,d)d.hasOwnProperty(v)&&(s[v]=(s[v]||0)+d[v],l[v]=(l[v]||0)+d[v])}return u}return t},gi={id:null,props:{}},wi=function(){return arguments.length>0&&void 0!==arguments[0]?arguments[0]:gi};!function(t){t.Set="IsLoading.Set"}(fi||(fi={}));var Oi=function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],r=arguments.length>1?arguments[1]:void 0;return r.type===fi.Set?r.payload:t},Si=function(t){return{value:t,"fantasy-land/map":function(){return this}}},Ei=wt((function(t,r){return t(Si)(r).value})),ji=wt((function(t,r){return function(e){return function(n){return Cr((function(t){return r(t,n)}),e(t(n)))}}})),_i=nt((function(t){return ji((function(r){return Jt(t,r)}),Xt(t))})),xi=Bt((function(t,r,e){var n=Array.prototype.slice.call(e,0);return n.splice(t,r),n})),Pi=wt((function t(r,e){if(null==e)return e;switch(r.length){case 0:return e;case 1:return function(t,r){if(null==r)return r;if(Ht(t)&&Vt(r))return xi(t,1,r);var e={};for(var n in r)e[n]=r[n];return delete e[t],e}(r[0],e);default:var n=r[0],o=Array.prototype.slice.call(r,1);return null==e[n]?function(t,r){if(Ht(t)&&Vt(r))return[].concat(r);var e={};for(var n in r)e[n]=r[n];return e}(n,e):Zt(n,t(o,e[n]),e)}}));function Ai(t){return Ai="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ai(t)}function ki(t,r){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);r&&(n=n.filter((function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable}))),e.push.apply(e,n)}return e}function Ti(t,r,e){return(r=function(t){var r=function(t){if("object"!=Ai(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var e=r.call(t,"string");if("object"!=Ai(e))return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Ai(r)?r:r+""}(r))in t?Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[r]=e,t}function Li(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}var Ii=function(){var t,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0;if(e.type===ko("SET_LAYOUT"))return Array.isArray(e.payload)?function(t){if(Array.isArray(t))return Li(t)}(t=e.payload)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,r){if(t){if("string"==typeof t)return Li(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Li(t,r):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}():function(t){for(var r=1;r<arguments.length;r++){var e=null!=arguments[r]?arguments[r]:{};r%2?ki(Object(e),!0).forEach((function(r){Ti(t,r,e[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):ki(Object(e)).forEach((function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(e,r))}))}return t}({},e.payload);if(Ft(e.type,["UNDO_PROP_CHANGE","REDO_PROP_CHANGE",ko("ON_PROP_CHANGE")])){var n=yn("props",e.payload.itempath),o=Ei(_i(n),r),i=Je(o,e.payload.props);return Xt(n,i,r)}if(e.type===ko("INSERT_COMPONENT")){var a=e.payload,u=a.component,c=a.componentPath;return Xt(c,u,r)}if(e.type===ko("REMOVE_COMPONENT")){var s=e.payload.componentPath;return Pi(s,r)}return r},Ci={strs:{},objs:{}},Ni=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Ci,r=arguments.length>1?arguments[1]:void 0;return r.type===ko("SET_PATHS")?r.payload:t},Ri=wt((function(t,r){return Pi([t],r)}));function Di(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1?arguments[1]:void 0;switch(r.type){case"ADD_CALLBACK_JOB":return function(t,r){return Zt(t.jobId,t,r)}(r.payload,t);case"REMOVE_CALLBACK_JOB":return function(t,r){return Ri(t,r)}(r.payload.jobId,t);case"CALLBACK_JOB_OUTDATED":return function(t,r){return Xt([t,"outdated"],!0,r)}(r.payload.jobId,t);default:return t}}function Mi(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1?arguments[1]:void 0;switch(r.type){case"LOADED":return r.payload.reduce((function(t,r){var e=[JSON.stringify(r.path)],n=Wt([],e,t);return Xt(e,n.filter((function(t){return t.property!==r.property})),t)}),t);case"LOADING":return r.payload.reduce((function(t,r){var e=[JSON.stringify(r.path)],n=Wt([],e,t);return Ft(r,n)||n.push(r),Xt(e,n,t)}),t);default:return t}}var Ui=wt((function(t,r){return Lr(t+1,(function(){var e=arguments[t];if(null!=e&&zr(e[r]))return e[r].apply(e,Array.prototype.slice.call(arguments,0,t));throw new TypeError(Zr(e)+' does not have a method named "'+r+'"')}))})),qi=Ui(1,"join");function Gi(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function Fi(t,e,n,o){var i,a=Je(e,n);return Array.isArray(o)?r().createElement.apply(r(),[t,a].concat(function(t){if(Array.isArray(t))return Gi(t)}(i=o)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(i)||function(t,r){if(t){if("string"==typeof t)return Gi(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Gi(t,r):void 0}}(i)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())):r().createElement(t,a,o)}function Bi(t){return"Object"===Ct(t)&&pn("type",t)&&pn("namespace",t)&&pn("props",t)}function Hi(t){return qi(",",t)}function $i(t,r){return nr(t,r.layout)}function zi(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}var Ji=["dependenciesRequest","layoutRequest","reloadRequest","loginRequest"],Yi=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1?arguments[1]:void 0;if(Ft(r.type,["UNDO_PROP_CHANGE","REDO_PROP_CHANGE","ON_PROP_CHANGE"])){var e=Hi(r.payload.itempath),n=Wt(0,[e,"hash"],t);t=Zt(e,{hash:n+1,changedProps:r.payload.props,renderType:r.payload.renderType},t)}return t};function Wi(){var t={appLifecycle:Ro,callbacks:Ho,config:Yo,error:Zo,graphs:Vo,history:ni,hooks:oi,profile:mi,changed:wi,isLoading:Oi,layout:Ii,paths:Ni,layoutHashes:Yi,loading:Mi};return er((function(r){var e;t[r]=(e=r,function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1?arguments[1]:void 0,n=t;if(r.type===e){var o=r.payload,i=o.id,a={status:o.status,content:o.content};n=Array.isArray(i)?Xt(i,a,t):i?Zt(i,a,t):Je(t,a)}return n})}),Ji),t.callbackJobs=Di,function(t){for(var r=Object.keys(t),e={},n=0;n<r.length;n++){var o=r[n];"function"==typeof t[o]&&(e[o]=t[o])}var i,a=Object.keys(e);try{!function(t){Object.keys(t).forEach((function(r){var e=t[r];if(void 0===e(void 0,{type:yt.INIT}))throw new Error(lt(12));if(void 0===e(void 0,{type:yt.PROBE_UNKNOWN_ACTION()}))throw new Error(lt(13))}))}(e)}catch(t){i=t}return function(t,r){if(void 0===t&&(t={}),i)throw i;for(var n=!1,o={},u=0;u<a.length;u++){var c=a[u],s=e[c],l=t[c],f=s(l,r);if(void 0===f)throw r&&r.type,new Error(lt(14));o[c]=f,n=n||f!==l}return(n=n||a.length!==Object.keys(t).length)?o:t}}(t)}function Vi(t,r,e){var n,o=r.graphs,i=r.paths,a=r.layout,u=t.itempath,c=t.props,s=nr(u.concat(["props"]),a)||{},l=s.id;return l&&(e&&(r.changed={id:l,props:c}),n={id:l,props:{}},It(c).forEach((function(t){go(o,i,l,t).length&&(n.props[t]=s[t])}))),n}function Ki(t){return Ki="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ki(t)}function Qi(t,r){for(var e=0;e<r.length;e++){var n=r[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ta(n.key),n)}}function Xi(t,r,e){return r&&Qi(t.prototype,r),e&&Qi(t,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function Zi(t,r,e){return(r=ta(r))in t?Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[r]=e,t}function ta(t){var r=function(t){if("object"!=Ki(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var e=r.call(t,"string");if("object"!=Ki(e))return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Ki(r)?r:r+""}var ra=Xi((function t(r){var e=this;!function(t,r){if(!(t instanceof r))throw new TypeError("Cannot call a class as a function")}(this,t),Zi(this,"_store",void 0),Zi(this,"_unsubscribe",void 0),Zi(this,"_observers",[]),Zi(this,"observe",(function(t,r){if("function"==typeof t){if(!Array.isArray(r))throw new Error("inputs must be an array");return e.add(t,r),function(){return e.remove(t)}}return e.add(t.observer,t.inputs),function(){return e.remove(t.observer)}})),Zi(this,"setStore",(function(t){e.__finalize__(),e.__init__(t)})),Zi(this,"__finalize__",(function(){var t;return null===(t=e._unsubscribe)||void 0===t?void 0:t.call(e)})),Zi(this,"__init__",(function(t){e._store=t,t&&(e._unsubscribe=t.subscribe(e.notify)),e._observers.forEach((function(t){t.lastState=null}))})),Zi(this,"add",(function(t,r){return e._observers.push({inputPaths:Cr((function(t){return t.split(".")}),r),lastState:null,observer:t,triggered:!1})})),Zi(this,"notify",(function(){var t=e._store;if(t){var r=t.getState(),n=Or((function(t){return!t.triggered&&Ke((function(e){return nr(e,r)!==nr(e,t.lastState)}),t.inputPaths)}),e._observers);n.forEach((function(t){t.triggered=!0})),n.forEach((function(r){r.lastState=t.getState(),r.observer(t),r.triggered=!1}))}})),Zi(this,"remove",(function(t){return e._observers.splice(e._observers.findIndex((function(r){return t===r.observer}),e._observers),1)})),this.__init__(r)})),ea=function(t){var r=t(),e=r.config,n=r.isLoading,o=null==e?void 0:e.update_title;o&&(n?document.title!==o&&(na.title=document.title,document.title=o):document.title===o?document.title=na.title:na.title=document.title)},na={inputs:["isLoading"],mutationObserver:void 0,observer:function(t){var r=t.getState,e=r().config;if(na.config!==e){var n;na.config=e,null===(n=na.mutationObserver)||void 0===n||n.disconnect(),na.mutationObserver=new MutationObserver((function(){return ea(r)}));var o=document.querySelector("title");o&&na.mutationObserver.observe(o,{subtree:!0,childList:!0,attributes:!0,characterData:!0})}ea(r)}},oa=na,ia=nt((function(t){var r=[];for(var e in t)Et(e,t)&&(r[r.length]=[e,t[e]]);return r})),aa=wt((function(t,r){for(var e={},n=0;n<t.length;)t[n]in r&&(e[t[n]]=r[t[n]]),n+=1;return e})),ua=e(311),ca=e.n(ua),sa=function(t){return"function"==typeof t},la=function(t){return t},fa=function(t){return null===t};function pa(t,r,e){void 0===r&&(r=la),ca()(sa(r)||fa(r),"Expected payloadCreator to be a function, undefined or null");var n=fa(r)||r===la?la:function(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),o=1;o<e;o++)n[o-1]=arguments[o];return t instanceof Error?t:r.apply(void 0,[t].concat(n))},o=sa(e),i=t.toString(),a=function(){var r=n.apply(void 0,arguments),i={type:t};return r instanceof Error&&(i.error=!0),void 0!==r&&(i.payload=r),o&&(i.meta=e.apply(void 0,arguments)),i};return a.toString=function(){return i},a}var ya=e(427);function ha(t){return ha="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ha(t)}function da(){da=function(){return r};var t,r={},e=Object.prototype,n=e.hasOwnProperty,o=Object.defineProperty||function(t,r,e){t[r]=e.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function s(t,r,e){return Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}),t[r]}try{s({},"")}catch(t){s=function(t,r,e){return t[r]=e}}function l(t,r,e,n){var i=r&&r.prototype instanceof b?r:b,a=Object.create(i.prototype),u=new T(n||[]);return o(a,"_invoke",{value:x(t,e,u)}),a}function f(t,r,e){try{return{type:"normal",arg:t.call(r,e)}}catch(t){return{type:"throw",arg:t}}}r.wrap=l;var p="suspendedStart",y="suspendedYield",h="executing",d="completed",v={};function b(){}function m(){}function g(){}var w={};s(w,a,(function(){return this}));var O=Object.getPrototypeOf,S=O&&O(O(L([])));S&&S!==e&&n.call(S,a)&&(w=S);var E=g.prototype=b.prototype=Object.create(w);function j(t){["next","throw","return"].forEach((function(r){s(t,r,(function(t){return this._invoke(r,t)}))}))}function _(t,r){function e(o,i,a,u){var c=f(t[o],t,i);if("throw"!==c.type){var s=c.arg,l=s.value;return l&&"object"==ha(l)&&n.call(l,"__await")?r.resolve(l.__await).then((function(t){e("next",t,a,u)}),(function(t){e("throw",t,a,u)})):r.resolve(l).then((function(t){s.value=t,a(s)}),(function(t){return e("throw",t,a,u)}))}u(c.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new r((function(r,o){e(t,n,r,o)}))}return i=i?i.then(o,o):o()}})}function x(r,e,n){var o=p;return function(i,a){if(o===h)throw Error("Generator is already running");if(o===d){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var u=n.delegate;if(u){var c=P(u,n);if(c){if(c===v)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=d,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=h;var s=f(r,e,n);if("normal"===s.type){if(o=n.done?d:y,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=d,n.method="throw",n.arg=s.arg)}}}function P(r,e){var n=e.method,o=r.iterator[n];if(o===t)return e.delegate=null,"throw"===n&&r.iterator.return&&(e.method="return",e.arg=t,P(r,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=f(o,r.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,v;var a=i.arg;return a?a.done?(e[r.resultName]=a.value,e.next=r.nextLoc,"return"!==e.method&&(e.method="next",e.arg=t),e.delegate=null,v):a:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,v)}function A(t){var r={tryLoc:t[0]};1 in t&&(r.catchLoc=t[1]),2 in t&&(r.finallyLoc=t[2],r.afterLoc=t[3]),this.tryEntries.push(r)}function k(t){var r=t.completion||{};r.type="normal",delete r.arg,t.completion=r}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(A,this),this.reset(!0)}function L(r){if(r||""===r){var e=r[a];if(e)return e.call(r);if("function"==typeof r.next)return r;if(!isNaN(r.length)){var o=-1,i=function e(){for(;++o<r.length;)if(n.call(r,o))return e.value=r[o],e.done=!1,e;return e.value=t,e.done=!0,e};return i.next=i}}throw new TypeError(ha(r)+" is not iterable")}return m.prototype=g,o(E,"constructor",{value:g,configurable:!0}),o(g,"constructor",{value:m,configurable:!0}),m.displayName=s(g,c,"GeneratorFunction"),r.isGeneratorFunction=function(t){var r="function"==typeof t&&t.constructor;return!!r&&(r===m||"GeneratorFunction"===(r.displayName||r.name))},r.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,g):(t.__proto__=g,s(t,c,"GeneratorFunction")),t.prototype=Object.create(E),t},r.awrap=function(t){return{__await:t}},j(_.prototype),s(_.prototype,u,(function(){return this})),r.AsyncIterator=_,r.async=function(t,e,n,o,i){void 0===i&&(i=Promise);var a=new _(l(t,e,n,o),i);return r.isGeneratorFunction(e)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},j(E),s(E,c,"Generator"),s(E,a,(function(){return this})),s(E,"toString",(function(){return"[object Generator]"})),r.keys=function(t){var r=Object(t),e=[];for(var n in r)e.push(n);return e.reverse(),function t(){for(;e.length;){var n=e.pop();if(n in r)return t.value=n,t.done=!1,t}return t.done=!0,t}},r.values=L,T.prototype={constructor:T,reset:function(r){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(k),!r)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(r){if(this.done)throw r;var e=this;function o(n,o){return u.type="throw",u.arg=r,e.next=n,o&&(e.method="next",e.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(c&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,r){for(var e=this.tryEntries.length-1;e>=0;--e){var o=this.tryEntries[e];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=r&&r<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=r,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,r){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&r&&(this.next=r),v},finish:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e.finallyLoc===t)return this.complete(e.completion,e.afterLoc),k(e),v}},catch:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e.tryLoc===t){var n=e.completion;if("throw"===n.type){var o=n.arg;k(e)}return o}}throw Error("illegal catch attempt")},delegateYield:function(r,e,n){return this.delegate={iterator:L(r),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=t),v}},r}function va(t,r,e,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void e(t)}u.done?r(c):Promise.resolve(c).then(n,o)}var ba=pa(ko("ON_ERROR")),ma=pa(ko("SET_APP_LIFECYCLE")),ga=pa(ko("SET_CONFIG")),wa=pa(ko("ADD_HTTP_HEADERS")),Oa=pa(ko("SET_GRAPHS")),Sa=pa(ko("SET_HOOKS")),Ea=pa(ko("SET_LAYOUT")),ja=pa(ko("SET_PATHS")),_a=(pa(ko("SET_REQUEST_QUEUE")),pa(ko("ON_PROP_CHANGE"))),xa=pa(ko("INSERT_COMPONENT")),Pa=pa(ko("REMOVE_COMPONENT")),Aa=function(t){return function(r,e){return t(ba({type:"backEnd",error:{message:r,html:e.join("\n")}}))}};var ka=it(console.warn);function Ta(){try{return{"X-CSRFToken":ya.parse(document.cookie)._csrf_token}}catch(t){return ka(t),{}}}var La=Na("REDO"),Ia=Na("UNDO"),Ca=Na("REVERT");function Na(t){return function(r,e){var n=e(),o=n.history,i=n.paths;r(pa(t)());var a=("REDO"===t?o.future[0]:o.past[o.past.length-1])||{},u=a.id,c=a.props;u&&(r(pa("UNDO_PROP_CHANGE")({itempath:xn(i,u),props:c})),r(Ra({id:u,props:c})))}}function Ra(t){var r=t.id,e=t.props;return function(){var t,n=(t=da().mark((function t(n,o){var i,a,u;return da().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:i=o(),a=i.graphs,u=i.paths,n(Pu(jo(r,e,a,u)));case 2:case"end":return t.stop()}}),t)})),function(){var r=this,e=arguments;return new Promise((function(n,o){var i=t.apply(r,e);function a(t){va(i,n,o,a,u,"next",t)}function u(t){va(i,n,o,a,u,"throw",t)}a(void 0)}))});return function(t,r){return n.apply(this,arguments)}}()}function Da(t,r,e){if(t&&"function"==typeof t.text)t.text().then((function(t){e(ba({type:"backEnd",error:{message:r,html:t}}))}));else{var n=t instanceof Error?t:{message:r,html:t};e(ba({type:"backEnd",error:n}))}}function Ma(t){return Ma="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ma(t)}var Ua=wt((function(t,r){return r instanceof t||null!=r&&(r.constructor===t||"Object"===t.name&&"object"===Ma(r))})),qa=wt((function(t,r){return De([t],r)})),Ga=nt((function(t){return $t(t)?t.split("").reverse().join(""):Array.prototype.slice.call(t,0).reverse()}));function Fa(t){return Fa="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Fa(t)}function Ba(t,r){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);r&&(n=n.filter((function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable}))),e.push.apply(e,n)}return e}function Ha(t){for(var r=1;r<arguments.length;r++){var e=null!=arguments[r]?arguments[r]:{};r%2?Ba(Object(e),!0).forEach((function(r){$a(t,r,e[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):Ba(Object(e)).forEach((function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(e,r))}))}return t}function $a(t,r,e){return(r=function(t){var r=function(t){if("object"!=Fa(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var e=r.call(t,"string");if("object"!=Fa(e))return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Fa(r)?r:r+""}(r))in t?Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[r]=e,t}function za(t,r){return Ua(Number,t)&&t<0?r.length+t:t}function Ja(t,r){for(var e=[],n=0;n<t.length;n++){var o=za(t[n],nr(e,r));e.push(o)}return e}var Ya={Assign:function(t,r){var e=r.params,n=r.location;return Xt(n,e.value,t)},Merge:function(t,r){var e=nr(r.location,t);return Xt(r.location,Ha(Ha({},e),r.params.value),t)},Extend:function(t,r){var e=nr(r.location,t);return Xt(r.location,te(e,r.params.value),t)},Delete:function(t,r){return Pi(r.location,t)},Insert:function(t,r){var e=nr(r.location,t);return Xt(r.location,nn(za(r.params.index,e),r.params.value,e),t)},Append:function(t,r){var e=nr(r.location,t);return Xt(r.location,yn(r.params.value,e),t)},Prepend:function(t,r){var e=nr(r.location,t);return Xt(r.location,qa(r.params.value,e),t)},Add:function(t,r){var e=nr(r.location,t);return Xt(r.location,e+r.params.value,t)},Sub:function(t,r){var e=nr(r.location,t);return Xt(r.location,e-r.params.value,t)},Mul:function(t,r){var e=nr(r.location,t);return Xt(r.location,e*r.params.value,t)},Div:function(t,r){var e=nr(r.location,t);return Xt(r.location,e/r.params.value,t)},Clear:function(t,r){var e=nr(r.location,t);return Xt(r.location,ar(e),t)},Reverse:function(t,r){var e=nr(r.location,t);return Xt(r.location,Ga(e),t)},Remove:function(t,r){var e=nr(r.location,t);return Xt(r.location,e.filter((function(t){return!Mt(t,r.params.value)})),t)}};function Wa(t,r){for(var e=t,n=0;n<r.operations.length;n++){var o=r.operations[n];o.location=Ja(o.location,e);var i=Ya[o.operation];if(!i)throw new Error("Invalid Operation ".concat(o.operation));e=i(e,o)}return e}function Va(t){return Va="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Va(t)}function Ka(){Ka=function(){return r};var t,r={},e=Object.prototype,n=e.hasOwnProperty,o=Object.defineProperty||function(t,r,e){t[r]=e.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function s(t,r,e){return Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}),t[r]}try{s({},"")}catch(t){s=function(t,r,e){return t[r]=e}}function l(t,r,e,n){var i=r&&r.prototype instanceof b?r:b,a=Object.create(i.prototype),u=new T(n||[]);return o(a,"_invoke",{value:x(t,e,u)}),a}function f(t,r,e){try{return{type:"normal",arg:t.call(r,e)}}catch(t){return{type:"throw",arg:t}}}r.wrap=l;var p="suspendedStart",y="suspendedYield",h="executing",d="completed",v={};function b(){}function m(){}function g(){}var w={};s(w,a,(function(){return this}));var O=Object.getPrototypeOf,S=O&&O(O(L([])));S&&S!==e&&n.call(S,a)&&(w=S);var E=g.prototype=b.prototype=Object.create(w);function j(t){["next","throw","return"].forEach((function(r){s(t,r,(function(t){return this._invoke(r,t)}))}))}function _(t,r){function e(o,i,a,u){var c=f(t[o],t,i);if("throw"!==c.type){var s=c.arg,l=s.value;return l&&"object"==Va(l)&&n.call(l,"__await")?r.resolve(l.__await).then((function(t){e("next",t,a,u)}),(function(t){e("throw",t,a,u)})):r.resolve(l).then((function(t){s.value=t,a(s)}),(function(t){return e("throw",t,a,u)}))}u(c.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new r((function(r,o){e(t,n,r,o)}))}return i=i?i.then(o,o):o()}})}function x(r,e,n){var o=p;return function(i,a){if(o===h)throw Error("Generator is already running");if(o===d){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var u=n.delegate;if(u){var c=P(u,n);if(c){if(c===v)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=d,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=h;var s=f(r,e,n);if("normal"===s.type){if(o=n.done?d:y,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=d,n.method="throw",n.arg=s.arg)}}}function P(r,e){var n=e.method,o=r.iterator[n];if(o===t)return e.delegate=null,"throw"===n&&r.iterator.return&&(e.method="return",e.arg=t,P(r,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=f(o,r.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,v;var a=i.arg;return a?a.done?(e[r.resultName]=a.value,e.next=r.nextLoc,"return"!==e.method&&(e.method="next",e.arg=t),e.delegate=null,v):a:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,v)}function A(t){var r={tryLoc:t[0]};1 in t&&(r.catchLoc=t[1]),2 in t&&(r.finallyLoc=t[2],r.afterLoc=t[3]),this.tryEntries.push(r)}function k(t){var r=t.completion||{};r.type="normal",delete r.arg,t.completion=r}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(A,this),this.reset(!0)}function L(r){if(r||""===r){var e=r[a];if(e)return e.call(r);if("function"==typeof r.next)return r;if(!isNaN(r.length)){var o=-1,i=function e(){for(;++o<r.length;)if(n.call(r,o))return e.value=r[o],e.done=!1,e;return e.value=t,e.done=!0,e};return i.next=i}}throw new TypeError(Va(r)+" is not iterable")}return m.prototype=g,o(E,"constructor",{value:g,configurable:!0}),o(g,"constructor",{value:m,configurable:!0}),m.displayName=s(g,c,"GeneratorFunction"),r.isGeneratorFunction=function(t){var r="function"==typeof t&&t.constructor;return!!r&&(r===m||"GeneratorFunction"===(r.displayName||r.name))},r.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,g):(t.__proto__=g,s(t,c,"GeneratorFunction")),t.prototype=Object.create(E),t},r.awrap=function(t){return{__await:t}},j(_.prototype),s(_.prototype,u,(function(){return this})),r.AsyncIterator=_,r.async=function(t,e,n,o,i){void 0===i&&(i=Promise);var a=new _(l(t,e,n,o),i);return r.isGeneratorFunction(e)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},j(E),s(E,c,"Generator"),s(E,a,(function(){return this})),s(E,"toString",(function(){return"[object Generator]"})),r.keys=function(t){var r=Object(t),e=[];for(var n in r)e.push(n);return e.reverse(),function t(){for(;e.length;){var n=e.pop();if(n in r)return t.value=n,t.done=!1,t}return t.done=!0,t}},r.values=L,T.prototype={constructor:T,reset:function(r){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(k),!r)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(r){if(this.done)throw r;var e=this;function o(n,o){return u.type="throw",u.arg=r,e.next=n,o&&(e.method="next",e.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(c&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,r){for(var e=this.tryEntries.length-1;e>=0;--e){var o=this.tryEntries[e];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=r&&r<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=r,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,r){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&r&&(this.next=r),v},finish:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e.finallyLoc===t)return this.complete(e.completion,e.afterLoc),k(e),v}},catch:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e.tryLoc===t){var n=e.completion;if("throw"===n.type){var o=n.arg;k(e)}return o}}throw Error("illegal catch attempt")},delegateYield:function(r,e,n){return this.delegate={iterator:L(r),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=t),v}},r}function Qa(t,r,e,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void e(t)}u.done?r(c):Promise.resolve(c).then(n,o)}var Xa=it(console.warn),Za={GET:function(t,r){return fetch(t,Jo(r,{method:"GET",headers:Ta()}))},POST:function(t,r){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return fetch(t,Jo(r,{method:"POST",headers:Ta(),body:e?JSON.stringify(e):null}))}};function tu(t,r,e,n,o){return function(){var i,a=(i=Ka().mark((function i(a,u){var c,s,l,f,p,y,h,d,v,b,m,g;return Ka().wrap((function(i){for(;;)switch(i.prev=i.next){case 0:y=function(t){u().error.backEndConnected!==t&&a({type:"SET_CONNECTION_STATUS",payload:t})},c=u(),s=c.config,l=c.hooks,f=null,p="".concat(wn(s)).concat(t),a({type:e,payload:{id:n,status:"loading"}}),i.prev=5,d=0;case 7:if(!(d<=1)){i.next=36;break}return i.prev=8,i.next=11,Za[r](p,s.fetch,o);case 11:h=i.sent,i.next=19;break;case 14:return i.prev=14,i.t0=i.catch(8),console.log("fetch error",h),y(!1),i.abrupt("return");case 19:if(401!==h.status&&400!==h.status){i.next=32;break}if(!l.request_refresh_jwt){i.next=32;break}return i.next=23,h.text();case 23:if(!i.sent.includes(pi)){i.next=32;break}return i.next=27,l.request_refresh_jwt(s.fetch.headers.Authorization.substr(7));case 27:if(!(v=i.sent)){i.next=32;break}return f={Authorization:"Bearer ".concat(v)},s=Jo(s,{fetch:{headers:f}}),i.abrupt("continue",33);case 32:return i.abrupt("break",36);case 33:d++,i.next=7;break;case 36:if(b=h.headers.get("content-type"),f&&a(wa(f)),y(!0),!b||-1===b.indexOf("application/json")){i.next=41;break}return i.abrupt("return",h.json().then((function(t){return a({type:e,payload:{status:h.status,content:t,id:n}}),t})));case 41:return i.next=43,h.text();case 43:return m=i.sent,Xa("Response is missing header: content-type: application/json"),i.abrupt("return",a({type:e,payload:{id:n,status:h.status,content:m}}));case 48:i.prev=48,i.t1=i.catch(5),g="Error from API call: "+t,Da(i.t1,g,a);case 52:case"end":return i.stop()}}),i,null,[[5,48],[8,14]])})),function(){var t=this,r=arguments;return new Promise((function(e,n){var o=i.apply(t,r);function a(t){Qa(o,e,n,a,u,"next",t)}function u(t){Qa(o,e,n,a,u,"throw",t)}a(void 0)}))});return function(t,r){return a.apply(this,arguments)}}()}function ru(t){return ru="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ru(t)}function eu(t,r){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);r&&(n=n.filter((function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable}))),e.push.apply(e,n)}return e}function nu(t){for(var r=1;r<arguments.length;r++){var e=null!=arguments[r]?arguments[r]:{};r%2?eu(Object(e),!0).forEach((function(r){ou(t,r,e[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):eu(Object(e)).forEach((function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(e,r))}))}return t}function ou(t,r,e){return(r=function(t){var r=function(t){if("object"!=ru(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var e=r.call(t,"string");if("object"!=ru(e))return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==ru(r)?r:r+""}(r))in t?Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[r]=e,t}function iu(t){var r,e=document.querySelector("head");if("_js_dist"===t.type){var n=document.createElement("script");n.src=t.url,n.async=!0,r=new Promise((function(t,r){n.onload=function(){t()},n.onerror=function(t){return r(t)}})),null==e||e.appendChild(n)}else if("_css_dist"===t.type){var o=document.createElement("link");o.href=t.url,o.rel="stylesheet",r=new Promise((function(t,r){o.onload=function(){t()},o.onerror=function(t){return r(t)}})),null==e||e.appendChild(o)}return r}function au(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function uu(t){var r,e,n=t.lastIndexOf("}");return n+2<t.length?(e=t.substring(n+2),r=JSON.parse(t.substring(0,n+1))):r=JSON.parse(t),[r,e]}function cu(t,r,e){var n=It(t),o=n.join(",");return r.paths.objs[o].map((function(t){return n.reduce((function(r,e,n){return r[e]=t.values[n],r}),{})})).filter((function(r){return Mt(Ri(e,r),Ri(e,t))}))}var su=pa("LOADING"),lu=pa("LOADED");function fu(t){return fu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},fu(t)}function pu(){pu=function(){return r};var t,r={},e=Object.prototype,n=e.hasOwnProperty,o=Object.defineProperty||function(t,r,e){t[r]=e.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function s(t,r,e){return Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}),t[r]}try{s({},"")}catch(t){s=function(t,r,e){return t[r]=e}}function l(t,r,e,n){var i=r&&r.prototype instanceof b?r:b,a=Object.create(i.prototype),u=new T(n||[]);return o(a,"_invoke",{value:x(t,e,u)}),a}function f(t,r,e){try{return{type:"normal",arg:t.call(r,e)}}catch(t){return{type:"throw",arg:t}}}r.wrap=l;var p="suspendedStart",y="suspendedYield",h="executing",d="completed",v={};function b(){}function m(){}function g(){}var w={};s(w,a,(function(){return this}));var O=Object.getPrototypeOf,S=O&&O(O(L([])));S&&S!==e&&n.call(S,a)&&(w=S);var E=g.prototype=b.prototype=Object.create(w);function j(t){["next","throw","return"].forEach((function(r){s(t,r,(function(t){return this._invoke(r,t)}))}))}function _(t,r){function e(o,i,a,u){var c=f(t[o],t,i);if("throw"!==c.type){var s=c.arg,l=s.value;return l&&"object"==fu(l)&&n.call(l,"__await")?r.resolve(l.__await).then((function(t){e("next",t,a,u)}),(function(t){e("throw",t,a,u)})):r.resolve(l).then((function(t){s.value=t,a(s)}),(function(t){return e("throw",t,a,u)}))}u(c.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new r((function(r,o){e(t,n,r,o)}))}return i=i?i.then(o,o):o()}})}function x(r,e,n){var o=p;return function(i,a){if(o===h)throw Error("Generator is already running");if(o===d){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var u=n.delegate;if(u){var c=P(u,n);if(c){if(c===v)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=d,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=h;var s=f(r,e,n);if("normal"===s.type){if(o=n.done?d:y,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=d,n.method="throw",n.arg=s.arg)}}}function P(r,e){var n=e.method,o=r.iterator[n];if(o===t)return e.delegate=null,"throw"===n&&r.iterator.return&&(e.method="return",e.arg=t,P(r,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=f(o,r.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,v;var a=i.arg;return a?a.done?(e[r.resultName]=a.value,e.next=r.nextLoc,"return"!==e.method&&(e.method="next",e.arg=t),e.delegate=null,v):a:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,v)}function A(t){var r={tryLoc:t[0]};1 in t&&(r.catchLoc=t[1]),2 in t&&(r.finallyLoc=t[2],r.afterLoc=t[3]),this.tryEntries.push(r)}function k(t){var r=t.completion||{};r.type="normal",delete r.arg,t.completion=r}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(A,this),this.reset(!0)}function L(r){if(r||""===r){var e=r[a];if(e)return e.call(r);if("function"==typeof r.next)return r;if(!isNaN(r.length)){var o=-1,i=function e(){for(;++o<r.length;)if(n.call(r,o))return e.value=r[o],e.done=!1,e;return e.value=t,e.done=!0,e};return i.next=i}}throw new TypeError(fu(r)+" is not iterable")}return m.prototype=g,o(E,"constructor",{value:g,configurable:!0}),o(g,"constructor",{value:m,configurable:!0}),m.displayName=s(g,c,"GeneratorFunction"),r.isGeneratorFunction=function(t){var r="function"==typeof t&&t.constructor;return!!r&&(r===m||"GeneratorFunction"===(r.displayName||r.name))},r.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,g):(t.__proto__=g,s(t,c,"GeneratorFunction")),t.prototype=Object.create(E),t},r.awrap=function(t){return{__await:t}},j(_.prototype),s(_.prototype,u,(function(){return this})),r.AsyncIterator=_,r.async=function(t,e,n,o,i){void 0===i&&(i=Promise);var a=new _(l(t,e,n,o),i);return r.isGeneratorFunction(e)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},j(E),s(E,c,"Generator"),s(E,a,(function(){return this})),s(E,"toString",(function(){return"[object Generator]"})),r.keys=function(t){var r=Object(t),e=[];for(var n in r)e.push(n);return e.reverse(),function t(){for(;e.length;){var n=e.pop();if(n in r)return t.value=n,t.done=!1,t}return t.done=!0,t}},r.values=L,T.prototype={constructor:T,reset:function(r){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(k),!r)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(r){if(this.done)throw r;var e=this;function o(n,o){return u.type="throw",u.arg=r,e.next=n,o&&(e.method="next",e.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(c&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,r){for(var e=this.tryEntries.length-1;e>=0;--e){var o=this.tryEntries[e];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=r&&r<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=r,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,r){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&r&&(this.next=r),v},finish:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e.finallyLoc===t)return this.complete(e.completion,e.afterLoc),k(e),v}},catch:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e.tryLoc===t){var n=e.completion;if("throw"===n.type){var o=n.arg;k(e)}return o}}throw Error("illegal catch attempt")},delegateYield:function(r,e,n){return this.delegate={iterator:L(r),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=t),v}},r}function yu(t,r){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);r&&(n=n.filter((function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable}))),e.push.apply(e,n)}return e}function hu(t){for(var r=1;r<arguments.length;r++){var e=null!=arguments[r]?arguments[r]:{};r%2?yu(Object(e),!0).forEach((function(r){du(t,r,e[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):yu(Object(e)).forEach((function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(e,r))}))}return t}function du(t,r,e){return(r=function(t){var r=function(t){if("object"!=fu(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var e=r.call(t,"string");if("object"!=fu(e))return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==fu(r)?r:r+""}(r))in t?Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[r]=e,t}function vu(t,r,e,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void e(t)}u.done?r(c):Promise.resolve(c).then(n,o)}function bu(t){return function(){var r=this,e=arguments;return new Promise((function(n,o){var i=t.apply(r,e);function a(t){vu(i,n,o,a,u,"next",t)}function u(t){vu(i,n,o,a,u,"throw",t)}a(void 0)}))}}function mu(t){return function(t){if(Array.isArray(t))return Ou(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||wu(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function gu(t,r){return function(t){if(Array.isArray(t))return t}(t)||function(t,r){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var n,o,i,a,u=[],c=!0,s=!1;try{if(i=(e=e.call(t)).next,0===r){if(Object(e)!==e)return;c=!1}else for(;!(c=(n=i.call(e)).done)&&(u.push(n.value),u.length!==r);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(t,r)||wu(t,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function wu(t,r){if(t){if("string"==typeof t)return Ou(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Ou(t,r):void 0}}function Ou(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}var Su=pa(Co.AddBlocked),Eu=pa(No.AddCompleted),ju=pa(Co.AddExecuted),_u=pa(Co.AddExecuting),xu=pa(Co.AddPrioritized),Pu=pa(Co.AddRequested),Au=pa(Co.AddStored),ku=pa(Co.AddWatched),Tu=pa(Co.RemoveExecuted),Lu=pa(Co.RemoveBlocked),Iu=pa(Co.RemoveExecuting),Cu=pa(Co.RemovePrioritized),Nu=pa(Co.RemoveRequested),Ru=pa(Co.RemoveStored),Du=pa(Co.RemoveWatched),Mu=pa(No.Aggregate),Uu=pa("UPDATE_RESOURCE_USAGE"),qu=pa("ADD_CALLBACK_JOB"),Gu=pa("REMOVE_CALLBACK_JOB"),Fu=pa("CALLBACK_JOB_OUTDATED");function Bu(t,r,e,n,o){var i="";if(eo(e))return[r,i];if(1!==r.length)if(r.length)i="Multiple objects were found for an `"+o+"` of a callback that only takes one value. The id spec is "+JSON.stringify(e.id)+(n?" with MATCH values "+n:"")+" and the property is `"+e.property+"`. The objects we found are: "+JSON.stringify(Cr(aa(["id","property"]),r));else{var a="string"==typeof e.id;i="A nonexistent object was used in an `"+o+"` of a Dash callback. The id of this object is "+(a?"`"+e.id+"`":JSON.stringify(e.id)+(n?" with MATCH values "+n:""))+" and the property is `"+e.property+(a?"`. The string ids in the current layout are: ["+It(t.strs).join(", ")+"]":"`. The wildcard ids currently available are logged above.")}return[r[0],i]}function Hu(t,r,e,n,o){var i=arguments.length>5&&void 0!==arguments[5]&&arguments[5],a="Input"===o?e.getInputs:e.getState,u=[],c=0,s=a(t).map((function(i,a){var s=gu(Bu(t,i.map((function(t){var e=t.id,n=t.property,o=t.path;return{id:e,property:n,value:nr([].concat(mu(o),["props",n]),r)}})),n[a],e.anyVals,o),2),l=s[0],f=s[1];return eo(n[a])&&!l.length&&c++,f&&u.push(f),l}));if(u.length){if(i&&u.length+c===s.length)return null;$u(u,t)}return s}function $u(t,r){var e=t[0];throw-1!==e.indexOf("logged above")&&console.error(r.objs),new ReferenceError(e)}var zu=function(t){return Array.isArray(t)?ce("value",t):t.value},Ju=function(t,r){return Array.isArray(t)?_e(t,r):[[t,r]]};function Yu(t){return t.split("@")[0]}function Wu(t,r,e,n){return Vu.apply(this,arguments)}function Vu(){return Vu=bu(pu().mark((function t(r,e,n,o){var i,a,u,c,s,l,f,p,y,h,d,v,b,m,g,w,O;return pu().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if((i=window.dash_clientside=window.dash_clientside||{}).no_update||(Object.defineProperty(i,"no_update",{value:{description:"Return to prevent updating an Output."},writable:!1}),Object.defineProperty(i,"PreventUpdate",{value:{description:"Throw to prevent updating all Outputs."},writable:!1})),a=o.inputs,u=o.outputs,c=o.state,s=Date.now(),l=Xu(a),f=Xu(c),p={},y=yi,t.prev=8,v=e.namespace,b=e.function_name,m=a.map(zu),c&&(m=te(m,c.map(zu))),i.callback_context={},i.callback_context.triggered=o.changedPropIds.map((function(t){return{prop_id:t,value:l[t]}})),i.callback_context.triggered_id=Zu(o.changedPropIds),i.callback_context.inputs_list=a,i.callback_context.inputs=l,i.callback_context.states_list=c,i.callback_context.states=f,i.callback_context.outputs_list=u,g=(h=i[v])[b].apply(h,mu(m)),delete i.callback_context,"function"!=typeof(null===(d=g)||void 0===d?void 0:d.then)){t.next=26;break}return t.next=25,g;case 25:g=t.sent;case 26:u&&Ju(u,g).forEach((function(t){var r=gu(t,2),e=r[0],n=r[1];Ju(e,n).forEach((function(t){var r=gu(t,2),e=r[0],n=r[1],o=e.id,a=e.property,u=zn(o),c=p[u]=p[u]||{};n!==i.no_update&&(c[Yu(a)]=n)}))})),t.next=37;break;case 29:if(t.prev=29,t.t0=t.catch(8),t.t0!==i.PreventUpdate){t.next=35;break}y=204,t.next=37;break;case 35:throw y="CLIENTSIDE_ERROR",t.t0;case 37:return t.prev=37,delete i.callback_context,w=Date.now()-s,O={__dash_server:w,__dash_client:w,__dash_upload:0,__dash_download:0},n.ui&&r(Uu({id:o.output,usage:O,status:y,result:p,inputs:a,state:c})),t.finish(37);case 43:return t.abrupt("return",p);case 44:case"end":return t.stop()}}),t,null,[[8,29,37,43]])}))),Vu.apply(this,arguments)}function Ku(t,r){return function(e,n){ia(t).reduce((function(t,e,o){var i,a=gu(e,2),u=a[0],c=a[1],s=u,l=[];if(u.startsWith("{")){var f=gu(uu(u),2);s=f[0],i=f[1],l=function(t,r,e,n){var o=[],i={};return ia(t).forEach((function(a){var u,c,s=(c=2,function(t){if(Array.isArray(t))return t}(u=a)||function(t,r){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var n,o,i,a,u=[],c=!0,s=!1;try{if(i=(e=e.call(t)).next,0===r){if(Object(e)!==e)return;c=!1}else for(;!(c=(n=i.call(e)).done)&&(u.push(n.value),u.length!==r);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(u,c)||function(t,r){if(t){if("string"==typeof t)return au(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?au(t,r):void 0}}(u,c)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),l=s[0],f=s[1];if(!o.length)if(Array.isArray(f)){var p=(r.parsedChangedPropsIds[e]||r.parsedChangedPropsIds[0])[l];f.includes("MATCH")?i[l]=p:f.includes("ALL")?o=cu(t,n(),l):f.includes("ALLSMALLER")&&(o=cu(t,n(),l).filter((function(t){return t[l]<p})))}else i[l]=f})),o.length?o:[i]}(s,r,o,n)}else if(u.includes(".")){var p=gu(u.split("."),2);s=p[0],i=p[1]}var y=i?du({},i,c):c;return 0===l.length?t.push([s,y]):1===l.length?t.push([l[0],y]):l.forEach((function(r){t.push([r,y])})),t}),[]).forEach((function(t){var n=gu(t,2),o=n[0],i=n[1];e(function(t,r,e){return function(n,o){var i=o(),a=i.paths,u=i.config,c=xn(a,t);c?(n(_a({props:r,itempath:c,renderType:"callback"})),n(Ra({id:t,props:r}))):u.suppress_callback_exceptions||Aa(n)("ID running component not found in layout",["Component defined in running keyword not found in layout.",'Component id: "'.concat(zn(t),'"'),"This ID was used in the callback(s) for Output(s):","".concat(e.output),"You can suppress this exception by setting","`suppress_callback_exceptions=True`."])}}(o,i,r))}))}}function Qu(t,r,e,n,o,i,a,u){r.request_pre&&r.request_pre(n);var c,s,l,f,p=Date.now(),y=JSON.stringify(n),h=i;return u&&(t(Ku(u.running,n)),l=u.runningOff),new Promise((function(i,u){var d=function(h){var d=h.status;if(s){var v=a().callbackJobs[s];if(null!=v&&v.outdated)return t(Gu({jobId:s})),i({})}function m(r){if(e.ui){var o={__dash_server:0,__dash_client:Date.now()-p,__dash_upload:y.length,__dash_download:Number(h.headers.get("Content-Length"))};(h.headers.get("Server-Timing")||"").split(",").forEach((function(t){var r=t.split(";")[0],e=t.match(/;dur=[0-9.]+/);e&&(o[r]=Number(e[0].slice(5)))})),t(Uu({id:n.output,usage:o,status:d,result:r,inputs:n.inputs,state:n.state}))}}var g=function(t){var e,o=t.multi,a=t.response;if(r.request_post&&r.request_post(n,a),o)e=a;else{var u=n.output;e=du({},u.substr(0,u.lastIndexOf(".")),a.props)}m(e),i(e)},w=function(){s&&t(Gu({jobId:s})),l&&t(Ku(l,n)),f&&t(Ku(f,n))};d===yi?h.json().then((function(r){if(!c&&r.cacheKey&&(c=r.cacheKey),!s&&r.job){var e={jobId:r.job,cacheKey:r.cacheKey,cancelInputs:r.cancel,progressDefault:r.progressDefault,output:JSON.stringify(n.outputs)};t(qu(e)),s=r.job}r.sideUpdate&&t(Ku(r.sideUpdate,n)),r.progress&&t(Ku(r.progress,n)),!f&&r.progressDefault&&(f=r.progressDefault),o&&void 0===r.response?setTimeout(b,void 0!==o.interval?o.interval:500):r.dist?Promise.all(r.dist.map(iu)).then((function(){w(),g(r)})):(w(),g(r))})):204===d?(w(),m({}),i({})):(w(),u(h))},v=function(){e.ui&&t(Uu({id:n.output,status:"NO_RESPONSE",result:{},inputs:n.inputs,state:n.state})),u(new Error("Callback failed: the server did not respond."))},b=function(){(function(){var t=Ta(),r="".concat(wn(e),"_dash-update-component"),n=y,o=function(t,e){var n="?";r.includes("?")&&(n="&"),r="".concat(r).concat(n).concat(t,"=").concat(e)};if(c||s){c&&o("cacheKey",c),s&&o("job",s);for(var i=JSON.parse(n),a=0;a<i.inputs.length;a++)i.inputs[a].value=null;for(var u=0;u<((null==i?void 0:i.state)||[]).length;u++)i.state[u].value=null;n=JSON.stringify(i)}return h&&(h.forEach((function(t){var r=gu(t,2),e=r[0],n=r[1];return o(e,n)})),h=h.filter((function(t){var r=gu(t,3);return r[0],r[1],!r[2]}))),fetch(r,Jo(e.fetch,{method:"POST",headers:t,body:n}))})().then(d,v)};b()}))}function Xu(t){if(!t)return{};for(var r={},e=0;e<t.length;e++){var n;if(Array.isArray(t[e]))for(var o=t[e],i=0;i<o.length;i++){var a;r["".concat(zn(o[i].id),".").concat(o[i].property)]=null!==(a=o[i].value)&&void 0!==a?a:null}else r["".concat(zn(t[e].id),".").concat(t[e].property)]=null!==(n=t[e].value)&&void 0!==n?n:null}return r}function Zu(t){if(t&&t.length){var r=t[0];return r.startsWith("{")?JSON.parse(r.substring(0,r.lastIndexOf("}")+1)):r.split(".")[0]}}function tc(t,r,e,o,i,a,u,c){var s=a.allOutputs,l=t.callback,f=l.output,p=l.inputs,y=l.state,h=l.clientside_function,d=l.background,v=l.dynamic_creator;try{var b=Hu(o,i,t,p,"Input",!0);if(null===b)return hu(hu({},t),{},{executionPromise:null});var m=[],g=[];if(s.forEach((function(r,e){var n=gu(Bu(o,Cr(aa(["id","property"]),r),t.callback.outputs[e],t.anyVals,"Output"),2),i=n[0],a=n[1];m.push(i),a&&g.push(a)})),g.length)return _r(b).length&&$u(g,o),hu(hu({},t),{},{executionPromise:null});var w=function(){var a=bu(pu().mark((function a(){var s,l,p,g,w,O,S,E,j,_,x,P,A;return pu().wrap((function(a){for(;;)switch(a.prev=a.next){case 0:if(s=m.map((function(t){var r;return{path:xn(o,t.id),property:null===(r=t.property)||void 0===r?void 0:r.split("@")[0],id:t.id}})),u(su(s)),a.prev=2,l=It(t.changedPropIds),p=l.map((function(t){return t.startsWith("{")?uu(t)[0]:t})),g={output:f,outputs:Rn(f)?m:m[0],inputs:b,changedPropIds:l,parsedChangedPropsIds:p,state:t.callback.state.length?Hu(o,i,t,y,"State"):void 0},!h){a.next=17;break}return a.prev=7,a.next=10,Wu(u,h,r,g);case 10:return w=a.sent,a.abrupt("return",{data:w,payload:g});case 14:return a.prev=14,a.t0=a.catch(7),a.abrupt("return",{error:a.t0,payload:g});case 17:O=r,S=null,j=[],_=JSON.stringify(g.outputs),Ce(c().callbackJobs).forEach((function(r){_===r.output&&(j.push(["oldJob",r.jobId,!0]),u(Fu({jobId:r.jobId}))),r.cancelInputs&&Ie(r.cancelInputs,t.callback.inputs).length&&(j.push(["cancelJob",r.jobId]),r.progressDefault&&u(Ku(r.progressDefault,g)))})),x=pu().mark((function i(){var a,s,l,f;return pu().wrap((function(i){for(;;)switch(i.prev=i.next){case 0:return i.prev=0,i.next=3,Qu(u,e,O,g,d,j.length?j:void 0,c,t.callback.running);case 3:return a=i.sent,S&&u(wa(S)),s=c().layout,_r(m).forEach((function(t){var r=Yu(t.property),e=xn(o,t.id),n=nr(e.concat(["props",r]),s),i=[zn(t.id),r],u=nr(i,a);if(pn("__dash_patch_update",u)){if(void 0===n)throw new Error("Cannot patch undefined");a=Xt(i,Wa(n,u),a)}})),v&&setTimeout((function(){return u((function(t,r){(0,n.unstable_batchedUpdates)((function(){var e=r().graphs;t(Oa(nu(nu({},e),{},{reset:!0}))),t(tu("_dash-dependencies","GET","dependenciesRequest"))}))}))}),0),i.abrupt("return",{v:{data:a,payload:g}});case 11:if(i.prev=11,i.t0=i.catch(0),E=i.t0,!(A<=1)||401!==i.t0.status&&400!==i.t0.status){i.next=29;break}return i.next=17,i.t0.text();case 17:if(!i.sent.includes(pi)){i.next=29;break}if(null===e.request_refresh_jwt){i.next=29;break}return l=null,r.fetch.headers.Authorization&&(l=r.fetch.headers.Authorization.substr(7)),i.next=24,e.request_refresh_jwt(l);case 24:if(!(f=i.sent)){i.next=29;break}return S={Authorization:"Bearer ".concat(f)},O=Jo(r,{fetch:{headers:S}}),i.abrupt("return",0);case 29:return i.abrupt("return",1);case 30:case"end":return i.stop()}}),i,null,[[0,11]])})),A=0;case 24:if(!(A<=1)){a.next=36;break}return a.delegateYield(x(),"t1",26);case 26:if(0!==(P=a.t1)){a.next=29;break}return a.abrupt("continue",33);case 29:if(1!==P){a.next=31;break}return a.abrupt("break",36);case 31:if(!P){a.next=33;break}return a.abrupt("return",P.v);case 33:A++,a.next=24;break;case 36:return a.abrupt("return",{error:E,payload:null});case 39:return a.prev=39,a.t2=a.catch(2),a.abrupt("return",{error:a.t2,payload:null});case 42:return a.prev=42,u(lu(s)),a.finish(42);case 45:case"end":return a.stop()}}),a,null,[[2,39,42,45],[7,14]])})));return function(){return a.apply(this,arguments)}}();return hu(hu({},t),{},{executionPromise:w()})}catch(r){return hu(hu({},t),{},{executionPromise:{error:r,payload:null}})}}var rc=nt((function(t){return function(){return t}})),ec=function(t){return{value:t,map:function(r){return ec(r(t))}}},nc=Bt((function(t,r,e){return t((function(t){return ec(r(t))}))(e).value})),oc=Bt((function(t,r,e){return nc(t,rc(r),e)}));function ic(t,r){return function(t){if(Array.isArray(t))return t}(t)||function(t,r){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var n,o,i,a,u=[],c=!0,s=!1;try{if(i=(e=e.call(t)).next,0===r){if(Object(e)!==e)return;c=!1}else for(;!(c=(n=i.call(e)).done)&&(u.push(n.value),u.length!==r);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(t,r)||function(t,r){if(t){if("string"==typeof t)return ac(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?ac(t,r):void 0}}(t,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ac(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function uc(t){return uc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},uc(t)}function cc(t,r){if(!(t instanceof r))throw new TypeError("Cannot call a class as a function")}function sc(t,r){for(var e=0;e<r.length;e++){var n=r[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fc(n.key),n)}}function lc(t,r,e){return r&&sc(t.prototype,r),e&&sc(t,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function fc(t){var r=function(t){if("object"!=uc(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var e=r.call(t,"string");if("object"!=uc(e))return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==uc(r)?r:r+""}var pc="_dash_persistence.";function yc(t){var r="string"==typeof t?new Error(t):t;return pa("ON_ERROR")({type:"frontEnd",error:r})}function hc(t,r){var e=t+r,n=e.length;return function(r){return r===t||r.substr(0,n)===e}}var dc=function(t){return"U"===t?void 0:JSON.parse(t||null)},vc=function(t){return void 0===t?"U":JSON.stringify(t)},bc=function(){return lc((function t(r){cc(this,t),this._name=r,this._storage=window[r]}),[{key:"hasItem",value:function(t){return null!==this._storage.getItem(pc+t)}},{key:"getItem",value:function(t){return dc(this._storage.getItem(pc+t))}},{key:"_setItem",value:function(t,r){this._storage.setItem(pc+t,vc(r))}},{key:"setItem",value:function(t,r,e){try{this._setItem(t,r)}catch(r){e(yc("".concat(t," failed to save in ").concat(this._name,". Persisted props may be lost.")))}}},{key:"removeItem",value:function(t){this._storage.removeItem(pc+t)}},{key:"clear",value:function(t){for(var r=this,e=hc(pc+(t||""),t?".":""),n=[],o=0;o<this._storage.length;o++){var i=this._storage.key(o);e(i)&&n.push(i)}er((function(t){return r._storage.removeItem(t)}),n)}}])}(),mc={memory:new(function(){return lc((function t(){cc(this,t),this._data={}}),[{key:"hasItem",value:function(t){return t in this._data}},{key:"getItem",value:function(t){return dc(this._data[t])}},{key:"setItem",value:function(t,r){this._data[t]=vc(r)}},{key:"removeItem",value:function(t){delete this._data[t]}},{key:"clear",value:function(t){var r=this;t?er((function(t){return delete r._data[t]}),Or(hc(t,"."),It(this._data))):this._data={}}}])}())},gc={local:"localStorage",session:"sessionStorage"};function wc(t,r){return mc[t]||(mc[t]=function(t,r){var e=new bc(t),n=mc.memory,o=function(){for(var t="Spam",r=2;r<16;r++)t+=t;return t}(),i=pc+"x.x";try{return e._setItem(i,o),e.getItem(i)!==o?(r(yc("".concat(t," init failed set/get, falling back to memory"))),n):(e.removeItem(i),e)}catch(e){r(yc("".concat(t," init first try failed; clearing and retrying")))}try{if(e.clear(),e._setItem(i,o),e.getItem(i)!==o)throw new Error("nope");return e.removeItem(i),r(yc("".concat(t," init set/get succeeded after clearing!"))),e}catch(e){return r(yc("".concat(t," init still failed, falling back to memory"))),n}}(gc[t],r)),mc[t]}var Oc={extract:function(t){return t},apply:function(t,r){return t}},Sc=function(t,r,e){return t.persistenceTransforms&&t.persistenceTransforms[r]?e?t.persistenceTransforms[r][e]:t.persistenceTransforms[r]:Oc},Ec=function(t,r,e){return"".concat(zn(t),".").concat(r,".").concat(JSON.stringify(e))},jc=function(t){var r=t.props,e=t.type,n=t.namespace;if(!e||!n)return{props:r};var o=r.id,i=r.persistence,a=Pn(t),u=function(t){return r[t]||(a.defaultProps||a.dashPersistence||{})[t]},c=u("persisted_props"),s=u("persistence_type");return{canPersist:o&&c&&s,id:o,props:r,element:a,persistence:i,persisted_props:c,persistence_type:s}};function _c(t,r){return"Object"===Ct(t)&&t.props?Pc(t,t,[],r):t}function xc(t,r,e,n,o,i,a){if(r.hasItem(t)){var u=ic(r.getItem(t),2),c=u[0],s=u[1],l=a?c:s,f=a?s:c,p=ic(o.split("."),2),y=p[0],h=p[1],d=Sc(e,y,h);Mt(l,d.extract(n[y]))?i[y]=d.apply(f,y in i?i[y]:n[y]):r.removeItem(t)}}function Pc(t,r,e,n){var o=jc(r),i=o.canPersist,a=o.id,u=o.props,c=o.element,s=o.persistence,l=o.persisted_props,f=o.persistence_type,p=t;if(i&&s){var y=wc(f,n),h={};for(var d in er((function(t){return xc(Ec(a,t,s),y,c,u,t,h)}),l),h)p=oc(_i(e.concat("props",d)),h[d],p)}var v=u.children;return Array.isArray(v)?v.forEach((function(t,r){"Object"===Ct(t)&&t.props&&(p=Pc(p,t,e.concat("props","children",r),n))})):"Object"===Ct(v)&&v.props&&(p=Pc(p,v,e.concat("props","children"),n)),p}function Ac(t){return Ac="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ac(t)}function kc(t,r){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);r&&(n=n.filter((function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable}))),e.push.apply(e,n)}return e}function Tc(t){for(var r=1;r<arguments.length;r++){var e=null!=arguments[r]?arguments[r]:{};r%2?kc(Object(e),!0).forEach((function(r){Lc(t,r,e[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):kc(Object(e)).forEach((function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(e,r))}))}return t}function Lc(t,r,e){return(r=function(t){var r=function(t){if("object"!=Ac(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var e=r.call(t,"string");if("object"!=Ac(e))return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Ac(r)?r:r+""}(r))in t?Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[r]=e,t}function Ic(t,r){return function(t){if(Array.isArray(t))return t}(t)||function(t,r){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var n,o,i,a,u=[],c=!0,s=!1;try{if(i=(e=e.call(t)).next,0===r){if(Object(e)!==e)return;c=!1}else for(;!(c=(n=i.call(e)).done)&&(u.push(n.value),u.length!==r);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(t,r)||function(t,r){if(t){if("string"==typeof t)return Cc(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Cc(t,r):void 0}}(t,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Cc(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}var Nc={observer:function(t){var r=t.dispatch,e=t.getState,n=e().callbacks.executed;var o=[],i=[];n.forEach((function(t){var n,a=te(null!==(n=t.predecessors)&&void 0!==n?n:[],[t.callback]),u=t.callback,c=u.clientside_function,s=u.output,l=t.executionResult;if(!Kt(l)){var f=l.data,p=l.error,y=l.payload;if(void 0!==f&&(Object.entries(f).forEach((function(t){var n=Ic(t,2),i=n[0],u=n[1],c=$n(i),s=e(),l=s.graphs,f=s.layout,p=s.paths,y=function(t,n){var o=e(),i=o.layout,a=xn(o.paths,t);if(!a)return!1;n=function(t,r,e){var n=jc(t),o=n.canPersist,i=n.id,a=n.props,u=n.persistence,c=n.persisted_props,s=n.persistence_type,l=n.element,f=function(t,e){return t in r?r[t]:e},p=f("persistence",u);if(!o||!u&&!p)return r;var y=f("persistence_type",s),h=f("persisted_props",c),d=p!==u||y!==s||h!==c,v=function(t){return!(t.split(".")[0]in r)},b={},m=a;if(d&&u){var g=wc(s,e);er((function(t){return xc(Ec(i,t,u),g,l,a,t,b,!0)}),Or(v,c)),m=Je(a,b)}if(p){var w=wc(y,e);d&&er((function(t){return xc(Ec(i,t,p),w,l,m,t,b)}),Or(v,h));var O=l.persistenceTransforms||{};for(var S in r){var E=O[S];if(E)for(var j in E)w.removeItem(Ec(i,"".concat(S,".").concat(j),p));else w.removeItem(Ec(i,S,p))}}return d?Je(r,b):r}(nr(a,i),n,r);var u=_c({props:n},r).props;return r(_a({itempath:a,props:u,source:"response",renderType:"callback"})),u}(c,u);o=te(o,_r(Cr((function(t){return go(l,p,c,t,!0)}),It(u))).map((function(t){return Tc(Tc({},t),{},{predecessors:a})})));var h=xn(p,c);if(h){var d=nr(h,f),v=Wt("defaultValue",[d.namespace,d.type],window.__dashprivate_childrenProps),b=function(t,n,i){var u=arguments.length>3&&void 0!==arguments[3]&&arguments[3],c=_n(t,i,e().paths);r(ja(c)),o=te(o,So(l,c,t,{chunkPath:i,filterRoot:u}).map((function(t){return Tc(Tc({},t),{},{predecessors:a})}))),o=te(o,So(l,p,n,{removedArrayInputsOnly:!0,newPaths:c,chunkPath:i,filterRoot:u}).map((function(t){return Tc(Tc({},t),{},{predecessors:a})})))},m=!1;["children"].concat(v).forEach((function(t){if(!m)if(t.includes("[]")){var r=Ic(t.split("[]").map((function(t){return t.split(".").filter((function(t){return t}))})),1)[0];if(!nr(r,y))return;b(Tc(Tc({},d),{},{props:Tc(Tc({},d.props),y)}),d,h,It(y)),m=!0}else{var e=t.split("."),n=nr(e,y);if(!n)return;var o=te(xn(p,c),["props"].concat(e)),i=nr(o,f);b(n,i,o)}}));var g=be((function(t,r){return!(r in u)}),y);if(!ur(g)){var w=e(),O=w.graphs,S=w.paths;o=te(o,jo(i,g,O,S).map((function(t){return Tc(Tc({},t),{},{predecessors:a})})))}}})),i.push(Tc(Tc({},t),{},{executionMeta:{allProps:Cr(mo,_r(t.getOutputs(e().paths))),updatedProps:_r(Cr((function(t){var r=Ic(t,2),e=r[0],n=r[1];return Cr((function(t){return mo({id:e,property:t})}),It(n))}),ia(f)))}}))),void 0!==p){var h;if(t.callback.no_output){var d=It(t.changedPropIds).join(", ");h="Callback error with no output from input ".concat(d)}else{var v=y?Cr(mo,_r([y.outputs])).join(", "):s;h="Callback error updating ".concat(v)}if(c){var b=c.namespace,m=c.function_name;h+=" via clientside function ".concat(b,".").concat(m)}Da(p,h,r),i.push(Tc(Tc({},t),{},{executionMeta:{allProps:Cr(mo,_r(t.getOutputs(e().paths))),updatedProps:[]}}))}}})),r(Mu([n.length?Tu(n):null,n.length?Eu(n.length):null,i.length?Au(i):null,o.length?Pu(o):null]))},inputs:["callbacks.executed"]},Rc=Nc;function Dc(t){return Dc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Dc(t)}function Mc(){Mc=function(){return r};var t,r={},e=Object.prototype,n=e.hasOwnProperty,o=Object.defineProperty||function(t,r,e){t[r]=e.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function s(t,r,e){return Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}),t[r]}try{s({},"")}catch(t){s=function(t,r,e){return t[r]=e}}function l(t,r,e,n){var i=r&&r.prototype instanceof b?r:b,a=Object.create(i.prototype),u=new T(n||[]);return o(a,"_invoke",{value:x(t,e,u)}),a}function f(t,r,e){try{return{type:"normal",arg:t.call(r,e)}}catch(t){return{type:"throw",arg:t}}}r.wrap=l;var p="suspendedStart",y="suspendedYield",h="executing",d="completed",v={};function b(){}function m(){}function g(){}var w={};s(w,a,(function(){return this}));var O=Object.getPrototypeOf,S=O&&O(O(L([])));S&&S!==e&&n.call(S,a)&&(w=S);var E=g.prototype=b.prototype=Object.create(w);function j(t){["next","throw","return"].forEach((function(r){s(t,r,(function(t){return this._invoke(r,t)}))}))}function _(t,r){function e(o,i,a,u){var c=f(t[o],t,i);if("throw"!==c.type){var s=c.arg,l=s.value;return l&&"object"==Dc(l)&&n.call(l,"__await")?r.resolve(l.__await).then((function(t){e("next",t,a,u)}),(function(t){e("throw",t,a,u)})):r.resolve(l).then((function(t){s.value=t,a(s)}),(function(t){return e("throw",t,a,u)}))}u(c.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new r((function(r,o){e(t,n,r,o)}))}return i=i?i.then(o,o):o()}})}function x(r,e,n){var o=p;return function(i,a){if(o===h)throw Error("Generator is already running");if(o===d){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var u=n.delegate;if(u){var c=P(u,n);if(c){if(c===v)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=d,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=h;var s=f(r,e,n);if("normal"===s.type){if(o=n.done?d:y,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=d,n.method="throw",n.arg=s.arg)}}}function P(r,e){var n=e.method,o=r.iterator[n];if(o===t)return e.delegate=null,"throw"===n&&r.iterator.return&&(e.method="return",e.arg=t,P(r,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=f(o,r.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,v;var a=i.arg;return a?a.done?(e[r.resultName]=a.value,e.next=r.nextLoc,"return"!==e.method&&(e.method="next",e.arg=t),e.delegate=null,v):a:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,v)}function A(t){var r={tryLoc:t[0]};1 in t&&(r.catchLoc=t[1]),2 in t&&(r.finallyLoc=t[2],r.afterLoc=t[3]),this.tryEntries.push(r)}function k(t){var r=t.completion||{};r.type="normal",delete r.arg,t.completion=r}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(A,this),this.reset(!0)}function L(r){if(r||""===r){var e=r[a];if(e)return e.call(r);if("function"==typeof r.next)return r;if(!isNaN(r.length)){var o=-1,i=function e(){for(;++o<r.length;)if(n.call(r,o))return e.value=r[o],e.done=!1,e;return e.value=t,e.done=!0,e};return i.next=i}}throw new TypeError(Dc(r)+" is not iterable")}return m.prototype=g,o(E,"constructor",{value:g,configurable:!0}),o(g,"constructor",{value:m,configurable:!0}),m.displayName=s(g,c,"GeneratorFunction"),r.isGeneratorFunction=function(t){var r="function"==typeof t&&t.constructor;return!!r&&(r===m||"GeneratorFunction"===(r.displayName||r.name))},r.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,g):(t.__proto__=g,s(t,c,"GeneratorFunction")),t.prototype=Object.create(E),t},r.awrap=function(t){return{__await:t}},j(_.prototype),s(_.prototype,u,(function(){return this})),r.AsyncIterator=_,r.async=function(t,e,n,o,i){void 0===i&&(i=Promise);var a=new _(l(t,e,n,o),i);return r.isGeneratorFunction(e)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},j(E),s(E,c,"Generator"),s(E,a,(function(){return this})),s(E,"toString",(function(){return"[object Generator]"})),r.keys=function(t){var r=Object(t),e=[];for(var n in r)e.push(n);return e.reverse(),function t(){for(;e.length;){var n=e.pop();if(n in r)return t.value=n,t.done=!1,t}return t.done=!0,t}},r.values=L,T.prototype={constructor:T,reset:function(r){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(k),!r)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(r){if(this.done)throw r;var e=this;function o(n,o){return u.type="throw",u.arg=r,e.next=n,o&&(e.method="next",e.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(c&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,r){for(var e=this.tryEntries.length-1;e>=0;--e){var o=this.tryEntries[e];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=r&&r<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=r,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,r){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&r&&(this.next=r),v},finish:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e.finallyLoc===t)return this.complete(e.completion,e.afterLoc),k(e),v}},catch:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e.tryLoc===t){var n=e.completion;if("throw"===n.type){var o=n.arg;k(e)}return o}}throw Error("illegal catch attempt")},delegateYield:function(r,e,n){return this.delegate={iterator:L(r),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=t),v}},r}function Uc(t,r){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);r&&(n=n.filter((function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable}))),e.push.apply(e,n)}return e}function qc(t){for(var r=1;r<arguments.length;r++){var e=null!=arguments[r]?arguments[r]:{};r%2?Uc(Object(e),!0).forEach((function(r){Gc(t,r,e[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):Uc(Object(e)).forEach((function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(e,r))}))}return t}function Gc(t,r,e){return(r=function(t){var r=function(t){if("object"!=Dc(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var e=r.call(t,"string");if("object"!=Dc(e))return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Dc(r)?r:r+""}(r))in t?Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[r]=e,t}function Fc(t,r,e,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void e(t)}u.done?r(c):Promise.resolve(c).then(n,o)}function Bc(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}var Hc={observer:function(t){var r,e,n=t.dispatch,o=t.getState,i=o().callbacks.executing,a=(r=pe((function(t){return t.executionPromise instanceof Promise}),i),e=2,function(t){if(Array.isArray(t))return t}(r)||function(t,r){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var n,o,i,a,u=[],c=!0,s=!1;try{if(i=(e=e.call(t)).next,0===r){if(Object(e)!==e)return;c=!1}else for(;!(c=(n=i.call(e)).done)&&(u.push(n.value),u.length!==r);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(r,e)||function(t,r){if(t){if("string"==typeof t)return Bc(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Bc(t,r):void 0}}(r,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),u=a[0],c=a[1];n(Mu([i.length?Iu(i):null,u.length?ku(u):null,c.length?ju(c.map((function(t){return Zt("executionResult",t.executionPromise,t)}))):null])),u.forEach(function(){var t,r=(t=Mc().mark((function t(r){var e,i,a,u,c,s;return Mc().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,r.executionPromise;case 2:if(e=t.sent,i=o(),a=i.callbacks.watched,u=i.appLifecycle,c=i.hooks.callback_resolved,u===To("HYDRATED")){t.next=6;break}return t.abrupt("return");case 6:if(c&&c(r.callback,e),s=cn((function(t){return t===r||t.executionPromise===r.executionPromise}),a)){t.next=10;break}return t.abrupt("return");case 10:n(Mu([Du([s]),ju([qc(qc({},s),{},{executionResult:e})])]));case 11:case"end":return t.stop()}}),t)})),function(){var r=this,e=arguments;return new Promise((function(n,o){var i=t.apply(r,e);function a(t){Fc(i,n,o,a,u,"next",t)}function u(t){Fc(i,n,o,a,u,"throw",t)}a(void 0)}))});return function(t){return r.apply(this,arguments)}}())},inputs:["callbacks.executing"]},$c=Hc,zc=wt((function(t,r){for(var e={},n={},o=0,i=t.length;o<i;)n[t[o]]=1,o+=1;for(var a in r)n.hasOwnProperty(a)||(e[a]=r[a]);return e}));function Jc(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}var Yc=function(t){var r,e;return(r=Array()).concat.apply(r,function(t){if(Array.isArray(t))return Jc(t)}(e=Ce(zc(["stored","completed"],t)))||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(e)||function(t,r){if(t){if("string"==typeof t)return Jc(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Jc(t,r):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())},Wc=pa(fi.Set),Vc={observer:function(t){var r=t.dispatch,e=(0,t.getState)(),n=e.callbacks,o=e.isLoading,i=Yc(n),a=Boolean(i.length);o!==a&&r(Wc(a))},inputs:["callbacks"]},Kc=Vc,Qc=wt((function(t,r){return Array.prototype.slice.call(r,0).sort(t)})),Xc=e(296),Zc=function(t,r,e){if(!e.length)return!0;var n=[],o=r.events,i=new Promise((function(t){o.once("rendered",t)}));return e.forEach((function(e){var o=xn(r,e);if(o){var a=nr(o,t);if(a){var u=Pn(a),c=(0,Xc.isReady)(u);c&&"function"==typeof c.then&&n.push(Promise.race([c,i.then((function(){return document.getElementById(zn(e))&&c}))]))}}})),!n.length||Promise.all(n)};function ts(t){return ts="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ts(t)}function rs(){rs=function(){return r};var t,r={},e=Object.prototype,n=e.hasOwnProperty,o=Object.defineProperty||function(t,r,e){t[r]=e.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function s(t,r,e){return Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}),t[r]}try{s({},"")}catch(t){s=function(t,r,e){return t[r]=e}}function l(t,r,e,n){var i=r&&r.prototype instanceof b?r:b,a=Object.create(i.prototype),u=new T(n||[]);return o(a,"_invoke",{value:x(t,e,u)}),a}function f(t,r,e){try{return{type:"normal",arg:t.call(r,e)}}catch(t){return{type:"throw",arg:t}}}r.wrap=l;var p="suspendedStart",y="suspendedYield",h="executing",d="completed",v={};function b(){}function m(){}function g(){}var w={};s(w,a,(function(){return this}));var O=Object.getPrototypeOf,S=O&&O(O(L([])));S&&S!==e&&n.call(S,a)&&(w=S);var E=g.prototype=b.prototype=Object.create(w);function j(t){["next","throw","return"].forEach((function(r){s(t,r,(function(t){return this._invoke(r,t)}))}))}function _(t,r){function e(o,i,a,u){var c=f(t[o],t,i);if("throw"!==c.type){var s=c.arg,l=s.value;return l&&"object"==ts(l)&&n.call(l,"__await")?r.resolve(l.__await).then((function(t){e("next",t,a,u)}),(function(t){e("throw",t,a,u)})):r.resolve(l).then((function(t){s.value=t,a(s)}),(function(t){return e("throw",t,a,u)}))}u(c.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new r((function(r,o){e(t,n,r,o)}))}return i=i?i.then(o,o):o()}})}function x(r,e,n){var o=p;return function(i,a){if(o===h)throw Error("Generator is already running");if(o===d){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var u=n.delegate;if(u){var c=P(u,n);if(c){if(c===v)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=d,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=h;var s=f(r,e,n);if("normal"===s.type){if(o=n.done?d:y,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=d,n.method="throw",n.arg=s.arg)}}}function P(r,e){var n=e.method,o=r.iterator[n];if(o===t)return e.delegate=null,"throw"===n&&r.iterator.return&&(e.method="return",e.arg=t,P(r,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=f(o,r.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,v;var a=i.arg;return a?a.done?(e[r.resultName]=a.value,e.next=r.nextLoc,"return"!==e.method&&(e.method="next",e.arg=t),e.delegate=null,v):a:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,v)}function A(t){var r={tryLoc:t[0]};1 in t&&(r.catchLoc=t[1]),2 in t&&(r.finallyLoc=t[2],r.afterLoc=t[3]),this.tryEntries.push(r)}function k(t){var r=t.completion||{};r.type="normal",delete r.arg,t.completion=r}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(A,this),this.reset(!0)}function L(r){if(r||""===r){var e=r[a];if(e)return e.call(r);if("function"==typeof r.next)return r;if(!isNaN(r.length)){var o=-1,i=function e(){for(;++o<r.length;)if(n.call(r,o))return e.value=r[o],e.done=!1,e;return e.value=t,e.done=!0,e};return i.next=i}}throw new TypeError(ts(r)+" is not iterable")}return m.prototype=g,o(E,"constructor",{value:g,configurable:!0}),o(g,"constructor",{value:m,configurable:!0}),m.displayName=s(g,c,"GeneratorFunction"),r.isGeneratorFunction=function(t){var r="function"==typeof t&&t.constructor;return!!r&&(r===m||"GeneratorFunction"===(r.displayName||r.name))},r.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,g):(t.__proto__=g,s(t,c,"GeneratorFunction")),t.prototype=Object.create(E),t},r.awrap=function(t){return{__await:t}},j(_.prototype),s(_.prototype,u,(function(){return this})),r.AsyncIterator=_,r.async=function(t,e,n,o,i){void 0===i&&(i=Promise);var a=new _(l(t,e,n,o),i);return r.isGeneratorFunction(e)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},j(E),s(E,c,"Generator"),s(E,a,(function(){return this})),s(E,"toString",(function(){return"[object Generator]"})),r.keys=function(t){var r=Object(t),e=[];for(var n in r)e.push(n);return e.reverse(),function t(){for(;e.length;){var n=e.pop();if(n in r)return t.value=n,t.done=!1,t}return t.done=!0,t}},r.values=L,T.prototype={constructor:T,reset:function(r){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(k),!r)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(r){if(this.done)throw r;var e=this;function o(n,o){return u.type="throw",u.arg=r,e.next=n,o&&(e.method="next",e.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(c&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,r){for(var e=this.tryEntries.length-1;e>=0;--e){var o=this.tryEntries[e];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=r&&r<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=r,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,r){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&r&&(this.next=r),v},finish:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e.finallyLoc===t)return this.complete(e.completion,e.afterLoc),k(e),v}},catch:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e.tryLoc===t){var n=e.completion;if("throw"===n.type){var o=n.arg;k(e)}return o}}throw Error("illegal catch attempt")},delegateYield:function(r,e,n){return this.delegate={iterator:L(r),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=t),v}},r}function es(t,r){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);r&&(n=n.filter((function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable}))),e.push.apply(e,n)}return e}function ns(t){for(var r=1;r<arguments.length;r++){var e=null!=arguments[r]?arguments[r]:{};r%2?es(Object(e),!0).forEach((function(r){os(t,r,e[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):es(Object(e)).forEach((function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(e,r))}))}return t}function os(t,r,e){return(r=function(t){var r=function(t){if("object"!=ts(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var e=r.call(t,"string");if("object"!=ts(e))return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==ts(r)?r:r+""}(r))in t?Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[r]=e,t}function is(t,r,e,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void e(t)}u.done?r(c):Promise.resolve(c).then(n,o)}function as(t){return function(){var r=this,e=arguments;return new Promise((function(n,o){var i=t.apply(r,e);function a(t){is(i,n,o,a,u,"next",t)}function u(t){is(i,n,o,a,u,"throw",t)}a(void 0)}))}}function us(t){return function(t){if(Array.isArray(t))return ss(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||cs(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function cs(t,r){if(t){if("string"==typeof t)return ss(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?ss(t,r):void 0}}function ss(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}var ls=function(t,r){var e,n;return(null!==(e=t.priority)&&void 0!==e?e:"")>(null!==(n=r.priority)&&void 0!==n?n:"")?-1:1},fs=function(t,r){var e=(0,t.getOutputs)(r),n=_r(e),o=[],i={};return n.forEach((function(t){var r=t.id,e=t.property,n=zn(r);(i[n]=i[n]||[]).push(e),o.push(mo({id:n,property:e}))})),{allOutputs:e,allPropIds:o}},ps=function(t,r){var e=[].concat(us(_r(t.getInputs(r))),us(_r(t.getState(r)))),n=new Map(e.map((function(t){return[zn(t.id),t]})));return Array.from(n.values())},ys={observer:function(){var t=as(rs().mark((function t(r){var e,n,o,i,a,u,c,s,l,f,p,y,h,d,v,b,m,g,w,O,S;return rs().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e=r.dispatch,n=r.getState,o=n(),i=o.callbacks,a=i.executing,u=i.watched,c=o.config,s=o.hooks,l=o.layout,f=o.paths,p=o.appLifecycle,y=n(),h=y.callbacks.prioritized,p===To("HYDRATED")){t.next=5;break}return t.abrupt("return");case 5:d=Math.max(0,12-a.length-u.length),h=Qc(ls,h),v=pe((function(t){return!0===Zc(l,f,ps(t,f))}),h),j=2,b=function(t){if(Array.isArray(t))return t}(E=v)||function(t,r){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var n,o,i,a,u=[],c=!0,s=!1;try{if(i=(e=e.call(t)).next,0===r){if(Object(e)!==e)return;c=!1}else for(;!(c=(n=i.call(e)).done)&&(u.push(n.value),u.length!==r);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(E,j)||cs(E,j)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),m=b[0],g=b[1],w=m.slice(0,d),O=g.slice(0,d-w.length),w.length&&e(Mu([Cu(w),_u(Cr((function(t){return tc(t,c,s,f,l,fs(t,f),e,n)}),w))])),O.length&&(S=Cr((function(t){return ns(ns(ns({},t),fs(t,f)),{},{isReady:Zc(l,f,ps(t,f))})}),O),e(Mu([Cu(O),Su(S)])),S.forEach(function(){var t=as(rs().mark((function t(r){var o,i,a;return rs().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,r.isReady;case 2:if(o=n(),i=o.callbacks.blocked,cn((function(t){return t===r||t.isReady===r.isReady}),i)){t.next=6;break}return t.abrupt("return");case 6:a=tc(r,c,s,f,l,r,e,n),e(Mu([Lu([r]),_u([a])]));case 8:case"end":return t.stop()}}),t)})));return function(r){return t.apply(this,arguments)}}()));case 12:case"end":return t.stop()}var E,j}),t)})));return function(r){return t.apply(this,arguments)}}(),inputs:["callbacks.prioritized","callbacks.completed"]},hs=ys,ds=function(){function t(t,r,e,n){this.valueFn=t,this.valueAcc=r,this.keyFn=e,this.xf=n,this.inputs={}}return t.prototype["@@transducer/init"]=br,t.prototype["@@transducer/result"]=function(t){var r;for(r in this.inputs)if(Et(r,this.inputs)&&(t=this.xf["@@transducer/step"](t,this.inputs[r]))["@@transducer/reduced"]){t=t["@@transducer/value"];break}return this.inputs=null,this.xf["@@transducer/result"](t)},t.prototype["@@transducer/step"]=function(t,r){var e=this.keyFn(r);return this.inputs[e]=this.inputs[e]||[e,ai(this.valueAcc,!1)],this.inputs[e][1]=this.valueFn(this.inputs[e][1],r),t},t}();function vs(t,r,e){return function(n){return new ds(t,r,e,n)}}var bs=wt(tr("groupBy",kr(4,[],dr([],vs,(function(t,r,e,n){var o=Hr((function(n,o){var i=e(o),a=t(Et(i,n)?n[i]:ai(r,!1),o);return a&&a["@@transducer/reduced"]?re(n):(n[i]=a,n)}));return Fr(o,{},n)})))((function(t,r){return t.push(r),t}),[]))),ms=wt((function(t,r){return ze({},r,t)}));function gs(t){return gs="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},gs(t)}function ws(){ws=function(){return r};var t,r={},e=Object.prototype,n=e.hasOwnProperty,o=Object.defineProperty||function(t,r,e){t[r]=e.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function s(t,r,e){return Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}),t[r]}try{s({},"")}catch(t){s=function(t,r,e){return t[r]=e}}function l(t,r,e,n){var i=r&&r.prototype instanceof b?r:b,a=Object.create(i.prototype),u=new T(n||[]);return o(a,"_invoke",{value:x(t,e,u)}),a}function f(t,r,e){try{return{type:"normal",arg:t.call(r,e)}}catch(t){return{type:"throw",arg:t}}}r.wrap=l;var p="suspendedStart",y="suspendedYield",h="executing",d="completed",v={};function b(){}function m(){}function g(){}var w={};s(w,a,(function(){return this}));var O=Object.getPrototypeOf,S=O&&O(O(L([])));S&&S!==e&&n.call(S,a)&&(w=S);var E=g.prototype=b.prototype=Object.create(w);function j(t){["next","throw","return"].forEach((function(r){s(t,r,(function(t){return this._invoke(r,t)}))}))}function _(t,r){function e(o,i,a,u){var c=f(t[o],t,i);if("throw"!==c.type){var s=c.arg,l=s.value;return l&&"object"==gs(l)&&n.call(l,"__await")?r.resolve(l.__await).then((function(t){e("next",t,a,u)}),(function(t){e("throw",t,a,u)})):r.resolve(l).then((function(t){s.value=t,a(s)}),(function(t){return e("throw",t,a,u)}))}u(c.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new r((function(r,o){e(t,n,r,o)}))}return i=i?i.then(o,o):o()}})}function x(r,e,n){var o=p;return function(i,a){if(o===h)throw Error("Generator is already running");if(o===d){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var u=n.delegate;if(u){var c=P(u,n);if(c){if(c===v)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=d,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=h;var s=f(r,e,n);if("normal"===s.type){if(o=n.done?d:y,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=d,n.method="throw",n.arg=s.arg)}}}function P(r,e){var n=e.method,o=r.iterator[n];if(o===t)return e.delegate=null,"throw"===n&&r.iterator.return&&(e.method="return",e.arg=t,P(r,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=f(o,r.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,v;var a=i.arg;return a?a.done?(e[r.resultName]=a.value,e.next=r.nextLoc,"return"!==e.method&&(e.method="next",e.arg=t),e.delegate=null,v):a:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,v)}function A(t){var r={tryLoc:t[0]};1 in t&&(r.catchLoc=t[1]),2 in t&&(r.finallyLoc=t[2],r.afterLoc=t[3]),this.tryEntries.push(r)}function k(t){var r=t.completion||{};r.type="normal",delete r.arg,t.completion=r}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(A,this),this.reset(!0)}function L(r){if(r||""===r){var e=r[a];if(e)return e.call(r);if("function"==typeof r.next)return r;if(!isNaN(r.length)){var o=-1,i=function e(){for(;++o<r.length;)if(n.call(r,o))return e.value=r[o],e.done=!1,e;return e.value=t,e.done=!0,e};return i.next=i}}throw new TypeError(gs(r)+" is not iterable")}return m.prototype=g,o(E,"constructor",{value:g,configurable:!0}),o(g,"constructor",{value:m,configurable:!0}),m.displayName=s(g,c,"GeneratorFunction"),r.isGeneratorFunction=function(t){var r="function"==typeof t&&t.constructor;return!!r&&(r===m||"GeneratorFunction"===(r.displayName||r.name))},r.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,g):(t.__proto__=g,s(t,c,"GeneratorFunction")),t.prototype=Object.create(E),t},r.awrap=function(t){return{__await:t}},j(_.prototype),s(_.prototype,u,(function(){return this})),r.AsyncIterator=_,r.async=function(t,e,n,o,i){void 0===i&&(i=Promise);var a=new _(l(t,e,n,o),i);return r.isGeneratorFunction(e)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},j(E),s(E,c,"Generator"),s(E,a,(function(){return this})),s(E,"toString",(function(){return"[object Generator]"})),r.keys=function(t){var r=Object(t),e=[];for(var n in r)e.push(n);return e.reverse(),function t(){for(;e.length;){var n=e.pop();if(n in r)return t.value=n,t.done=!1,t}return t.done=!0,t}},r.values=L,T.prototype={constructor:T,reset:function(r){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(k),!r)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(r){if(this.done)throw r;var e=this;function o(n,o){return u.type="throw",u.arg=r,e.next=n,o&&(e.method="next",e.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(c&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,r){for(var e=this.tryEntries.length-1;e>=0;--e){var o=this.tryEntries[e];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=r&&r<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=r,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,r){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&r&&(this.next=r),v},finish:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e.finallyLoc===t)return this.complete(e.completion,e.afterLoc),k(e),v}},catch:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e.tryLoc===t){var n=e.completion;if("throw"===n.type){var o=n.arg;k(e)}return o}}throw Error("illegal catch attempt")},delegateYield:function(r,e,n){return this.delegate={iterator:L(r),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=t),v}},r}function Os(t,r,e,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void e(t)}u.done?r(c):Promise.resolve(c).then(n,o)}var Ss=function(){var t,r=(t=ws().mark((function t(r){var e,n;return ws().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=new Promise((function(t){return e=t})),setTimeout(e,r),t.abrupt("return",n);case 3:case"end":return t.stop()}}),t)})),function(){var r=this,e=arguments;return new Promise((function(n,o){var i=t.apply(r,e);function a(t){Os(i,n,o,a,u,"next",t)}function u(t){Os(i,n,o,a,u,"throw",t)}a(void 0)}))});return function(t){return r.apply(this,arguments)}}();function Es(t){return Es="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Es(t)}function js(){js=function(){return r};var t,r={},e=Object.prototype,n=e.hasOwnProperty,o=Object.defineProperty||function(t,r,e){t[r]=e.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function s(t,r,e){return Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}),t[r]}try{s({},"")}catch(t){s=function(t,r,e){return t[r]=e}}function l(t,r,e,n){var i=r&&r.prototype instanceof b?r:b,a=Object.create(i.prototype),u=new T(n||[]);return o(a,"_invoke",{value:x(t,e,u)}),a}function f(t,r,e){try{return{type:"normal",arg:t.call(r,e)}}catch(t){return{type:"throw",arg:t}}}r.wrap=l;var p="suspendedStart",y="suspendedYield",h="executing",d="completed",v={};function b(){}function m(){}function g(){}var w={};s(w,a,(function(){return this}));var O=Object.getPrototypeOf,S=O&&O(O(L([])));S&&S!==e&&n.call(S,a)&&(w=S);var E=g.prototype=b.prototype=Object.create(w);function j(t){["next","throw","return"].forEach((function(r){s(t,r,(function(t){return this._invoke(r,t)}))}))}function _(t,r){function e(o,i,a,u){var c=f(t[o],t,i);if("throw"!==c.type){var s=c.arg,l=s.value;return l&&"object"==Es(l)&&n.call(l,"__await")?r.resolve(l.__await).then((function(t){e("next",t,a,u)}),(function(t){e("throw",t,a,u)})):r.resolve(l).then((function(t){s.value=t,a(s)}),(function(t){return e("throw",t,a,u)}))}u(c.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new r((function(r,o){e(t,n,r,o)}))}return i=i?i.then(o,o):o()}})}function x(r,e,n){var o=p;return function(i,a){if(o===h)throw Error("Generator is already running");if(o===d){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var u=n.delegate;if(u){var c=P(u,n);if(c){if(c===v)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=d,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=h;var s=f(r,e,n);if("normal"===s.type){if(o=n.done?d:y,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=d,n.method="throw",n.arg=s.arg)}}}function P(r,e){var n=e.method,o=r.iterator[n];if(o===t)return e.delegate=null,"throw"===n&&r.iterator.return&&(e.method="return",e.arg=t,P(r,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=f(o,r.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,v;var a=i.arg;return a?a.done?(e[r.resultName]=a.value,e.next=r.nextLoc,"return"!==e.method&&(e.method="next",e.arg=t),e.delegate=null,v):a:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,v)}function A(t){var r={tryLoc:t[0]};1 in t&&(r.catchLoc=t[1]),2 in t&&(r.finallyLoc=t[2],r.afterLoc=t[3]),this.tryEntries.push(r)}function k(t){var r=t.completion||{};r.type="normal",delete r.arg,t.completion=r}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(A,this),this.reset(!0)}function L(r){if(r||""===r){var e=r[a];if(e)return e.call(r);if("function"==typeof r.next)return r;if(!isNaN(r.length)){var o=-1,i=function e(){for(;++o<r.length;)if(n.call(r,o))return e.value=r[o],e.done=!1,e;return e.value=t,e.done=!0,e};return i.next=i}}throw new TypeError(Es(r)+" is not iterable")}return m.prototype=g,o(E,"constructor",{value:g,configurable:!0}),o(g,"constructor",{value:m,configurable:!0}),m.displayName=s(g,c,"GeneratorFunction"),r.isGeneratorFunction=function(t){var r="function"==typeof t&&t.constructor;return!!r&&(r===m||"GeneratorFunction"===(r.displayName||r.name))},r.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,g):(t.__proto__=g,s(t,c,"GeneratorFunction")),t.prototype=Object.create(E),t},r.awrap=function(t){return{__await:t}},j(_.prototype),s(_.prototype,u,(function(){return this})),r.AsyncIterator=_,r.async=function(t,e,n,o,i){void 0===i&&(i=Promise);var a=new _(l(t,e,n,o),i);return r.isGeneratorFunction(e)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},j(E),s(E,c,"Generator"),s(E,a,(function(){return this})),s(E,"toString",(function(){return"[object Generator]"})),r.keys=function(t){var r=Object(t),e=[];for(var n in r)e.push(n);return e.reverse(),function t(){for(;e.length;){var n=e.pop();if(n in r)return t.value=n,t.done=!1,t}return t.done=!0,t}},r.values=L,T.prototype={constructor:T,reset:function(r){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(k),!r)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(r){if(this.done)throw r;var e=this;function o(n,o){return u.type="throw",u.arg=r,e.next=n,o&&(e.method="next",e.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(c&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,r){for(var e=this.tryEntries.length-1;e>=0;--e){var o=this.tryEntries[e];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=r&&r<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=r,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,r){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&r&&(this.next=r),v},finish:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e.finallyLoc===t)return this.complete(e.completion,e.afterLoc),k(e),v}},catch:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e.tryLoc===t){var n=e.completion;if("throw"===n.type){var o=n.arg;k(e)}return o}}throw Error("illegal catch attempt")},delegateYield:function(r,e,n){return this.delegate={iterator:L(r),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=t),v}},r}function _s(t,r){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);r&&(n=n.filter((function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable}))),e.push.apply(e,n)}return e}function xs(t){for(var r=1;r<arguments.length;r++){var e=null!=arguments[r]?arguments[r]:{};r%2?_s(Object(e),!0).forEach((function(r){Ps(t,r,e[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):_s(Object(e)).forEach((function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(e,r))}))}return t}function Ps(t,r,e){return(r=function(t){var r=function(t){if("object"!=Es(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var e=r.call(t,"string");if("object"!=Es(e))return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Es(r)?r:r+""}(r))in t?Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[r]=e,t}function As(t,r,e,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void e(t)}u.done?r(c):Promise.resolve(c).then(n,o)}var ks={observer:function(){var t,r=(t=js().mark((function t(r){var e,n,o,i,a,u,c,s,l,f,p,y,h,d,v,b,m,g,w,O,S,E,j,_,x,P,A,k,T,L,I,C,N,R,D,M,U,q,G,F,B,H,$,z,J,Y,W;return js().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e=r.dispatch,n=r.getState,t.next=3,Ss(0);case 3:if(o=n(),i=o.callbacks,a=o.callbacks,u=a.prioritized,c=a.blocked,s=a.executing,l=a.watched,f=a.stored,p=o.paths,y=o.graphs,h=n(),d=h.callbacks.requested,v=d.slice(0),b=Yc(i),m=Or((function(t){var r;return Ft(t.callback,null!==(r=t.predecessors)&&void 0!==r?r:[])}),d),d=ve(d,m),g=[],w=[],Ce(bs(Eo,d)).forEach((function(t){if(1===t.length)w.push(t[0]);else{var r=t.find((function(t){return t.initialCall}));r&&g.push(r);var e=t.filter((function(t){return t!==r}));1===e.length?w.push(e[0]):(g=te(g,e),w.push(ms({changedPropIds:$r(fr(Math.max),{},ce("changedPropIds",e)),executionGroup:Or((function(t){return Boolean(t)}),ce("executionGroup",e)).slice(-1)[0]},e.slice(-1)[0])))}})),O=_r(Cr((function(t){return t.slice(0,-1)}),Ce(bs(Eo,te(u,d=w))))),S=_r(Cr((function(t){return t.slice(0,-1)}),Ce(bs(Eo,te(c,d))))),E=_r(Cr((function(t){return t.slice(0,-1)}),Ce(bs(Eo,te(s,d))))),j=_r(Cr((function(t){return t.slice(0,-1)}),Ce(bs(Eo,te(l,d))))),_=xo(d,p),x=_.added,P=_.removed,A=xo(u,p),k=A.added,T=A.removed,L=xo(c,p),I=L.added,C=L.removed,N=xo(s,p),R=N.added,D=N.removed,M=xo(l,p),U=M.added,q=M.removed,d=te(ve(d,P),x),G=Oo(p,d,b,y),F=[],B=[],G.length||!d.length||d.length!==b.length){t.next=33;break}H=d.slice(0),$=js().mark((function t(){var r,e,n;return js().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:r=H[0],G.push(r),H=H.slice(1),H=Oo(p,H,G),e=ve(H,H),n=Or((function(t){return!t.predecessors||!Ft(r.callback,t.predecessors)}),e),F=te(F,n),B=te(B,n.map((function(t){var e;return xs(xs({},t),{},{predecessors:te(null!==(e=t.predecessors)&&void 0!==e?e:[],[r.callback])})})));case 8:case"end":return t.stop()}}),t)}));case 29:if(!H.length){t.next=33;break}return t.delegateYield($(),"t0",31);case 31:t.next=29;break;case 33:d=te(ve(d,F),B),z=bs((function(t){return t.executionGroup}),Or((function(t){return!Kt(t.executionGroup)}),f)),J=Or((function(t){if(!t.executionGroup||!z[t.executionGroup]||!z[t.executionGroup].length)return!1;var r=Cr(mo,_r(t.getInputs(p))),e=_r(Cr((function(t){return t.executionMeta.allProps}),z[t.executionGroup])),n=_r(Cr((function(t){return t.executionMeta.updatedProps}),z[t.executionGroup]));return ur(Ie(r,n))&&ur(ve(r,e))&&!ie(eo,t.callback.inputs)}),G),d=ve(d,J),G=ve(G,J),d=ve(d,G),Y=ve(d,v),W=ve(v,d),e(Mu([Y.length?Pu(Y):null,W.length?Nu(W):null,O.length?Cu(O):null,S.length?Lu(S):null,E.length?Iu(E):null,j.length?Du(j):null,T.length?Cu(T):null,k.length?xu(k):null,C.length?Lu(C):null,I.length?Su(I):null,D.length?Iu(D):null,R.length?_u(R):null,q.length?Du(q):null,U.length?ku(U):null,G.length?xu(G):null]));case 42:case"end":return t.stop()}}),t)})),function(){var r=this,e=arguments;return new Promise((function(n,o){var i=t.apply(r,e);function a(t){As(i,n,o,a,u,"next",t)}function u(t){As(i,n,o,a,u,"throw",t)}a(void 0)}))});return function(t){return r.apply(this,arguments)}}(),inputs:["callbacks.requested","callbacks.completed"]},Ts=ks;function Ls(t,r){return function(t){if(Array.isArray(t))return t}(t)||function(t,r){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var n,o,i,a,u=[],c=!0,s=!1;try{if(i=(e=e.call(t)).next,0===r){if(Object(e)!==e)return;c=!1}else for(;!(c=(n=i.call(e)).done)&&(u.push(n.value),u.length!==r);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(t,r)||function(t,r){if(t){if("string"==typeof t)return Is(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Is(t,r):void 0}}(t,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Is(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}var Cs={observer:function(t){var r=t.dispatch,e=t.getState,n=e().callbacks,o=Yc(n),i=e().callbacks.stored,a=Ls(pe((function(t){return Kt(t.executionGroup)}),i),2),u=a[0],c=a[1],s=bs((function(t){return t.executionGroup}),c),l=bs((function(t){return t.executionGroup}),Or((function(t){return!Kt(t.executionGroup)}),o)),f=$r((function(t,r){var e=Ls(r,2),n=e[0],o=e[1];return l[n]?t:te(t,o)}),[],ia(s));r(Mu([u.length?Ru(u):null,f.length?Ru(f):null]))},inputs:["callbacks.stored","callbacks.completed"]},Ns=Cs;function Rs(t){return Rs="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Rs(t)}function Ds(t,r){for(var e=0;e<r.length;e++){var n=r[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Us(n.key),n)}}function Ms(t,r,e){return(r=Us(r))in t?Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[r]=e,t}function Us(t){var r=function(t){if("object"!=Rs(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var e=r.call(t,"string");if("object"!=Rs(e))return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Rs(r)?r:r+""}var qs=function(){return t=function t(){var r=this;!function(t,r){if(!(t instanceof r))throw new TypeError("Cannot call a class as a function")}(this,t),Ms(this,"__store",void 0),Ms(this,"storeObserver",new ra),Ms(this,"setObservers",it((function(){var t=r.storeObserver.observe;t(oa),t(Kc),t(Ts),t(hs),t($c),t(Rc),t(Ns)}))),Ms(this,"createAppStore",(function(t,e){r.__store=ht(t,e),r.storeObserver.setStore(r.__store);var n=window.dash_stores=window.dash_stores||[];n.includes(r.__store)||n.push(r.__store),r.setObservers()})),Ms(this,"initializeStore",(function(t){if(r.__store&&!t)return r.__store;var e=function(){return function(t){return function(r,e){var n=r||{},o=n.history,i=n.config,a=n.hooks,u=r;return"RELOAD"===e.type?u={history:o,config:i,hooks:a}:"SET_CONFIG"===e.type&&(u={hooks:a}),t(u,e)}}((t=Wi(),function(r,e){var n=e.type,o=e.payload;if("ON_PROP_CHANGE"===n){var i=Vi(o,r,!0);i&&!ur(i.props)&&(r.history.present=i)}var a,u=t(r,e);if("ON_PROP_CHANGE"===n&&"response"!==o.source){var c=Vi(o,u);c&&!ur(c.props)&&(u.history={past:[].concat((a=u.history.past,function(t){if(Array.isArray(t))return zi(t)}(a)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(a)||function(t,r){if(t){if("string"==typeof t)return zi(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?zi(t,r):void 0}}(a)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),[r.history.present]),present:c,future:[]})}return u}));var t}();return r.createAppStore(e,vt(gt)),t||(window.store=r.__store),r.__store})),this.__store=this.initializeStore()},(r=[{key:"store",get:function(){return this.__store}}])&&Ds(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,r}();function Gs(t){return Gs="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Gs(t)}function Fs(t,r){for(var e=0;e<r.length;e++){var n=r[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Bs(n.key),n)}}function Bs(t){var r=function(t){if("object"!=Gs(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var e=r.call(t,"string");if("object"!=Gs(e))return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Gs(r)?r:r+""}function Hs(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Hs=function(){return!!t})()}function $s(t){return $s=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},$s(t)}function zs(t,r){return zs=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,r){return t.__proto__=r,t},zs(t,r)}var Js=function(t){function e(t){return function(t,r){if(!(t instanceof r))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,r,e){return r=$s(r),function(t,r){if(r&&("object"==Gs(r)||"function"==typeof r))return r;if(void 0!==r)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Hs()?Reflect.construct(r,e||[],$s(t).constructor):r.apply(t,e))}(this,e,[t])}return function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(r&&r.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),r&&zs(t,r)}(e,t),n=e,(o=[{key:"render",value:function(){return r().createElement("div",{id:"_dash-app-content"},this.props.children)}}])&&Fs(n.prototype,o),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,o}(t.Component);Js.propTypes={children:a().object};var Ys=Js,Ws=["String","Number","Null","Boolean"],Vs=function(t){return Ft(Ct(t),Ws)},Ks=wt((function(t,r){return hr((function(e,n){return e[n]=t(r[n],n,r),e}),{},It(r))})),Qs=Ks;function Xs(t){return Xs="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Xs(t)}function Zs(t,r){for(var e=0;e<r.length;e++){var n=r[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tl(n.key),n)}}function tl(t){var r=function(t){if("object"!=Xs(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var e=r.call(t,"string");if("object"!=Xs(e))return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Xs(r)?r:r+""}function rl(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(rl=function(){return!!t})()}function el(t){return el=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},el(t)}function nl(t,r){return nl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,r){return t.__proto__=r,t},nl(t,r)}var ol=function(t){function r(t){var e;return function(t,r){if(!(t instanceof r))throw new TypeError("Cannot call a class as a function")}(this,r),(e=function(t,r,e){return r=el(r),function(t,r){if(r&&("object"==Xs(r)||"function"==typeof r))return r;if(void 0!==r)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,rl()?Reflect.construct(r,e||[],el(t).constructor):r.apply(t,e))}(this,r,[t])).state={myID:t.componentId,oldChildren:null,hasError:!1},e}return function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(r&&r.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),r&&nl(t,r)}(r,t),e=r,n=[{key:"componentDidCatch",value:function(t,r){var e=this.props.dispatch;e(ba({myID:this.state.myID,type:"frontEnd",error:t,info:r})),e(Ca)}},{key:"componentDidUpdate",value:function(t,r){var e=t.children;this.state.hasError||e===r.oldChildren||e===this.props.children||this.setState({oldChildren:e})}},{key:"render",value:function(){var t=this.state,r=t.hasError,e=t.oldChildren;return r?e:this.props.children}}],o=[{key:"getDerivedStateFromError",value:function(t){return{hasError:!0}}}],n&&Zs(e.prototype,n),o&&Zs(e,o),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,n,o}(t.Component);ol.propTypes={children:a().object,componentId:a().string,error:a().object,dispatch:a().func};var il=ol;function al(t){return al="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},al(t)}function ul(t,r){return function(t){if(Array.isArray(t))return t}(t)||function(t,r){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var n,o,i,a,u=[],c=!0,s=!1;try{if(i=(e=e.call(t)).next,0===r){if(Object(e)!==e)return;c=!1}else for(;!(c=(n=i.call(e)).done)&&(u.push(n.value),u.length!==r);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(t,r)||function(t,r){if(t){if("string"==typeof t)return cl(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?cl(t,r):void 0}}(t,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function cl(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}var sl={},ll=function(t){return function(r){var e,n,o=$i(t,r),i=Hi(t);e="dashChildrenUpdate"in Wt({},[null==(n=o)?void 0:n.namespace,null==n?void 0:n.type],window)?function(t,r){var e,n=0,o={};return Object.entries(t.layoutHashes).forEach((function(t){var i,a,u,c=ul(t,2),s=c[0],l=c[1],f=function(t,r){var e=t.split(","),n=r.split(",");if(!n.every((function(t,r){return e[r]===t})))return[!1,[]];var o=e.slice(n.length);return[o.filter((function(t){return"props"===t})).length<2,o]}(s,r),p=ul(f,2),y=p[0],h=p[1];if(y){var d=Wt({},[s],sl);n+=Wt(0,["hash"],l),d!==l&&(s!==r?(Object.assign(o,(i={},a=h[1],u=!0,(a=function(t){var r=function(t){if("object"!=al(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var e=r.call(t,"string");if("object"!=al(e))return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==al(r)?r:r+""}(a))in i?Object.defineProperty(i,a,{value:u,enumerable:!0,configurable:!0,writable:!0}):i[a]=u,i)),e="components"):(Object.assign(o,Wt({},["changedProps"],l)),e=Wt({},["renderType"],l)),sl[s]=l)}})),{hash:n,changedProps:o,renderType:e}}(r,i):r.layoutHashes[i];var a=0,u={},c="";return e&&(a=e.hash,u=e.changedProps,c=e.renderType),[o,null==o?void 0:o.props,a,u,c]}};function fl(t,r){var e=ul(t,3),n=(e[0],e[1],e[2]),o=ul(r,3);return o[0],o[1],n===o[2]}function pl(t){return t.config}var yl=e(925),hl=e.n(yl);function dl(t){return dl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},dl(t)}function vl(t){var r=t.element,e=t.props,n=t.children,o=t.component;!function(t){if("Array"===Ct(t))throw new Error("The children property of a component is a list of lists, instead of just a list. This can sometimes be due to a trailing comma. Check the component that has the following contents and remove one of the levels of nesting: \n"+JSON.stringify(t,null,2));if("Object"===Ct(t)&&!(pn("namespace",t)&&pn("type",t)&&pn("props",t)))throw new Error("An object was provided as `children` instead of a component, string, or number (or list of those). Check the children property that looks something like:\n"+JSON.stringify(t,null,2))}(o);var i=function(t,r,e,n){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,i=[];for(var a in t)if(t.hasOwnProperty(a)){var u=void 0;try{"function"!=typeof t[a]?(u=Error((n||"React class")+": "+e+" type `"+a+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+dl(t[a])+"`.")).name="Invariant Violation":u=t[a](r,a,n,e,null,hl())}catch(t){u=t}if(!u||u instanceof Error||i.push((n||"React class")+": type specification of "+e+" `"+a+"` is invalid; the type checker function must return `null` or an `Error` but returned a "+dl(u)+". You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument)."),u instanceof Error){var c=o&&o()||"";i.push("Failed "+e+" type: "+u.message+c)}}return i.join("\n\n")}(r.propTypes,e,"component prop",r);return i&&function(t,r,e){var n,o=t.split("`");if(Ft("is marked as required",t)){var i=o[1];n="".concat(i," in ").concat(e),r.id&&(n+=' with ID "'.concat(r.id,'"')),n+=" is required but it was not provided."}else if(Ft("Bad object",t))n=t.split("supplied to ")[0]+"supplied to ".concat(e)+".\nBad"+t.split(".\nBad")[1];else{if(!Ft("Invalid ",t)||!Ft(" supplied to ",t))throw new Error(t);var a=o[1];if(n="Invalid argument `".concat(a,"` passed into ").concat(e),r.id&&(n+=' with ID "'.concat(r.id,'"')),n+=".",Ft(", expected ",t)){var u=t.split(", expected ")[1];n+="\nExpected ".concat(u)}if(Ft(" of type `",t)){var c=t.split(" of type `")[1].split("`")[0];n+="\nWas supplied type `".concat(c,"`.")}if(pn(a,r)){var s=JSON.stringify(r[a],null,2);s&&(Ft("\n",s)?n+="\nValue provided: \n".concat(s):n+="\nValue provided: ".concat(s))}}throw new Error(n)}(i,e,o.type),n}var bl=r().createContext({});function ml(e){var n=e.children,o=e.componentPath,i=(0,t.useMemo)((function(){return JSON.stringify(o)}),[o]),a=V(),u=(0,t.useCallback)((function(t){var r=t||{},e=r.extraPath,n=r.rawPath,u=r.filterFunc,c=[i];e?c=[JSON.stringify(te(o,e))]:n&&(c=[JSON.stringify(n)]);var s=Wt([],c,a.getState().loading);return u?s.filter(u).length>0:s.length>0}),[i]),c=(0,t.useCallback)((function(t){var r=t||{},e=r.filterFunc,n=r.extraPath,a=r.rawPath;return b((function(t){var r=[i];n?r=[JSON.stringify(te(o,n))]:a&&(r=[JSON.stringify(a)]);var u=Wt([],r,t.loading);return e?u.filter(e).length>0:u.length>0}))}),[i]),s=(0,t.useMemo)((function(){return{componentPath:o,isLoading:u,useLoading:c,useSelector:b,useStore:V,useDispatch:Z}}),[i]);return r().createElement(bl.Provider,{value:s},n)}function gl(t){return gl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},gl(t)}var wl=["componentPath","_dashprivate_error","_passedComponent","_newRender"],Ol=["_dash_error"];function Sl(t,r){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);r&&(n=n.filter((function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable}))),e.push.apply(e,n)}return e}function El(t,r,e){return(r=function(t){var r=function(t){if("object"!=gl(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var e=r.call(t,"string");if("object"!=gl(e))return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==gl(r)?r:r+""}(r))in t?Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[r]=e,t}function jl(t){return function(t){if(Array.isArray(t))return xl(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||_l(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _l(t,r){if(t){if("string"==typeof t)return xl(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?xl(t,r):void 0}}function xl(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function Pl(t,r){if(null==t)return{};var e,n,o=function(t,r){if(null==t)return{};var e={};for(var n in t)if({}.hasOwnProperty.call(t,n)){if(-1!==r.indexOf(n))continue;e[n]=t[n]}return e}(t,r);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)e=i[n],-1===r.indexOf(e)&&{}.propertyIsEnumerable.call(t,e)&&(o[e]=t[e])}return o}var Al=function e(o){var i,a,u=o.componentPath,c=o._dashprivate_error,s=o._passedComponent,l=o._newRender,f=Pl(o,wl),p=Z(),y=(0,t.useRef)({}),h=(0,t.useRef)(!1),d=(0,t.useRef)(u),v=null,m=null,g=null,w=b(pl),O=(i=b(ll(u),fl),a=5,function(t){if(Array.isArray(t))return t}(i)||function(t,r){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var n,o,i,a,u=[],c=!0,s=!1;try{if(i=(e=e.call(t)).next,0===r){if(Object(e)!==e)return;c=!1}else for(;!(c=(n=i.call(e)).done)&&(u.push(n.value),u.length!==r);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(i,a)||_l(i,a)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),S=O[0],E=O[1],j=O[2],_=O[3],x=O[4];v=S,m=E,g=j,(0,t.useMemo)((function(){l?(h.current=!0,(g=0)in y.current&&delete y.current[g]):h.current=!1,d.current=u}),[l]);var P,A=(0,t.useCallback)((function(t,n,o){var i;return Vs(v)?v:r().createElement(e,{key:null!=t&&null!==(i=t.props)&&void 0!==i&&i.id?zn(t.props.id):Hi(n),_dashprivate_error:c,componentPath:n,_passedComponent:t,_newRender:o})}),[]),k=(0,t.useCallback)((function(t,r,e){return Array.isArray(t)?t.map((function(t,n){return Bi(t)?A(t,te(u,["props"].concat(jl(r),[n])),e):t})):Bi(t)?A(t,te(u,["props"].concat(jl(r))),e):t}),[u]),T=function(t){for(var r=1;r<arguments.length;r++){var e=null!=arguments[r]?arguments[r]:{};r%2?Sl(Object(e),!0).forEach((function(r){El(t,r,e[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):Sl(Object(e)).forEach((function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(e,r))}))}return t}({setProps:function(t){var r=m.id,e=t._dash_error,o=Pl(t,Ol);p((function(i,a){var u=a(),c=u.graphs,s=$i(d.current,u);if(s){var l=s.props;if(l){var f=be((function(t,r){return!Mt(t,l[r])}),o);if(e&&i(ba({type:"frontEnd",error:e})),!ur(f)){var p=function(t,r,e){if(!(t&&e&&r.length))return[];if("string"==typeof t){var n=e.inputMap[t];return n?r.filter((function(t){return n[t]})):[]}var o=Object.keys(t).sort(),i=yr(o,t),a=o.join(","),u=e.inputPatterns[a];return u?r.filter((function(t){var r=u[t];return r&&r.some((function(t){return to(o,i,t.values)}))})):[]}(r,It(f),c);(0,n.unstable_batchedUpdates)((function(){!function(t,r,e){var n=jc(t),o=n.canPersist,i=n.id,a=n.props,u=n.element,c=n.persistence,s=n.persisted_props,l=n.persistence_type;o&&c&&er((function(t){var n=ic(t.split("."),2),o=n[0],s=n[1];if(void 0!==r[o]){var f=wc(l,e),p=Sc(u,o,s).extract,y=Ec(i,t,c),h=p(a[o]),d=p(r[o]);if(h!==d){f.hasItem(y)&&(h=f.getItem(y)[1]);var v=void 0===h?[d]:[d,h];f.setItem(y,v,e)}}}),s)}(v,t,i),p.length&&i(Ra({id:r,props:aa(p,f)})),i(_a({props:f,itempath:d.current,renderType:"internal"}))}))}}}}))}},f);"dashRenderType"in Wt({},[null==(P=v)?void 0:P.namespace,null==P?void 0:P.type],window)&&(T.dashRenderType=h.current?"parent":_?x:"parent");var L=null;return g in y.current&&!h.current&&(L=r().isValidElement(y.current[g])?y.current[g]:null),L||(L=function(){if(h.current&&(v=s,m=null==s?void 0:s.props),!v)return null;var t,e=Pn(v),n=function(t,r){for(var e=Wt([],["children_props",null==t?void 0:t.namespace,null==t?void 0:t.type],w),n=Je(Ri("children",r),T),o=function(){var t=e[i],r=0;(t.split(".")[0].replace("[]","").replace("{}","")in _||h.current||!g)&&(r={});var o=function(t,e){return Qs((function(t,n){return k(t,[].concat(jl(e),[n]),r)}),t)};if(t.includes(".")){var a,u,c=t.split(".");if(t.includes("[]")){var s,l=[],f=[],p=!1,y=!1;if(c.forEach((function(t){p?t.includes("{}")?(y=!0,f.push(t.replace("{}",""))):f.push(t):t.includes("[]")?(p=!0,t.includes("{}")?(y=!0,l.push(t.replace("{}","").replace("[]",""))):l.push(t.replace("[]",""))):t.includes("{}")?(y=!0,l.push(t.replace("{}",""))):l.push(t)})),void 0===(a=nr(l,n))||null===(s=a)||void 0===s||!s.length)return 0;if(!nr(f,a[0]))return 0;u=a.map((function(t,e){var n,i=te(l,te([e],f));return n=y?f.length?o(nr(f,t),i):o(t,i):k(nr(f,t),i,r),Xt(f,n,t)})),c=l}else if(t.includes("{}")){for(var d=[],v=[],b=!1,m=[],w=0;w<c.length;w++){var O=c[w];O.includes("{}")?(v=te(d,[O.replace("{}","")]),w<c.length-1&&(b=!0)):b?m.push(O):d.push(O)}var S=nr(v,n);void 0!==S&&(u=Qs((function(t,e){return k(b?nr(m,t):t,te(v,b?te([e],m):[e]),r)}),S),c=v)}else{if(void 0===(a=nr(c,n)))return 0;u=k(a,c,r)}n=Xt(c,u,n)}else if(t.includes("{}")){var E=t.replace("{}",""),j=t.includes("[]");j&&(E=E.replace("[]",""));var x=n[E];if(void 0!==x)if(j)for(var P=0;P<x.length;P++){var A=te([E],[P]);n=Xt(A,o(x[P],A),n)}else n=Zt(E,o(x,[E]),n)}else{var T=n[t];void 0!==T&&(n=Zt(t,k(T,[t],r),n))}},i=0;i<e.length;i++)o();return"Object"===Ct(n.id)&&(n.id=zn(n.id)),n}(v,m);return void 0!==m.children&&(t=k(m.children,["children"],!g||h.current||"children"in _?{}:0)),h.current=!1,w.props_check?r().createElement(vl,{element:e,props:n,component:v},Fi(e,n,T,t)):Fi(e,n,T,t)}(),y.current=El({},g,L)),v?r().createElement(il,{componentType:v.type,componentId:Ua(Object,m.id)?zn(m.id):m.id,error:c,dispatch:p},r().createElement(ml,{componentPath:u},r().isValidElement(L)?L:r().createElement("div",null))):r().createElement("div",null)};function kl(t){return kl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},kl(t)}function Tl(){Tl=function(){return r};var t,r={},e=Object.prototype,n=e.hasOwnProperty,o=Object.defineProperty||function(t,r,e){t[r]=e.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function s(t,r,e){return Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}),t[r]}try{s({},"")}catch(t){s=function(t,r,e){return t[r]=e}}function l(t,r,e,n){var i=r&&r.prototype instanceof b?r:b,a=Object.create(i.prototype),u=new T(n||[]);return o(a,"_invoke",{value:x(t,e,u)}),a}function f(t,r,e){try{return{type:"normal",arg:t.call(r,e)}}catch(t){return{type:"throw",arg:t}}}r.wrap=l;var p="suspendedStart",y="suspendedYield",h="executing",d="completed",v={};function b(){}function m(){}function g(){}var w={};s(w,a,(function(){return this}));var O=Object.getPrototypeOf,S=O&&O(O(L([])));S&&S!==e&&n.call(S,a)&&(w=S);var E=g.prototype=b.prototype=Object.create(w);function j(t){["next","throw","return"].forEach((function(r){s(t,r,(function(t){return this._invoke(r,t)}))}))}function _(t,r){function e(o,i,a,u){var c=f(t[o],t,i);if("throw"!==c.type){var s=c.arg,l=s.value;return l&&"object"==kl(l)&&n.call(l,"__await")?r.resolve(l.__await).then((function(t){e("next",t,a,u)}),(function(t){e("throw",t,a,u)})):r.resolve(l).then((function(t){s.value=t,a(s)}),(function(t){return e("throw",t,a,u)}))}u(c.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new r((function(r,o){e(t,n,r,o)}))}return i=i?i.then(o,o):o()}})}function x(r,e,n){var o=p;return function(i,a){if(o===h)throw Error("Generator is already running");if(o===d){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var u=n.delegate;if(u){var c=P(u,n);if(c){if(c===v)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=d,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=h;var s=f(r,e,n);if("normal"===s.type){if(o=n.done?d:y,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=d,n.method="throw",n.arg=s.arg)}}}function P(r,e){var n=e.method,o=r.iterator[n];if(o===t)return e.delegate=null,"throw"===n&&r.iterator.return&&(e.method="return",e.arg=t,P(r,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=f(o,r.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,v;var a=i.arg;return a?a.done?(e[r.resultName]=a.value,e.next=r.nextLoc,"return"!==e.method&&(e.method="next",e.arg=t),e.delegate=null,v):a:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,v)}function A(t){var r={tryLoc:t[0]};1 in t&&(r.catchLoc=t[1]),2 in t&&(r.finallyLoc=t[2],r.afterLoc=t[3]),this.tryEntries.push(r)}function k(t){var r=t.completion||{};r.type="normal",delete r.arg,t.completion=r}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(A,this),this.reset(!0)}function L(r){if(r||""===r){var e=r[a];if(e)return e.call(r);if("function"==typeof r.next)return r;if(!isNaN(r.length)){var o=-1,i=function e(){for(;++o<r.length;)if(n.call(r,o))return e.value=r[o],e.done=!1,e;return e.value=t,e.done=!0,e};return i.next=i}}throw new TypeError(kl(r)+" is not iterable")}return m.prototype=g,o(E,"constructor",{value:g,configurable:!0}),o(g,"constructor",{value:m,configurable:!0}),m.displayName=s(g,c,"GeneratorFunction"),r.isGeneratorFunction=function(t){var r="function"==typeof t&&t.constructor;return!!r&&(r===m||"GeneratorFunction"===(r.displayName||r.name))},r.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,g):(t.__proto__=g,s(t,c,"GeneratorFunction")),t.prototype=Object.create(E),t},r.awrap=function(t){return{__await:t}},j(_.prototype),s(_.prototype,u,(function(){return this})),r.AsyncIterator=_,r.async=function(t,e,n,o,i){void 0===i&&(i=Promise);var a=new _(l(t,e,n,o),i);return r.isGeneratorFunction(e)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},j(E),s(E,c,"Generator"),s(E,a,(function(){return this})),s(E,"toString",(function(){return"[object Generator]"})),r.keys=function(t){var r=Object(t),e=[];for(var n in r)e.push(n);return e.reverse(),function t(){for(;e.length;){var n=e.pop();if(n in r)return t.value=n,t.done=!1,t}return t.done=!0,t}},r.values=L,T.prototype={constructor:T,reset:function(r){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(k),!r)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(r){if(this.done)throw r;var e=this;function o(n,o){return u.type="throw",u.arg=r,e.next=n,o&&(e.method="next",e.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(c&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,r){for(var e=this.tryEntries.length-1;e>=0;--e){var o=this.tryEntries[e];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=r&&r<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=r,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,r){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&r&&(this.next=r),v},finish:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e.finallyLoc===t)return this.complete(e.completion,e.afterLoc),k(e),v}},catch:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e.tryLoc===t){var n=e.completion;if("throw"===n.type){var o=n.arg;k(e)}return o}}throw Error("illegal catch attempt")},delegateYield:function(r,e,n){return this.delegate={iterator:L(r),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=t),v}},r}function Ll(t,r,e,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void e(t)}u.done?r(c):Promise.resolve(c).then(n,o)}function Il(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}var Cl=function(e){var n,o,i=e.appLifecycle,a=e.config,u=e.dependenciesRequest,c=e.error,s=e.layoutRequest,l=e.layout,f=(n=(0,t.useState)(!1),o=2,function(t){if(Array.isArray(t))return t}(n)||function(t,r){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var n,o,i,a,u=[],c=!0,s=!1;try{if(i=(e=e.call(t)).next,0===r){if(Object(e)!==e)return;c=!1}else for(;!(c=(n=i.call(e)).done)&&(u.push(n.value),u.length!==r);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(n,o)||function(t,r){if(t){if("string"==typeof t)return Il(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Il(t,r):void 0}}(n,o)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),p=f[0],y=f[1],h=(0,t.useRef)(null);h.current||(h.current=new En);var d,v=(0,t.useRef)(!1);return(0,t.useEffect)(Nl.bind(null,e,h,y)),(0,t.useEffect)((function(){var t;v.current&&(t=Tl().mark((function t(){return Tl().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return v.current=!1,t.next=3,Ss(0);case 3:h.current.emit("rendered");case 4:case"end":return t.stop()}}),t)})),function(){var r=this,e=arguments;return new Promise((function(n,o){var i=t.apply(r,e);function a(t){Ll(i,n,o,a,u,"next",t)}function u(t){Ll(i,n,o,a,u,"throw",t)}a(void 0)}))})()})),(0,t.useEffect)((function(){a.serve_locally?window._dashPlotlyJSURL="".concat(a.requests_pathname_prefix,"_dash-component-suites/plotly/package_data/plotly.min.js"):window._dashPlotlyJSURL=a.plotlyjs_url}),[]),s.status&&!Ft(s.status,[yi,"loading"])?d=a.ui?r().createElement("div",{dangerouslySetInnerHTML:{__html:s.content}}):r().createElement("div",{className:"_dash-error"},"Error loading layout"):p||u.status&&!Ft(u.status,[yi,"loading"])?d=a.ui?r().createElement("div",{dangerouslySetInnerHTML:{__html:u.content}}):r().createElement("div",{className:"_dash-error"},"Error loading dependencies"):i===To("HYDRATED")?(v.current=!0,d=r().createElement(r().Fragment,null,Array.isArray(l)?l.map((function(t,e){return Vs(t)?t:r().createElement(Al,{_dashprivate_error:c,componentPath:[e],key:e})})):r().createElement(Al,{_dashprivate_error:c,componentPath:[]}))):d=r().createElement("div",{className:"_dash-loading"},"Loading..."),a&&!0===a.ui?r().createElement(Ys,null,d):d};function Nl(t,r,e){var o=t.appLifecycle,i=t.dependenciesRequest,a=t.dispatch,u=t.error,c=t.graphs,s=t.hooks,l=t.layout,f=t.layoutRequest;(0,n.unstable_batchedUpdates)((function(){if(ur(f))"function"==typeof s.layout_pre&&s.layout_pre(),a(tu("_dash-layout","GET","layoutRequest"));else if(f.status===yi&&ur(l)){"function"==typeof s.layout_post&&s.layout_post(f.content);var t=_c(f.content,a);a(ja(_n(t,[],null,r.current))),a(Ea(t))}if(ur(i)?a(tu("_dash-dependencies","GET","dependenciesRequest")):i.status===yi&&(ur(c)||c.reset)&&a(Oa(Xn(i.content,Aa(a)))),i.status===yi&&!ur(c)&&f.status===yi&&!ur(l)&&o===To("STARTED")){var n=!1;try{a((Aa(a),function(t,r){!function(t,r){var e,n,o=t.config,i=t.graphs,a=t.layout,u=t.paths,c=!o.suppress_callback_exceptions;c&&o.validation_layout?(e=o.validation_layout,n=_n(e,[],null,u.events)):(e=a,n=u);var s=i.outputMap,l=i.inputMap,f=i.outputPatterns,p=i.inputPatterns;function y(t){return"This ID was used in the callback(s) for Output(s):\n  "+t.map((function(t){return t.outputs.map(mo).join(", ")})).join("\n  ")}function h(t,e,n){r("ID not found in layout",["Attempting to connect a callback ".concat(e," item to component:"),'  "'.concat(zn(t),'"'),"but no components with that id exist in the layout.","","If you are assigning callbacks to components that are","generated by other callbacks (and therefore not in the","initial layout), you can suppress this exception by setting","`suppress_callback_exceptions=True`.",y(n)])}function d(t,n,o,i,a){var u=o.split("@")[0],c=nr(n,e),s=Pn(c);if(s&&s.propTypes&&!s.propTypes[u]){for(var l in s.propTypes){var f=l.length-1;if("*"===l.charAt(f)&&u.substr(0,f)===l.substr(0,f))return}var p=c.type,h=c.namespace;r("Invalid prop for this component",['Property "'.concat(u,'" was used with component ID:'),"  ".concat(JSON.stringify(t)),"in one of the ".concat(i," items of a callback."),"This ID is assigned to a ".concat(h,".").concat(p," component"),"in the layout, which does not support this property.",y(a)])}}function v(t,r,e,o){Po()(n)({id:t,property:r}).forEach((function(t){d(t.id,t.path,r,e,o)}))}var b={};function m(t){var r=t.state,e=t.output;if(!b[e]){b[e]=1;var o="State";r.forEach((function(r){var e=r.id,i=r.property;if("string"==typeof e){var a=xn(n,e);a?d(e,a,i,o,[t]):c&&h(e,o,[t])}else Ie([Mn,Un],Ce(e)).length||v(e,i,o,[t])}))}}function g(t,r,e){for(var o in t){var i=t[o],a=xn(n,o);if(a)for(var u in i){var s=i[u];d(o,a,u,r,s),e&&s.forEach(m)}else c&&h(o,r,_r(Ce(i)))}}function w(t,r,e){for(var n in t){var o=t[n],i=function(t){o[t].forEach((function(n){var o=n.keys,i=n.values,a=n.callbacks;v(ge(o,i),t,r,a),e&&a.forEach(m)}))};for(var a in o)i(a)}}g(s,"Output",!0),g(l,"Input"),w(f,"Output",!0),w(p,"Input")}(r(),Aa(t)),function(t,r){var e=r(),n=e.graphs,o=e.paths,i=e.layout;try{n.MultiGraph.overallOrder()}catch(r){t(ba({type:"backEnd",error:{message:"Circular Dependencies",html:r.toString()}}))}t(Pu(So(n,o,i,{outputsOnly:!0})))}(t,r),t(ma(To("HYDRATED")))}))}catch(t){u.frontEnd.length||u.backEnd.length||a(ba({type:"backEnd",error:t})),n=!0}finally{e(n)}}}))}Cl.propTypes={appLifecycle:a().oneOf([To("STARTED"),To("HYDRATED"),To("DESTROYED")]),dispatch:a().func,dependenciesRequest:a().object,graphs:a().object,hooks:a().object,layoutRequest:a().object,layout:a().any,history:a().any,error:a().object,config:a().object};var Rl=J((function(t){return{appLifecycle:t.appLifecycle,dependenciesRequest:t.dependenciesRequest,hooks:t.hooks,layoutRequest:t.layoutRequest,layout:t.layout,graphs:t.graphs,history:t.history,error:t.error,config:t.config}}),(function(t){return{dispatch:t}}))(Cl);function Dl(t){return t.isLoading?r().createElement("div",{className:"_dash-loading-callback"}):null}Dl.propTypes={isLoading:a().bool.isRequired};var Ml=J((function(t){return{isLoading:t.isLoading}}))(Dl),Ul=e(72),ql=e.n(Ul),Gl=e(825),Fl=e.n(Gl),Bl=e(659),Hl=e.n(Bl),$l=e(56),zl=e.n($l),Jl=e(540),Yl=e.n(Jl),Wl=e(113),Vl=e.n(Wl),Kl=e(217),Ql={};function Xl(t){var e=t.dispatch,n=t.history,o=r().createElement("span",{key:"undoLink",className:"_dash-undo-redo-link",onClick:function(){return e(Ia)}},r().createElement("div",{className:"_dash-icon-undo"},"↺"),r().createElement("div",{className:"_dash-undo-redo-label"},"undo")),i=r().createElement("span",{key:"redoLink",className:"_dash-undo-redo-link",onClick:function(){return e(La)}},r().createElement("div",{className:"_dash-icon-redo"},"↻"),r().createElement("div",{className:"_dash-undo-redo-label"},"redo"));return r().createElement("div",{className:"_dash-undo-redo"},r().createElement("div",null,n.past.length>0?o:null,n.future.length>0?i:null))}Ql.styleTagTransform=Vl(),Ql.setAttributes=zl(),Ql.insert=Hl().bind(null,"head"),Ql.domAPI=Fl(),Ql.insertStyleElement=Yl(),ql()(Kl.A,Ql),Kl.A&&Kl.A.locals&&Kl.A.locals,Xl.propTypes={history:a().object,dispatch:a().func};var Zl=J((function(t){return{history:t.history}}),(function(t){return{dispatch:t}}))(Xl),tf=nt((function(t){return function(r,e){return t(r,e)?-1:t(e,r)?1:0}})),rf=wt((function(t,r){return t<r}));function ef(t){return ef="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ef(t)}function nf(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function of(t,r){for(var e=0;e<r.length;e++){var n=r[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,af(n.key),n)}}function af(t){var r=function(t){if("object"!=ef(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var e=r.call(t,"string");if("object"!=ef(e))return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==ef(r)?r:r+""}function uf(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(uf=function(){return!!t})()}function cf(t){return cf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},cf(t)}function sf(t,r){return sf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,r){return t.__proto__=r,t},sf(t,r)}var lf=function(t){function r(t){var e;if(function(t,r){if(!(t instanceof r))throw new TypeError("Cannot call a class as a function")}(this,r),e=function(t,r,e){return r=cf(r),function(t,r){if(r&&("object"==ef(r)||"function"==typeof r))return r;if(void 0!==r)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,uf()?Reflect.construct(r,e||[],cf(t).constructor):r.apply(t,e))}(this,r,[t]),t.config.hot_reload){var n=t.config.hot_reload,o=n.interval,i=n.max_retry;e.state={interval:o,disabled:!1,intervalId:null,packages:null,max_retry:i}}else e.state={disabled:!0};return e._retry=0,e._head=document.querySelector("head"),e.clearInterval=e.clearInterval.bind(e),e}return function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(r&&r.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),r&&sf(t,r)}(r,t),e=r,n=[{key:"clearInterval",value:function(){window.clearInterval(this.state.intervalId),this.setState({intervalId:null})}},{key:"componentDidUpdate",value:function(t,r){var e=this.state.reloadRequest,n=this.props.dispatch;if(e&&pn("reloadRequest",r))if(200===e.status&&nr(["content","reloadHash"],e)!==nr(["reloadRequest","content","reloadHash"],r))if(!e.content.hard&&Mt(e.content.packages.length,Wt([],["reloadRequest","content","packages"],r).length)&&Mt(Qc(tf(rf),e.content.packages),Qc(tf(rf),Wt([],["reloadRequest","content","packages"],r))))n({type:"RELOAD"});else{var o,i=!1,a=function(t,r){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=function(t,r){if(t){if("string"==typeof t)return nf(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?nf(t,r):void 0}}(t))||r&&t&&"number"==typeof t.length){e&&(t=e);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,u=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return a=t.done,t},e:function(t){u=!0,i=t},f:function(){try{a||null==e.return||e.return()}finally{if(u)throw i}}}}(e.content.files);try{for(a.s();!(o=a.n()).done;){var u=o.value;if(!u.is_css){i=!1;break}i=!0;for(var c=[],s=document.evaluate('//link[contains(@href, "'.concat(u.url,'")]'),this._head),l=s.iterateNext();l;)c.push(l),l=s.iterateNext();if(er((function(t){return t.setAttribute("disabled","disabled")}),c),u.modified>0){var f=document.createElement("link");f.href="".concat(u.url,"?m=").concat(u.modified),f.type="text/css",f.rel="stylesheet",this._head.appendChild(f)}}}catch(t){a.e(t)}finally{a.f()}i||window.location.reload()}else null!==this.state.intervalId&&500===e.status&&(this._retry>this.state.max_retry&&(this.clearInterval(),window.alert("Hot reloading is disabled after failing ".concat(this._retry," times. ")+"Please check your application for errors, then refresh the page.")),this._retry++)}},{key:"componentDidMount",value:function(){var t=this.props,r=t.dispatch,e=t.reloadRequest,n=this.state,o=n.disabled,i=n.interval;if(!o&&!this.state.intervalId){var a=window.setInterval((function(){"loading"!==e.status&&r(tu("_reload-hash","GET","reloadRequest"))}),i);this.setState({intervalId:a})}}},{key:"componentWillUnmount",value:function(){!this.state.disabled&&this.state.intervalId&&this.clearInterval()}},{key:"render",value:function(){return null}}],o=[{key:"getDerivedStateFromProps",value:function(t){return ur(t.reloadRequest)||"loading"===t.reloadRequest.status?null:{reloadRequest:t.reloadRequest}}}],n&&of(e.prototype,n),o&&of(e,o),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,n,o}(r().Component);lf.defaultProps={},lf.propTypes={id:a().string,config:a().object,reloadRequest:a().object,dispatch:a().func,interval:a().number};var ff=J((function(t){return{config:t.config,reloadRequest:t.reloadRequest}}),(function(t){return{dispatch:t}}))(lf),pf=wt((function(t,r){var e={};return tt(r.length,(function(){var n=t.apply(this,arguments);return Et(n,e)||(e[n]=r.apply(this,arguments)),e[n]}))})),yf=pf;function hf(t){return hf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},hf(t)}function df(t,r){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);r&&(n=n.filter((function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable}))),e.push.apply(e,n)}return e}function vf(t){for(var r=1;r<arguments.length;r++){var e=null!=arguments[r]?arguments[r]:{};r%2?df(Object(e),!0).forEach((function(r){bf(t,r,e[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):df(Object(e)).forEach((function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(e,r))}))}return t}function bf(t,r,e){return(r=gf(r))in t?Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[r]=e,t}function mf(t,r){for(var e=0;e<r.length;e++){var n=r[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,gf(n.key),n)}}function gf(t){var r=function(t){if("object"!=hf(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var e=r.call(t,"string");if("object"!=hf(e))return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==hf(r)?r:r+""}function wf(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(wf=function(){return!!t})()}function Of(t){return Of=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Of(t)}function Sf(t,r){return Sf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,r){return t.__proto__=r,t},Sf(t,r)}var Ef=function(t){function e(t){var r;if(function(t,r){if(!(t instanceof r))throw new TypeError("Cannot call a class as a function")}(this,e),r=function(t,r,e){return r=Of(r),function(t,r){if(r&&("object"==hf(r)||"function"==typeof r))return r;if(void 0!==r)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,wf()?Reflect.construct(r,e||[],Of(t).constructor):r.apply(t,e))}(this,e,[t]),null!==t.hooks.layout_pre||null!==t.hooks.layout_post||null!==t.hooks.request_pre||null!==t.hooks.request_post||null!==t.hooks.callback_resolved||null!==t.hooks.request_refresh_jwt){var n=t.hooks;n.request_refresh_jwt&&(n=vf(vf({},n),{},{request_refresh_jwt:yf(Pe,n.request_refresh_jwt)})),t.dispatch(Sa(n))}return r}return function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(r&&r.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),r&&Sf(t,r)}(e,t),n=e,o=[{key:"UNSAFE_componentWillMount",value:function(){var t,r=this.props.dispatch,e=(t=document.getElementById("_dash-config"),JSON.parse(null!=t&&t.textContent?null==t?void 0:t.textContent:"{}"));e.fetch={credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}},r(ga(e))}},{key:"render",value:function(){var t=this.props.config;if("Null"===Ct(t))return r().createElement("div",{className:"_dash-loading"},"Loading...");var e=t.show_undo_redo;return r().createElement(r().Fragment,null,e?r().createElement(Zl,null):null,r().createElement(Rl,null),r().createElement(Ml,null),r().createElement(ff,null))}}],o&&mf(n.prototype,o),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,o}(r().Component);Ef.propTypes={hooks:a().object,dispatch:a().func,config:a().object};var jf=J((function(t){return{history:t.history,config:t.config}}),(function(t){return{dispatch:t}}))(Ef);function _f(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}var xf=function(e){var n,o,i=e.hooks,a=void 0===i?{layout_pre:null,layout_post:null,request_pre:null,request_post:null,callback_resolved:null,request_refresh_jwt:null}:i,u=(n=(0,t.useState)((function(){return new qs})),o=1,function(t){if(Array.isArray(t))return t}(n)||function(t,r){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var n,o,i,a,u=[],c=!0,s=!1;try{if(i=(e=e.call(t)).next,0===r){if(Object(e)!==e)return;c=!1}else for(;!(c=(n=i.call(e)).done)&&(u.push(n.value),u.length!==r);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(n,o)||function(t,r){if(t){if("string"==typeof t)return _f(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?_f(t,r):void 0}}(n,o)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())[0].store;return r().createElement(Y,{store:u},r().createElement(jf,{hooks:a}))};xf.propTypes={hooks:a().shape({layout_pre:a().func,layout_post:a().func,request_pre:a().func,request_post:a().func,callback_resolved:a().func,request_refresh_jwt:a().func})};var Pf=xf;function Af(){return window.dash_stores=window.dash_stores||[]}function kf(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function Tf(t){return Tf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Tf(t)}function Lf(t,r){for(var e=0;e<r.length;e++){var n=r[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Cf(n.key),n)}}function If(t,r,e){return r&&Lf(t.prototype,r),e&&Lf(t,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function Cf(t){var r=function(t){if("object"!=Tf(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var e=r.call(t,"string");if("object"!=Tf(e))return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Tf(r)?r:r+""}window.dash_component_api={ExternalWrapper:function(e){var o,i,a=e.component,u=e.componentPath,c=Z(),s=(o=(0,t.useState)(!1),i=2,function(t){if(Array.isArray(t))return t}(o)||function(t,r){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var n,o,i,a,u=[],c=!0,s=!1;try{if(i=(e=e.call(t)).next,0===r){if(Object(e)!==e)return;c=!1}else for(;!(c=(n=i.call(e)).done)&&(u.push(n.value),u.length!==r);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(o,i)||function(t,r){if(t){if("string"==typeof t)return kf(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?kf(t,r):void 0}}(o,i)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),l=s[0],f=s[1];return(0,t.useEffect)((function(){var t;return c((t={component:a,componentPath:u},function(r,e){var n=e().paths;r(xa(t)),r(ja(_n(t.component,t.componentPath,n)))})),f(!0),function(){c(Pa({componentPath:u}))}}),[]),(0,t.useEffect)((function(){(0,n.unstable_batchedUpdates)((function(){c(_a({itempath:u,props:a.props})),a.props.id&&c(Ra({id:a.props.id,props:a.props}))}))}),[a.props]),l?r().createElement(Al,{componentPath:u}):null},DashContext:bl,useDashContext:function(){var r=(0,t.useContext)(bl);return r||console.error("Dash Context was not found, component was rendered without a wrapper. Use `window.dash_component_api.ExternalWrapper` to make sure the component is properly connected."),r||{}},getLayout:function(t){for(var r=Af(),e=0;e<r.length;e++){var n,o=r[e].getState(),i=o.paths,a=o.layout;n=Array.isArray(t)?t:xn(i,t);var u=nr(n,a);if(void 0!==u)return u}},stringifyId:zn};var Nf=If((function t(e){!function(t,r){if(!(t instanceof r))throw new TypeError("Cannot call a class as a function")}(this,t);var n=document.getElementById("react-entry-point");o().createRoot?o().createRoot(n).render(r().createElement(Pf,{hooks:e})):o().render(r().createElement(Pf,{hooks:e}),n)})),Rf=/^([^\w]*)(javascript|vbscript)/im,Df=/&(tab|newline);/gi,Mf=/[\u0000-\u001F\u007F-\u009F\u2000-\u200D\uFEFF]/gim,Uf=/&#(\w+)(^\w|;)?/g,qf=window.dash_clientside=window.dash_clientside||{};qf.set_props=function(t,r){for(var e=Af(),n=0;n<e.length;n++){var o,i=e[n],a=i.dispatch,u=(0,i.getState)().paths;o=Array.isArray(t)?t:xn(u,t),a(_a({props:r,itempath:o,renderType:"clientsideApi"})),a(Ra({id:t,props:r}))}},qf.clean_url=void 0===qf.clean_url?function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"about:blank";if(""===t)return t;var e=t.replace(Df,"").replace(Mf,"").replace(Uf,(function(t,r){return String.fromCharCode(r)})).trim();return Rf.test(e)?r:t}:qf.clean_url,window.DashRenderer=Nf}(),window.dash_renderer={}}();