{"type": "object", "title": "Bash IDE configuration", "properties": {"bashIde.backgroundAnalysisMaxFiles": {"type": "number", "default": 500, "description": "Maximum number of files to analyze in the background. Set to 0 to disable background analysis.", "minimum": 0}, "bashIde.enableSourceErrorDiagnostics": {"type": "boolean", "default": false, "description": "Enable diagnostics for source errors. Ignored if includeAllWorkspaceSymbols is true."}, "bashIde.explainshellEndpoint": {"type": "string", "default": "", "description": "Configure explainshell server endpoint in order to get hover documentation on flags and options."}, "bashIde.globPattern": {"type": "string", "default": "**/*@(.sh|.inc|.bash|.command)", "description": "Glob pattern for finding and parsing shell script files in the workspace. Used by the background analysis features across files."}, "bashIde.includeAllWorkspaceSymbols": {"type": "boolean", "default": false, "description": "Controls how symbols (e.g. variables and functions) are included and used for completion and documentation. If false (default and recommended), then we only include symbols from sourced files (i.e. using non dynamic statements like 'source file.sh' or '. file.sh' or following ShellCheck directives). If true, then all symbols from the workspace are included."}, "bashIde.logLevel": {"type": "string", "default": "info", "enum": ["debug", "info", "warning", "error"], "description": "Controls the log level of the language server."}, "bashIde.shellcheckPath": {"type": "string", "default": "shellcheck", "description": "Controls the executable used for ShellCheck linting information. An empty string will disable linting."}, "bashIde.shellcheckArguments": {"type": "string", "default": "", "description": "Additional ShellCheck arguments. Note that we already add the following arguments: --shell, --format, --external-sources."}}}