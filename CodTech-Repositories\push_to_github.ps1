# 🚀 CodTech GitHub Repository Push Script
# This script helps you push all 4 repositories to GitHub

Write-Host "🚀 CodTech GitHub Repository Setup Script" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green

# Get GitHub username
$username = Read-Host "Enter your GitHub username"

if ([string]::IsNullOrWhiteSpace($username)) {
    Write-Host "❌ GitHub username is required!" -ForegroundColor Red
    exit 1
}

Write-Host "📋 Setting up repositories for user: $username" -ForegroundColor Yellow

# Repository configurations
$repositories = @(
    @{
        Name = "codtech-bigdata-taxi-analysis"
        Description = "NYC Yellow Taxi Big Data Analysis using PySpark - CodTech Internship Project"
        Folder = "codtech-bigdata-taxi-analysis"
    },
    @{
        Name = "codtech-interactive-dashboard"
        Description = "Interactive Sales Dashboard with Dash and Plotly - CodTech Internship Project"
        Folder = "codtech-interactive-dashboard"
    },
    @{
        Name = "codtech-sentiment-analysis"
        Description = "NLP Sentiment Analysis with Machine Learning - CodTech Internship Project"
        Folder = "codtech-sentiment-analysis"
    },
    @{
        Name = "codtech-churn-prediction"
        Description = "Telco Customer Churn Prediction ML Pipeline - CodTech Internship Project"
        Folder = "codtech-churn-prediction"
    }
)

Write-Host "`n📝 IMPORTANT INSTRUCTIONS:" -ForegroundColor Cyan
Write-Host "1. Make sure you have created these repositories on GitHub.com:" -ForegroundColor White
foreach ($repo in $repositories) {
    Write-Host "   - $($repo.Name)" -ForegroundColor Yellow
}
Write-Host "2. Set them as PUBLIC repositories for portfolio visibility" -ForegroundColor White
Write-Host "3. DO NOT initialize with README, .gitignore, or license" -ForegroundColor White
Write-Host "4. Have your GitHub credentials ready (Personal Access Token recommended)" -ForegroundColor White

$continue = Read-Host "`nHave you created all repositories on GitHub? (y/n)"
if ($continue -ne "y" -and $continue -ne "Y") {
    Write-Host "❌ Please create the repositories on GitHub first, then run this script again." -ForegroundColor Red
    exit 1
}

# Function to push repository
function Push-Repository {
    param(
        [string]$RepoName,
        [string]$FolderName,
        [string]$Username
    )
    
    Write-Host "`n🔄 Processing: $RepoName" -ForegroundColor Green
    
    if (Test-Path $FolderName) {
        Set-Location $FolderName
        
        # Add remote origin
        $remoteUrl = "https://github.com/$Username/$RepoName.git"
        Write-Host "   Adding remote: $remoteUrl" -ForegroundColor Yellow
        
        try {
            git remote add origin $remoteUrl 2>$null
            if ($LASTEXITCODE -ne 0) {
                Write-Host "   Remote already exists, updating..." -ForegroundColor Yellow
                git remote set-url origin $remoteUrl
            }
            
            # Rename branch to main and push
            git branch -M main
            Write-Host "   Pushing to GitHub..." -ForegroundColor Yellow
            git push -u origin main
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "   ✅ Successfully pushed $RepoName" -ForegroundColor Green
            } else {
                Write-Host "   ❌ Failed to push $RepoName" -ForegroundColor Red
            }
        }
        catch {
            Write-Host "   ❌ Error pushing $RepoName : $_" -ForegroundColor Red
        }
        
        Set-Location ..
    } else {
        Write-Host "   ❌ Folder $FolderName not found!" -ForegroundColor Red
    }
}

# Push all repositories
Write-Host "`n🚀 Starting GitHub push process..." -ForegroundColor Green

foreach ($repo in $repositories) {
    Push-Repository -RepoName $repo.Name -FolderName $repo.Folder -Username $username
}

Write-Host "`n🎉 GitHub push process completed!" -ForegroundColor Green
Write-Host "`n📋 Your repositories are now available at:" -ForegroundColor Cyan
foreach ($repo in $repositories) {
    Write-Host "   🔗 https://github.com/$username/$($repo.Name)" -ForegroundColor Yellow
}

Write-Host "`n📝 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Visit each repository on GitHub" -ForegroundColor White
Write-Host "2. Add repository topics/tags for better discoverability" -ForegroundColor White
Write-Host "3. Update repository descriptions if needed" -ForegroundColor White
Write-Host "4. Add these repositories to your portfolio/resume" -ForegroundColor White

Write-Host "`n✨ Your CodTech internship portfolio is now live on GitHub! ✨" -ForegroundColor Green
