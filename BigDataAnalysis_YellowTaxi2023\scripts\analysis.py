import os
from pyspark.sql import SparkSession
from pyspark.sql.functions import hour, col, count, avg
import matplotlib.pyplot as plt

def create_spark_session():
    """Create and configure Spark session"""
    return SparkSession.builder \
        .appName("YellowTaxiAnalysis") \
        .config("spark.sql.adaptive.enabled", "true") \
        .config("spark.sql.adaptive.coalescePartitions.enabled", "true") \
        .getOrCreate()

def load_and_clean_data(spark, data_path):
    """Load and clean the taxi data"""
    print("Loading data...")
    df = spark.read.parquet(data_path)
    print(f"Initial record count: {df.count()}")

    # Clean data
    df_clean = df.dropna(subset=["trip_distance", "fare_amount", "tpep_pickup_datetime"]) \
                .filter(col("fare_amount") > 0) \
                .filter(col("trip_distance") > 0) \
                .filter(col("passenger_count") > 0)

    print(f"Clean record count: {df_clean.count()}")
    return df_clean

def perform_analysis(df):
    """Perform comprehensive analysis"""
    print("Performing analysis...")

    # Add time-based columns
    df = df.withColumn("pickup_hour", hour(col("tpep_pickup_datetime")))

    # Analysis 1: Average fare by passenger count
    fare_by_passenger = df.groupBy("passenger_count") \
                         .agg(avg("fare_amount").alias("avg_fare"),
                              count("*").alias("trip_count")) \
                         .orderBy("passenger_count")

    # Analysis 2: Trips by hour
    trips_by_hour = df.groupBy("pickup_hour") \
                     .agg(count("*").alias("trip_count"),
                          avg("fare_amount").alias("avg_fare")) \
                     .orderBy("pickup_hour")

    # Analysis 3: Trip distance distribution
    distance_stats = df.select("trip_distance") \
                      .describe() \
                      .collect()

    return fare_by_passenger, trips_by_hour, distance_stats

def save_results(fare_by_passenger, trips_by_hour, output_dir):
    """Save analysis results"""
    os.makedirs(output_dir, exist_ok=True)

    # Convert to Pandas and save
    fare_df = fare_by_passenger.toPandas()
    trips_df = trips_by_hour.toPandas()

    fare_df.to_csv(f"{output_dir}/fare_by_passenger.csv", index=False)
    trips_df.to_csv(f"{output_dir}/trips_by_hour.csv", index=False)

    # Create visualizations
    create_visualizations(fare_df, trips_df, output_dir)

    print(f"Results saved to {output_dir}/")

def create_visualizations(fare_df, trips_df, output_dir):
    """Create and save visualizations"""
    plt.style.use('seaborn-v0_8')

    # Plot 1: Trips by hour
    plt.figure(figsize=(12, 6))
    plt.bar(trips_df['pickup_hour'], trips_df['trip_count'])
    plt.title('Number of Trips by Hour of Day')
    plt.xlabel('Hour of Day')
    plt.ylabel('Number of Trips')
    plt.xticks(range(0, 24))
    plt.grid(axis='y', alpha=0.3)
    plt.tight_layout()
    plt.savefig(f"{output_dir}/trips_by_hour.png", dpi=300, bbox_inches='tight')
    plt.close()

    # Plot 2: Average fare by passenger count
    plt.figure(figsize=(10, 6))
    plt.bar(fare_df['passenger_count'], fare_df['avg_fare'])
    plt.title('Average Fare by Passenger Count')
    plt.xlabel('Passenger Count')
    plt.ylabel('Average Fare ($)')
    plt.grid(axis='y', alpha=0.3)
    plt.tight_layout()
    plt.savefig(f"{output_dir}/fare_by_passenger.png", dpi=300, bbox_inches='tight')
    plt.close()

def main():
    """Main execution function"""
    spark = None
    try:
        # Initialize Spark
        spark = create_spark_session()

        # Set paths
        data_path = "data/yellow_taxi_trip_2023.parquet"
        output_dir = "output"

        # Check if data file exists
        if not os.path.exists(data_path):
            print(f"Error: Data file {data_path} not found!")
            return

        # Load and clean data
        df_clean = load_and_clean_data(spark, data_path)

        # Perform analysis
        fare_by_passenger, trips_by_hour, distance_stats = perform_analysis(df_clean)

        # Save results
        save_results(fare_by_passenger, trips_by_hour, output_dir)

        # Print summary statistics
        print("\n=== ANALYSIS SUMMARY ===")
        print("Fare by Passenger Count:")
        fare_by_passenger.show()

        print("\nTrips by Hour (sample):")
        trips_by_hour.show(5)

        print("\nAnalysis completed successfully!")

    except Exception as e:
        print(f"Error occurred: {e}")
        import traceback
        traceback.print_exc()

    finally:
        if spark:
            spark.stop()

if __name__ == "__main__":
    main()
