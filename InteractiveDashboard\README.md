# Interactive Sales Dashboard 📊

**COMPANY:** CODTECH IT SOLUTIONS
**NAME:** BIBI AMINA
**INTERN ID:** CT06DM754
**DOMAIN:** DATA ANALYTICS
**DURATION:** 6 WEEKS
**MENTOR:** NEELA SANTOSH

## 🎯 Project Overview

A comprehensive, interactive web dashboard built with Dash and Plotly that provides real-time visualization and analysis of superstore sales data. The dashboard enables users to explore sales trends, analyze profitability, and understand geographic distribution through dynamic filtering and interactive charts.

## ✨ Key Features

### 🔍 Advanced Filtering

- **Date Range Picker**: Select custom time periods for analysis
- **Category Filter**: Filter by product categories (Technology, Furniture, Office Supplies)
- **Segment Multi-select**: Analyze specific customer segments (Consumer, Corporate, Home Office)
- **Real-time Updates**: All visualizations update instantly based on filter selections

### 📈 Interactive Visualizations

- **Sales Trend Analysis**: Time series line chart showing sales patterns
- **Profitability Analysis**: Horizontal bar chart ranking sub-categories by profit
- **Geographic Distribution**: US choropleth map displaying sales by state
- **Regional Breakdown**: Pie chart showing sales distribution across regions

### 💾 Data Export

- **CSV Download**: Export filtered data with timestamp
- **Custom Filename**: Automatically generated with filter parameters

### 🎨 Modern UI/UX

- **Responsive Design**: Bootstrap-styled interface that works on all devices
- **Loading Indicators**: Smooth loading animations for better user experience
- **Professional Styling**: Clean, modern dashboard layout

## 🛠️ Technology Stack

- **Frontend Framework**: Dash (Python web framework)
- **Visualization**: Plotly (Interactive charts and graphs)
- **Data Processing**: Pandas (Data manipulation and analysis)
- **UI Components**: Dash Bootstrap Components
- **Styling**: Bootstrap CSS framework
- **Data Export**: Dash Extensions

## 📊 Dataset Information

### 📥 **Dataset Download Required**

⚠️ **Note**: Due to GitHub file size limitations, the dataset is not included in this repository.

**🔗 Download Instructions:**

1. **Primary Source**: [Superstore Sales Dataset](https://www.kaggle.com/datasets/vivek468/superstore-dataset-final)
2. **Alternative Sources**:
   - [Sample Superstore Data](https://community.tableau.com/s/question/0D54T00000CWeX8SAL/sample-superstore-sales-excelcsv)
   - [GitHub Sample Data](https://github.com/datasets/superstore-sales)

### 🔧 **Setup Instructions**

```bash
# Create data directory (if not exists)
mkdir data

# Download and place the dataset
# File should be: data/Sales_Data.csv

# Verify file exists before running dashboard
ls data/Sales_Data.csv
```

### 📋 **Dataset Details**

- **File**: `Sales_Data.csv`
- **Source**: Superstore sales dataset
- **Records**: ~9,000+ transactions
- **Time Period**: 2014-2017
- **Geographic Coverage**: United States

### Key Data Fields:

- **Order Information**: Order ID, Date, Ship Date
- **Product Details**: Category, Sub-Category, Product Name
- **Customer Data**: Customer ID, Segment, Region, State, City
- **Financial Metrics**: Sales, Profit, Discount, Quantity

## 🚀 Getting Started

### Prerequisites

- Python 3.7 or higher
- pip package manager
- Modern web browser

### Installation & Setup

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd InteractiveDashboard
   ```

2. **Create virtual environment** (recommended)

   ```bash
   python -m venv dashboard_env
   source dashboard_env/bin/activate  # On Windows: dashboard_env\Scripts\activate
   ```

3. **Install dependencies**

   ```bash
   pip install -r requirements.txt
   ```

4. **Verify data file**
   - Ensure `data/Sales_Data.csv` exists
   - The app will handle encoding issues automatically

### Running the Dashboard

1. **Start the application**

   ```bash
   python app.py
   ```

2. **Access the dashboard**
   - Open your browser
   - Navigate to `http://127.0.0.1:8050`
   - The dashboard will load automatically

## 📱 Dashboard Components

### 🎛️ Control Panel

- **Date Range Selector**: Interactive calendar picker
- **Category Dropdown**: Single-select product category filter
- **Segment Multi-select**: Multiple customer segment selection

### 📊 Key Performance Indicators (KPIs)

- **💰 Total Sales**: Real-time sales sum with currency formatting
- **📈 Total Profit**: Profit calculation with positive/negative indicators
- **🛒 Total Orders**: Unique order count

### 📈 Visualization Panels

#### 1. Sales Trend Chart

- **Type**: Interactive line chart
- **Purpose**: Identify seasonal patterns and growth trends
- **Features**: Hover tooltips, zoom, pan

#### 2. Profit Analysis Chart

- **Type**: Horizontal bar chart
- **Purpose**: Compare profitability across sub-categories
- **Features**: Sorted by profit value, color-coded

#### 3. Geographic Sales Map

- **Type**: US Choropleth map
- **Purpose**: Visualize sales distribution by state
- **Features**: Color intensity based on sales volume, hover details

#### 4. Regional Distribution Chart

- **Type**: Interactive pie chart
- **Purpose**: Show sales proportion by region
- **Features**: Percentage labels, hover information

## 📁 Project Structure

```
InteractiveDashboard/
├── app.py                 # Main application file
├── data/
│   └── Sales_Data.csv     # Superstore dataset
├── assets/                # Static assets directory
│   ├── custom.css         # Custom styling (optional)
│   └── favicon.ico        # Dashboard icon (optional)
├── requirements.txt       # Python dependencies
├── README.md             # Project documentation
└── screenshots/          # Dashboard screenshots (optional)
    ├── dashboard_main.png
    └── filters_demo.png
```

## 🔧 Configuration Options

### Customization

- **Color Schemes**: Modify chart colors in the app.py file
- **Layout**: Adjust Bootstrap grid system for different layouts
- **Filters**: Add new filter options by modifying callback functions

### Performance Optimization

- **Data Caching**: Implement caching for large datasets
- **Lazy Loading**: Add loading states for better UX
- **Memory Management**: Optimize DataFrame operations

## 📈 Usage Examples

### Scenario 1: Quarterly Analysis

1. Set date range to Q1 2017
2. Select "Technology" category
3. Observe sales trends and profit margins

### Scenario 2: Regional Comparison

1. Clear all filters
2. Compare regional pie chart
3. Use geographic map to identify top-performing states

### Scenario 3: Customer Segment Analysis

1. Select "Corporate" segment
2. Analyze profit by sub-category
3. Export filtered data for further analysis

## 📸 Dashboard Output

**Interactive Web Interface:**
This project generates a live, interactive web dashboard accessible at `http://127.0.0.1:8050` when running.

**Real-time Visualizations Include:**

- **Sales Trend Charts**: Interactive time series analysis with zoom and pan capabilities
- **Profitability Analysis**: Dynamic bar charts with hover details and filtering
- **Geographic Distribution**: Interactive US choropleth maps with state-level data
- **Regional Breakdown**: Interactive pie charts with drill-down functionality
- **KPI Metrics**: Real-time dashboard showing total sales, profit, and order counts

**Available Data:**

- `data/Sales_Data.csv`: Superstore sales dataset (9,000+ records)

**Note:** This is a live web application - no static images are generated. The dashboard provides interactive visualizations that update in real-time based on user selections.

## 🚀 Deployment Options

### Local Development

- Run with `python app.py`
- Access at `localhost:8050`

### Production Deployment

- **Heroku**: Use Procfile and requirements.txt
- **Docker**: Create Dockerfile for containerization
- **Cloud Platforms**: Deploy on AWS, GCP, or Azure

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Plotly Team**: For the excellent visualization library
- **Dash Community**: For comprehensive documentation and examples
- **Bootstrap Team**: For the responsive CSS framework
- **Sample Dataset**: Superstore data for demonstration purposes

---

### 📬 Contact

**Intern**: Bibi Amina
**Project**: CodTech Internship – Data Analytics
**Task**: Interactive Dashboard Development
